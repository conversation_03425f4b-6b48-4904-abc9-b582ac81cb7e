import A from"./CuUhnQhf.js";import{Skeleton as E}from"./BaodNYLQ.js";import I from"./C4nYFGHw.js";import{b as P,_ as T,a as _}from"./CiqmSqHP.js";import{_ as j,a as D}from"./D3J2c5MI.js";import{_ as F}from"./DAA1LH-g.js";import{_ as Y}from"./JNWsvxxg.js";import{i as z,Y as G,l as i,c as o,w as t,o as e,e as u,a as s,t as p,h as H,p as d,d as f,q as a,a0 as x,$ as g}from"./C3gyeM1K.js";const J={class:"px-4 py-4 flex justify-between items-center"},K={class:"text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl"},M=10,oe=z({__name:"best-sell",setup(O){const{data:y,error:c,status:b}=G("most-popular",{query:{limit:M}});c.value&&console.error("Error fetching best sell:",c.value);const h=i(()=>y.value),r=i(()=>b.value!=="success");return(m,Q)=>{const k=A,l=E,$=I,C=T,w=j,v=D,N=P,B=Y,S=F;return e(),o(S,{class:"col-span-3 flex flex-col max-md:col-span-3"},{default:t(()=>[u("div",J,[u("span",K,p(m.$t("home.best-sell-title")),1),s(k,{to:"/categories/smartphones",class:"text-primary-600 hidden max-sm:flex"},{default:t(()=>[H(p(m.$t("home.see-more")),1)]),_:1})]),s(B,{class:"grid md:grid-cols-4 xs:grid-cols-2 gap-4 px-4 place-items-center"},{default:t(()=>[s(N,{opts:{align:"start",slidesToScroll:"auto"},class:"w-full col-span-4"},{default:t(({canScrollNext:V,canScrollPrev:L})=>[s(C,null,{default:t(()=>[a(r)?(e(!0),f(g,{key:0},x(Array(4),(n,q)=>(e(),o(a(_),{key:`product-skeleton-${q}`,class:"flex flex-col rounded h-full border p-2 border-gray-200 max-w-1/4 max-w-72 me-6 w-full"},{default:t(()=>[s(l,{class:"w-full h-32 bg-gray-200 mb-2"}),s(l,{class:"w-4/5 h-5 bg-gray-200 mb-2"}),s(l,{class:"w-1/3 h-5 bg-gray-200 mb-2"})]),_:2},1024))),128)):(e(!0),f(g,{key:1},x(a(h),n=>(e(),o(a(_),{key:n.productId,class:"basis-4/10"},{default:t(()=>[(e(),o($,{key:`product-${n.productId}`,product:n,variant:"lg"},null,8,["product"]))]),_:2},1024))),128))]),_:1}),!a(r)&&L?(e(),o(w,{key:0})):d("",!0),!a(r)&&V?(e(),o(v,{key:1})):d("",!0)]),_:1})]),_:1})]),_:1})}}});export{oe as _};
