import{ax as b,ay as f}from"./C3gyeM1K.js";import{u as m}from"./BsYnu6hU.js";const t=Object.freeze(Object.assign(()=>{},{__mock__:!0})),I=t,O=t,_=t,y=t,C=t,R=t,d=t,k=t;function g(o){return(b().public.scripts||{})[o]}function x(o,a,p){const e=g(o),s=Object.assign(p||{},typeof e=="object"?e:{}),n=a(s),u=f(s.scriptInput,n.scriptInput,{key:o}),c=Object.assign((s==null?void 0:s.scriptOptions)||{},n.scriptOptions||{}),i=c.beforeInit;return c.beforeInit=()=>{var r;i==null||i(),(r=n.clientInit)==null||r.call(n)},m(u,c)}export{R as a,C as b,g as c,k as d,O as e,d as l,y as n,I as o,_ as s,x as u};
