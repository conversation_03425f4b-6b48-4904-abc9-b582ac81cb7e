import{Skeleton as X}from"./BaodNYLQ.js";import{_ as Y}from"./BoqtTPyX.js";import{_ as Z}from"./CiM6KWUS.js";import{i as G,r as g,X as H,l as _,d as a,a as i,w as m,$ as x,Y as J,o as l,e as o,c as k,q as n,p as T,h as D,t as r,a0 as E,g as Q,W as R,Z as U,a2 as ee,_ as te}from"./C3gyeM1K.js";import{_ as oe}from"./JNWsvxxg.js";import{_ as se}from"./ziqIZt9y.js";import{a as le}from"./C-PWkk0T.js";import"./jAU0Cazi.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";import"./BzSOFl6X.js";import"./DSkQD3L1.js";import"./BK9Yhb3N.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./ZoTeemxs.js";import"./CjR7N8Rt.js";import"./CvMKnfiC.js";import"./BRCXRF_f.js";import"./D3IGe8e3.js";import"./DYRsuAWe.js";import"./FUKBWQwh.js";import"./DwXv0R8y.js";import"./DDFqsyHi.js";import"./ytjl1pT4.js";import"./CY42fsWK.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./C00tNdvy.js";import"./BRIKaOVL.js";import"./C1DP8TeJ.js";import"./BYbD1TwY.js";import"./Ls81bkHL.js";import"./DLHC-ANp.js";const ne={class:"flex w-full justify-end"},re={class:"flex w-full flex-col gap-6"},ae={key:0,class:"flex flex-col w-full gap-6"},ie={key:2,class:"flex flex-col w-full h-full justify-center items-center gap-6"},ce={class:"font-bold whitespace-pre text-center"},me=["disabled","onClick"],de={class:"flex flex-col flex-grow gap-2 text-sm"},pe={key:0,class:"font-bold text-base"},fe={class:"flex w-full items-center gap-4"},ue={class:"w-24 text-gray-600"},_e={class:"text-gray-700"},xe={class:"flex w-full items-center gap-4"},we={class:"w-24 text-gray-600"},ye={class:"text-gray-700"},he={class:"flex w-full items-center gap-4"},ve={class:"w-24 text-gray-600"},ge={class:"text-gray-700",dir:"ltr"},ke=G({__name:"index",props:{order:{}},emits:["set:flow"],async setup(w,{emit:q}){var B;let d,$;const z=q,b=g(1),{data:y,error:C,status:h}=([d,$]=H(()=>J("/addresses",{watch:[b]})),d=await d,$(),d);C.value&&console.log("error on fetching address list",C.value);const c=g(!1),I=_(()=>{var t;return(t=y==null?void 0:y.value)==null?void 0:t.items}),A=_(()=>(h==null?void 0:h.value)!=="success"),p=g(((B=w.order)==null?void 0:B.addressId)||null),F=_(()=>{var t;return((t=w.order)==null?void 0:t.status)!=="draft"}),N=_(()=>{var t;return!((t=I.value)!=null&&t.length)}),K=async()=>{const{$api:t}=ee();t(`/orders/${w.order.orderId}/address`,{method:"POST",body:{addressId:p.value}}).then(()=>{z("set:flow",2)})};return(t,s)=>{const f=X,v=Y,M=Z,O=Q,P=oe,W=se;return l(),a(x,null,[i(P,{class:"flex-grow p-4 flex flex-col gap-4"},{default:m(()=>[o("div",ne,[n(A)?(l(),k(f,{key:0,class:"w-32 h-5"})):(l(),a(x,{key:1},[!n(F)&&!n(N)?(l(),k(v,{key:0,variant:"text",onClick:s[0]||(s[0]=e=>c.value=!0)},{default:m(()=>[D(r(t.$t("address.add-new-address")),1)]),_:1})):T("",!0)],64))]),o("div",re,[n(A)?(l(),a("div",ae,[(l(!0),a(x,null,E(Array(3),(e,u)=>(l(),a("div",{key:u,class:"flex w-full max-w-full border rounded-lg p-4 gap-2 flex-col"},[i(f,{class:"w-full h-8"}),i(f,{class:"w-full h-5"}),i(f,{class:"w-full h-5"})]))),128))])):n(c)?(l(),k(M,{key:1,address:n(le),"onFetch:address":s[1]||(s[1]=()=>{b.value+=1,c.value=!1}),"onClose:address":s[2]||(s[2]=e=>c.value=!1)},null,8,["address"])):n(N)?(l(),a("div",ie,[i(O,{name:"ui:empty-address",class:"w-64 h-64"}),o("span",ce,r(t.$t("checkout.empty-address")),1),i(v,{class:"px-6",onClick:s[3]||(s[3]=e=>c.value=!0)},{default:m(()=>[o("span",null,r(t.$t("address.add-new-address")),1)]),_:1})])):(l(!0),a(x,{key:3},E(n(I),e=>{var u,j,V,L,S;return l(),a("button",{key:e.addressId,disabled:n(F),class:R(["flex border rounded-lg gap-4 items-center p-4 cursor-pointer text-start",{active:e.addressId===n(p)}]),onClick:$e=>p.value=e.addressId},[s[5]||(s[5]=o("div",{class:"check flex rounded-full w-5 h-5 border p-0.5"},[o("div",{class:"child flex w-full h-full rounded-full"})],-1)),o("div",de,[e.buildingType?(l(),a("div",pe,r(t.$t(`form.${e.buildingType}`)),1)):T("",!0),o("div",fe,[o("span",ue,r(t.$t("form.receiver-name"))+": ",1),o("span",_e,r(e.recipientName),1)]),o("div",xe,[o("span",we,r(t.$t("form.address")),1),o("span",ye,r([(j=(u=e==null?void 0:e.city)==null?void 0:u.country)==null?void 0:j.name,(V=e==null?void 0:e.city)==null?void 0:V.name,e.fullAddress,e.district,e.street].filter(Boolean).join(", ")),1)]),o("div",he,[o("span",ve,r(t.$t("form.phone")),1),o("span",ge,r(["+",((L=e==null?void 0:e.phone)==null?void 0:L.code)+" ",(S=e==null?void 0:e.phone)==null?void 0:S.number].join("")),1)])])],10,me)}),128))])]),_:1}),i(W,{class:"gap-4 justify-end"},{default:m(()=>[i(v,{disabled:!n(p),class:"sm:min-w-24 xs:min-w-1/2",onClick:s[4]||(s[4]=U(()=>K(),["prevent"]))},{default:m(()=>[D(r(t.$t("form.next")),1)]),_:1},8,["disabled"])]),_:1})],64)}}}),rt=te(ke,[["__scopeId","data-v-4ca338f8"]]);export{rt as default};
