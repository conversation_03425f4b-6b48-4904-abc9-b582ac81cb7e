// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config'
import { createResolver } from '@nuxt/kit'

const { resolve } = createResolver(import.meta.url)

export default defineNuxtConfig({
	modules: [
		'@nuxtjs/tailwindcss',
		'@nuxt/eslint',
		'shadcn-nuxt',
		'@nuxtjs/i18n',
		'@pinia/nuxt',
		'@nuxt/image',
		'@vueuse/nuxt',
		'@nuxt/icon',
		'@nuxt/fonts',
		'@nuxt/scripts',
		'nuxt-multi-cache',
		'@sentry/nuxt/module',
		'@nuxtjs/critters',
	],

	plugins: ['~/plugins/api', '~/plugins/sentry'],
	// Set to false for capacitor, adjust for web if needed
	$env: {
		capacitor: {
			ssr: false,

			multiCache: {
				// api: {
				// 	enabled: false,
				// },
				route: {
					enabled: false,
				},
				component: {
					enabled: false,
				},
				data: {
					enabled: false,
				},
			},
		},

		lambda: {
			nitro: {
				preset: 'aws-lambda', // build for AWS Lambda
				serveStatic: true, // serve static assets from Lambda (will be cached by Cloudflare)
			},
			multiCache: {
				debug: true,
				api: {
					enabled: /* JSON.parse(process.env.ENABLE_CACHE || 'false') === */true,
					authorization: /* process.env.CACHE_TOKEN */'zoRxkweXVc42FRZkKwDxbmHdrbYrFEq9PtPqvr4LZUuatvgXbLYYCHsSMj0qvGJg',
					prefix: '/cache',
				},
				route: {
					enabled: /* JSON.parse(process.env.ENABLE_CACHE || 'false') === */ true,
				},
				component: {
					enabled: /* JSON.parse(process.env.ENABLE_CACHE || 'false') === */ true,
				},
				data: {
					enabled: /* JSON.parse(process.env.ENABLE_CACHE || 'false') === */true,
				},
			},
			sentry: {
				// enabled: !!process.env.SENTRY_ENVIRONMENT,
				enabled: true,
				debug: false,
				sourceMapsUploadOptions: {
					telemetry: false,
					org: 'action-ga',
					project: 'action-21',
					authToken: '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',
				},
			},
		},
	},

	components: {
		global: true,
		dirs: ['~/components'],
	},

	imports: {
		dirs: ['~/components', '~/composables/', '~/pages/', '~/assets/'],
		scan: true,
		autoImport: true,
	},

	devtools: { enabled: process.env.NODE_ENV === 'development' },

	app: {
		head: {
			charset: 'utf-8',
			viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
			title: 'ActionMobile',
			meta: [
				{ charset: 'utf-8' },
				{ name: 'viewport', content: 'width=device-width, initial-scale=1' },
				// { hid: 'description', name: 'description', content: '' },
				{ name: 'format-detection', content: 'telephone=no' },
				{
					name: 'google-site-verification',
					content: 'sy0qTAzAz7sRktGoDrq7EvAxYknViAK1jaHpOgWR1kc',
				},
				{
					name: 'facebook-domain-verification',
					content: '6nf09tyaviblgpt2gbtunp5ks5ra1q',
				},
				{
					name: 'facebook-domain-verification',
					content: 'eb4g0lit8r2s5q1ctl0np47eeds7ij',
				},
				{
					name: 'facebook-domain-verification',
					content: '5pupubnfmprsr67ouvz3h9sw602bwi',
				},
				{
					'http-equiv': 'Content-Security-Policy',
					'content': 'upgrade-insecure-requests',
				},
				{
					name: 'facebook-domain-verification',
					content: '9cr3f6sl18ft4xwvzq83vb0six57z0',
				},
				{
					name: 'og:type',
					content: 'website',
				},
				{
					name: 'og:image',
					content: '/images/logo.png',
				},
				{
					name: 'og-site-name',
					content: 'Action.jo',
				},
				{
					name: 'twitter:site',
					content: '@ActionWebsite',
				},
				{
					name: 'link',
					content: 'https://action.jo',
				},
				{
					name: 'twitter:image',
					content: '/images/logo.png',
				},
				{
					name: 'twitter:card',
					content: 'summary',
				},
			],
			link: [
				{
					rel: 'icon',
					type: 'image/x-icon',
					href: '/favicon.ico',
				},
			],
		},
	},

	css: ['~/assets/css/main.css'],
	runtimeConfig: {
		cacheToken: process.env.CACHE_TOKEN,
		public: {
			siteUrl: process.env.APP_URL || 'https://action.jo/',
			baseUrl: process.env.BASE_URL || 'https://stg.action.jo/api/v1/',
			appBaseUrl: process.env.APP_URL,
			cdnUrl: process.env.CDN_BASE_URL, // Add CDN URL to runtime config for debugging
			sentry: {
				dsn: process.env.SENTRY_DSN,
				environment: process.env.SENTRY_ENVIRONMENT,
				enabled: !!process.env.SENTRY_ENVIRONMENT,
			},
		},
	},

	alias: {
		'@': resolve(__dirname, './'),
	},

	routeRules: {
		'/ar/**': {
			redirect: {
				statusCode: 301,
				to: '/**',
			},
		},
	},

	sourcemap: {
		client: 'hidden',
	},
	features: {
		inlineStyles: false,
	},

	compatibilityDate: '2024-11-01',

	vite: {
		css: {
			preprocessorOptions: {
				scss: {
					api: 'modern-compiler',
				},
			},
		},
	},
	typescript: {
		strict: false,
		typeCheck: false,
		tsConfig: {
			exclude: [
				'../android',
				'../ios',
			],
		},
	},
	critters: {
		// Options passed directly to critters: https://github.com/GoogleChromeLabs/critters#critters-2
		config: {
			// Default: 'media'
			preload: 'swap',
		},
	},

	fonts: {
		families: [
			{
				name: 'Cairo',
				provider: 'google',
				weights: [200, 300, 400, 500, 700, 900],
				display: 'swap',
			},
		],
	},

	i18n: {
		defaultLocale: 'ar',
		langDir: 'locales/',
		locales: [
			{ code: 'ar', dir: 'rtl', file: 'ar.json', language: 'ar', name: 'العربية', iso: 'ar-jo' },
			{ code: 'en', dir: 'ltr', file: 'en.json', language: 'en', name: 'English', iso: 'en-jo' },
		],
		lazy: true,
		strategy: 'prefix_and_default',
		vueI18n: '~/i18n/vueI18n.ts',
		baseUrl: process.env.APP_URL || 'https://action.jo/',
		detectBrowserLanguage: {
			useCookie: true,
			cookieKey: 'i18n_redirected',
			alwaysRedirect: false,
		},
		bundle: {
			optimizeTranslationDirective: false,
		},
	},

	icon: {
		customCollections: [
			{
				prefix: 'ui',
				dir: './assets/svgs',
			},
		],
		collections: ['lucide'],
		clientBundle: {
			sizeLimitKb: 0,
		},
	},

	image: {
		provider: 'ipx', // default
		format: ['webp'],
		screens: {
			xs: 110,
			sm: 600,
			md: 992,
			lg: 1300,
			xl: 1700,
		},
		quality: 90,
		providers: {
			backend: {
				provider: 'bunny',
				options: {
					// baseURL: 'https://action-v2-backend.b-cdn.net', // handled by alias
				},
			},
		},
	},
	multiCache: {
		debug: process.env.ENABLE_DEBUG === 'true',
		api: {
			enabled: process.env.ENABLE_CACHE === 'true',
			authorization: process.env.CACHE_TOKEN,
			prefix: 'cache',
		},
		route: {
			enabled: process.env.ENABLE_CACHE === 'true',
		},
		data: {
			enabled: process.env.ENABLE_CACHE === 'true',
		},
	},
	// sentry: {
	// 	enabled: process.env.NODE_ENV === 'production',
	// 	debug: false,
	// 	sourceMapsUploadOptions: {
	// 		telemetry: false,
	// 		org: 'action-ga',
	// 		project: 'action-21',
	// 		authToken: '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',
	// 	},
	// },

	shadcn: {
		prefix: '',
		componentDir: './components/ui',
	},
})
