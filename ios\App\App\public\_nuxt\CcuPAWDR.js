import{_ as r}from"./BwX5ZKIs.js";import{i as t,c as m,o as p}from"./C3gyeM1K.js";import"./BoqtTPyX.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./DAA1LH-g.js";import"./JNWsvxxg.js";import"./ziqIZt9y.js";import"./BsHCAE3W.js";import"./C0H2RQFE.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";import"./C-PWkk0T.js";import"./C03emx4L.js";import"./BaodNYLQ.js";import"./CaU5TI22.js";import"./C1DP8TeJ.js";import"./BYbD1TwY.js";import"./C0-TGi1L.js";import"./CUNo_W7j.js";import"./BzSOFl6X.js";import"./ZoTeemxs.js";import"./CjR7N8Rt.js";import"./CvMKnfiC.js";import"./BRCXRF_f.js";import"./DYRsuAWe.js";import"./DwXv0R8y.js";import"./Bqx9KQ63.js";import"./C00tNdvy.js";import"./BRIKaOVL.js";import"./Ls81bkHL.js";import"./DLHC-ANp.js";import"./Ri7l79DY.js";import"./zZSnkz7W.js";const ro=t({__name:"address",setup(i){return(e,s)=>{const o=r;return p(),m(o)}}});export{ro as default};
