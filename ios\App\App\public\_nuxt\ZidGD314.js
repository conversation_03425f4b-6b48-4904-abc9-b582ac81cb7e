import{i as e,c as r,o as a,a1 as s,q as n,aw as p}from"./C3gyeM1K.js";const g=e({__name:"Son<PERSON>",props:{invert:{type:Boolean},theme:{},position:{},hotkey:{},richColors:{type:Boolean},expand:{type:Boolean},duration:{},gap:{},visibleToasts:{},closeButton:{type:Boolean},toastOptions:{},class:{},style:{},offset:{},dir:{},icons:{},containerAriaLabel:{},pauseWhenPageIsHidden:{type:Boolean},cn:{type:Function}},setup(o){const t=o;return(u,c)=>(a(),r(n(p),s({class:"toaster group"},t,{"toast-options":{classes:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}}}),null,16))}});export{g as Toaster};
