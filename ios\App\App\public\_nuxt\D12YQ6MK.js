import{_ as fe}from"./CRCPoDcF.js";import{u as ce}from"./BCJZ1_ur.js";import{i as M,c as K,o as E,w as V,s as B,a1 as O,q as e,T as W,r as $,l as x,aq as G,al as ve,p as pe,ac as ge,m as be,ar as we,a as R,j as he,k as ye,d as Se,e as I,t as X,K as xe}from"./C3gyeM1K.js";import{c as ne,u as A}from"./C2RgRyPS.js";import{P as Y}from"./ClItKS3j.js";import{c as oe}from"./BstbYvQo.js";import{a as Ce,k as De}from"./BBuxlZld.js";import{u as J}from"./Bqx9KQ63.js";import{u as Ve}from"./BRIKaOVL.js";import{u as Ee}from"./C1DP8TeJ.js";import{_ as Ke}from"./BYbD1TwY.js";import{u as Re}from"./DwXv0R8y.js";import{q as Me}from"./CRC5xmnh.js";function $e(i=[],o,n){const s=[...i];return s[n]=o,s.sort((f,c)=>f-c)}function ae(i,o,n){const c=100/(n-o)*(i-o);return oe(c,0,100)}function Be(i,o){return o>2?`Value ${i+1} of ${o}`:o===2?["Minimum","Maximum"][i]:void 0}function Pe(i,o){if(i.length===1)return 0;const n=i.map(f=>Math.abs(f-o)),s=Math.min(...n);return n.indexOf(s)}function Ae(i,o,n){const s=i/2,c=Q([0,50],[0,s]);return(s-c(o)*n)*n}function ke(i){return i.slice(0,-1).map((o,n)=>i[n+1]-o)}function Ie(i,o){if(o>0){const n=ke(i);return Math.min(...n)>=o}return!0}function Q(i,o){return n=>{if(i[0]===i[1]||o[0]===o[1])return o[0];const s=(o[1]-o[0])/(i[1]-i[0]);return o[0]+s*(n-i[0])}}function Fe(i){return(String(i).split(".")[1]||"").length}function ze(i,o){const n=10**o;return Math.round(i*n)/n}const le=["PageUp","PageDown"],se=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],re={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},[ie,ue]=ne(["SliderVertical","SliderHorizontal"]),de=M({__name:"SliderImpl",props:{asChild:{type:Boolean},as:{default:"span"}},emits:["slideStart","slideMove","slideEnd","homeKeyDown","endKeyDown","stepKeyDown"],setup(i,{emit:o}){const n=i,s=o,f=F();return(c,d)=>(E(),K(e(Y),O({"data-slider-impl":""},n,{onKeydown:d[0]||(d[0]=t=>{t.key==="Home"?(s("homeKeyDown",t),t.preventDefault()):t.key==="End"?(s("endKeyDown",t),t.preventDefault()):e(le).concat(e(se)).includes(t.key)&&(s("stepKeyDown",t),t.preventDefault())}),onPointerdown:d[1]||(d[1]=t=>{const v=t.target;v.setPointerCapture(t.pointerId),t.preventDefault(),e(f).thumbElements.value.includes(v)?v.focus():s("slideStart",t)}),onPointermove:d[2]||(d[2]=t=>{t.target.hasPointerCapture(t.pointerId)&&s("slideMove",t)}),onPointerup:d[3]||(d[3]=t=>{const v=t.target;v.hasPointerCapture(t.pointerId)&&(v.releasePointerCapture(t.pointerId),s("slideEnd",t))})}),{default:V(()=>[B(c.$slots,"default")]),_:3},16))}}),Te=M({__name:"SliderHorizontal",props:{dir:{},min:{},max:{},inverted:{type:Boolean}},emits:["slideEnd","slideStart","slideMove","homeKeyDown","endKeyDown","stepKeyDown"],setup(i,{emit:o}){const n=i,s=o,{max:f,min:c,dir:d,inverted:t}=W(n),{forwardRef:v,currentElement:p}=A(),w=F(),y=$(),h=$(),S=x(()=>(d==null?void 0:d.value)==="ltr"&&!t.value||(d==null?void 0:d.value)!=="ltr"&&t.value);function a(l,u){const r=h.value||p.value.getBoundingClientRect(),m=[...w.thumbElements.value][w.valueIndexToChangeRef.value],C=w.thumbAlignment.value==="contain"?m.clientWidth:0;!y.value&&!u&&w.thumbAlignment.value==="contain"&&(y.value=l.clientX-m.getBoundingClientRect().left);const k=[0,r.width-C],z=S.value?[c.value,f.value]:[f.value,c.value],T=Q(k,z);h.value=r;const L=u?l.clientX-r.left-C/2:l.clientX-r.left-(y.value??0);return T(L)}return ue({startEdge:S.value?"left":"right",endEdge:S.value?"right":"left",direction:S.value?1:-1,size:"width"}),(l,u)=>(E(),K(de,{ref:e(v),dir:e(d),"data-orientation":"horizontal",style:G({"--reka-slider-thumb-transform":!S.value&&e(w).thumbAlignment.value==="overflow"?"translateX(50%)":"translateX(-50%)"}),onSlideStart:u[0]||(u[0]=r=>{const m=a(r,!0);s("slideStart",m)}),onSlideMove:u[1]||(u[1]=r=>{const m=a(r);s("slideMove",m)}),onSlideEnd:u[2]||(u[2]=()=>{h.value=void 0,y.value=void 0,s("slideEnd")}),onStepKeyDown:u[3]||(u[3]=r=>{const m=S.value?"from-left":"from-right",C=e(re)[m].includes(r.key);s("stepKeyDown",r,C?-1:1)}),onEndKeyDown:u[4]||(u[4]=r=>s("endKeyDown",r)),onHomeKeyDown:u[5]||(u[5]=r=>s("homeKeyDown",r))},{default:V(()=>[B(l.$slots,"default")]),_:3},8,["dir","style"]))}}),He=M({__name:"SliderVertical",props:{min:{},max:{},inverted:{type:Boolean}},emits:["slideEnd","slideStart","slideMove","homeKeyDown","endKeyDown","stepKeyDown"],setup(i,{emit:o}){const n=i,s=o,{max:f,min:c,inverted:d}=W(n),t=F(),{forwardRef:v,currentElement:p}=A(),w=$(),y=$(),h=x(()=>!d.value);function S(a,l){const u=y.value||p.value.getBoundingClientRect(),r=[...t.thumbElements.value][t.valueIndexToChangeRef.value],m=t.thumbAlignment.value==="contain"?r.clientHeight:0;!w.value&&!l&&t.thumbAlignment.value==="contain"&&(w.value=a.clientY-r.getBoundingClientRect().top);const C=[0,u.height-m],k=h.value?[f.value,c.value]:[c.value,f.value],z=Q(C,k),T=l?a.clientY-u.top-m/2:a.clientY-u.top-(w.value??0);return y.value=u,z(T)}return ue({startEdge:h.value?"bottom":"top",endEdge:h.value?"top":"bottom",size:"height",direction:h.value?1:-1}),(a,l)=>(E(),K(de,{ref:e(v),"data-orientation":"vertical",style:G({"--reka-slider-thumb-transform":!h.value&&e(t).thumbAlignment.value==="overflow"?"translateY(-50%)":"translateY(50%)"}),onSlideStart:l[0]||(l[0]=u=>{const r=S(u,!0);s("slideStart",r)}),onSlideMove:l[1]||(l[1]=u=>{const r=S(u);s("slideMove",r)}),onSlideEnd:l[2]||(l[2]=()=>{y.value=void 0,w.value=void 0,s("slideEnd")}),onStepKeyDown:l[3]||(l[3]=u=>{const r=h.value?"from-bottom":"from-top",m=e(re)[r].includes(u.key);s("stepKeyDown",u,m?-1:1)}),onEndKeyDown:l[4]||(l[4]=u=>s("endKeyDown",u)),onHomeKeyDown:l[5]||(l[5]=u=>s("homeKeyDown",u))},{default:V(()=>[B(a.$slots,"default")]),_:3},8,["style"]))}}),[F,Ue]=ne("SliderRoot"),qe=M({inheritAttrs:!1,__name:"SliderRoot",props:{defaultValue:{default:()=>[0]},modelValue:{},disabled:{type:Boolean,default:!1},orientation:{default:"horizontal"},dir:{},inverted:{type:Boolean,default:!1},min:{default:0},max:{default:100},step:{default:1},minStepsBetweenThumbs:{default:0},thumbAlignment:{default:"contain"},asChild:{type:Boolean},as:{default:"span"},name:{},required:{type:Boolean}},emits:["update:modelValue","valueCommit"],setup(i,{emit:o}){const n=i,s=o,{min:f,max:c,step:d,minStepsBetweenThumbs:t,orientation:v,disabled:p,thumbAlignment:w,dir:y}=W(n),h=Ve(y),{forwardRef:S,currentElement:a}=A(),l=Ee(a),{CollectionSlot:u}=J({isProvider:!0}),r=Ce(n,"modelValue",s,{defaultValue:n.defaultValue,passive:n.modelValue===void 0}),m=x(()=>Array.isArray(r.value)?[...r.value]:[]),C=$(0),k=$(m.value);function z(b){const g=Pe(m.value,b);H(b,g)}function T(b){H(b,C.value)}function L(){const b=k.value[C.value];m.value[C.value]!==b&&s("valueCommit",ge(m.value))}function H(b,g,{commit:D}={commit:!1}){var U;const N=Fe(d.value),_=ze(Math.round((b-f.value)/d.value)*d.value+f.value,N),j=oe(_,f.value,c.value),P=$e(m.value,j,g);if(Ie(P,t.value*d.value)){C.value=P.indexOf(j);const q=String(P)!==String(r.value);q&&D&&s("valueCommit",P),q&&((U=Z.value[C.value])==null||U.focus(),r.value=P)}}const Z=$([]);return Ue({modelValue:r,currentModelValue:m,valueIndexToChangeRef:C,thumbElements:Z,orientation:v,min:f,max:c,disabled:p,thumbAlignment:w}),(b,g)=>(E(),K(e(u),null,{default:V(()=>[(E(),K(ve(e(v)==="horizontal"?Te:He),O(b.$attrs,{ref:e(S),"as-child":b.asChild,as:b.as,min:e(f),max:e(c),dir:e(h),inverted:b.inverted,"aria-disabled":e(p),"data-disabled":e(p)?"":void 0,onPointerdown:g[0]||(g[0]=()=>{e(p)||(k.value=m.value)}),onSlideStart:g[1]||(g[1]=D=>!e(p)&&z(D)),onSlideMove:g[2]||(g[2]=D=>!e(p)&&T(D)),onSlideEnd:g[3]||(g[3]=D=>!e(p)&&L()),onHomeKeyDown:g[4]||(g[4]=D=>!e(p)&&H(e(f),0,{commit:!0})),onEndKeyDown:g[5]||(g[5]=D=>!e(p)&&H(e(c),m.value.length-1,{commit:!0})),onStepKeyDown:g[6]||(g[6]=(D,N)=>{if(!e(p)){const P=e(le).includes(D.key)||D.shiftKey&&e(se).includes(D.key)?10:1,U=C.value,q=m.value[U],me=e(d)*P*N;H(q+me,U,{commit:!0})}})}),{default:V(()=>[B(b.$slots,"default",{modelValue:e(r)}),e(l)&&b.name?(E(),K(e(Ke),{key:0,type:"number",value:e(r),name:b.name,required:b.required,disabled:e(p),step:e(d)},null,8,["value","name","required","disabled","step"])):pe("",!0)]),_:3},16,["as-child","as","min","max","dir","inverted","aria-disabled","data-disabled"]))]),_:3}))}}),Oe=M({inheritAttrs:!1,__name:"SliderThumbImpl",props:{index:{},asChild:{type:Boolean},as:{}},setup(i){const o=i,n=F(),s=ie(),{forwardRef:f,currentElement:c}=A(),{CollectionItem:d}=J(),t=x(()=>{var a,l;return(l=(a=n.modelValue)==null?void 0:a.value)==null?void 0:l[o.index]}),v=x(()=>t.value===void 0?0:ae(t.value,n.min.value??0,n.max.value??100)),p=x(()=>{var a,l;return Be(o.index,((l=(a=n.modelValue)==null?void 0:a.value)==null?void 0:l.length)??0)}),w=Re(c),y=x(()=>w[s.size].value),h=x(()=>n.thumbAlignment.value==="overflow"||!y.value?0:Ae(y.value,v.value,s.direction)),S=De();return be(()=>{n.thumbElements.value.push(c.value)}),we(()=>{const a=n.thumbElements.value.findIndex(l=>l===c.value)??-1;n.thumbElements.value.splice(a,1)}),(a,l)=>(E(),K(e(d),null,{default:V(()=>[R(e(Y),O(a.$attrs,{ref:e(f),role:"slider",tabindex:e(n).disabled.value?void 0:0,"aria-label":a.$attrs["aria-label"]||p.value,"data-disabled":e(n).disabled.value?"":void 0,"data-orientation":e(n).orientation.value,"aria-valuenow":t.value,"aria-valuemin":e(n).min.value,"aria-valuemax":e(n).max.value,"aria-orientation":e(n).orientation.value,"as-child":a.asChild,as:a.as,style:{transform:"var(--reka-slider-thumb-transform)",position:"absolute",[e(s).startEdge]:`calc(${v.value}% + ${h.value}px)`,display:!e(S)&&t.value===void 0?"none":void 0},onFocus:l[0]||(l[0]=()=>{e(n).valueIndexToChangeRef.value=a.index})}),{default:V(()=>[B(a.$slots,"default")]),_:3},16,["tabindex","aria-label","data-disabled","data-orientation","aria-valuenow","aria-valuemin","aria-valuemax","aria-orientation","as-child","as","style"])]),_:3}))}}),ee=M({__name:"SliderThumb",props:{asChild:{type:Boolean},as:{default:"span"}},setup(i){const o=i,{getItems:n}=J(),{forwardRef:s,currentElement:f}=A(),c=x(()=>f.value?n(!0).findIndex(d=>d.ref===f.value):-1);return(d,t)=>(E(),K(Oe,O({ref:e(s)},o,{index:c.value}),{default:V(()=>[B(d.$slots,"default")]),_:3},16,["index"]))}}),Ye=M({__name:"SliderTrack",props:{asChild:{type:Boolean},as:{default:"span"}},setup(i){const o=F();return A(),(n,s)=>(E(),K(e(Y),{"as-child":n.asChild,as:n.as,"data-disabled":e(o).disabled.value?"":void 0,"data-orientation":e(o).orientation.value},{default:V(()=>[B(n.$slots,"default")]),_:3},8,["as-child","as","data-disabled","data-orientation"]))}}),te=M({__name:"SliderRange",props:{asChild:{type:Boolean},as:{default:"span"}},setup(i){const o=F(),n=ie();A();const s=x(()=>o.currentModelValue.value.map(d=>ae(d,o.min.value,o.max.value))),f=x(()=>o.currentModelValue.value.length>1?Math.min(...s.value):0),c=x(()=>100-Math.max(...s.value,0));return(d,t)=>(E(),K(e(Y),{"data-disabled":e(o).disabled.value?"":void 0,"data-orientation":e(o).orientation.value,"as-child":d.asChild,as:d.as,style:G({[e(n).startEdge]:`${f.value}%`,[e(n).endEdge]:`${c.value}%`})},{default:V(()=>[B(d.$slots,"default")]),_:3},8,["data-disabled","data-orientation","as-child","as","style"]))}}),Le={class:"flex flex-col w-full pb-6 gap-1 border-b border-gray-200"},Ne={class:"flex w-full mb-2 text-sm font-bold"},je={class:"flex w-full justify-between text-xs mb-1"},Xe={class:"flex justify-between gap-4 text-xs items-center"},rt=M({__name:"price-range",props:{price:{},update:{type:Function}},setup(i){const o=he(),{priceFormat:n}=ce(),{locale:s}=ye(),f=x(()=>s.value==="ar"?"rtl":"ltr"),c=x(()=>{var a,l,u,r,m;return Number(((u=(l=(a=o.query)==null?void 0:a.price)==null?void 0:l.split(":"))==null?void 0:u[0])||((m=(r=i.price)==null?void 0:r.config)==null?void 0:m.min)||0)}),d=x(()=>{var a,l,u,r,m;return Number(((u=(l=(a=o.query)==null?void 0:a.price)==null?void 0:l.split(":"))==null?void 0:u[1])||((m=(r=i.price)==null?void 0:r.config)==null?void 0:m.max)||0)}),t=$([c.value,d.value]),v=Me(()=>{var l,u,r,m;let a=null;(t.value[0]!==((u=(l=i.price)==null?void 0:l.config)==null?void 0:u.min)||t.value[1]!==((m=(r=i.price)==null?void 0:r.config)==null?void 0:m.max))&&(a=`${t.value[0]}:${t.value[1]}`),i.update(a)},500),p=a=>{t.value=a,v()},w=a=>a<t.value[1]?a:t.value[1]-5,y=a=>a>t.value[0]?a:t.value[0]+5,h=a=>{t.value[0]=a},S=a=>{t.value[1]=a};return(a,l)=>{const u=fe;return E(),Se("div",Le,[I("div",Ne,X(a.$t("filters.price-title")),1),I("div",je,[I("span",null,X(e(n)(a.price.config.min)),1),I("span",null,X(e(n)(a.price.config.max)),1)]),R(e(qe),{modelValue:e(t),"onUpdate:modelValue":[l[0]||(l[0]=r=>xe(t)?t.value=r:null),p],max:a.price.config.max,min:a.price.config.min,dir:e(f),step:1,direction:"horizontal-reverce",class:"relative flex items-center select-none touch-none w-full h-5","min-steps-between-thumbs":1},{default:V(()=>[R(e(Ye),{class:"bg-gray-300 relative grow rounded-full h-1 cursor-pointer"},{default:V(()=>[R(e(te),{class:"absolute bg-grass8 rounded-full h-full bg-primary-600"}),R(e(te),{class:"absolute bg-grass8 rounded-full h-full bg-primary-600"})]),_:1}),R(e(ee),{class:"block w-4 h-4 cursor-pointer bg-white rounded-full border border-primary-600 hover:bg-stone-50 shadow-sm focus:outline-none focus:shadow-[white] focus:shadow-grass9","aria-label":"Volume"}),R(e(ee),{class:"block w-4 h-4 cursor-pointer bg-white rounded-full border border-primary-600 hover:bg-stone-50 shadow-sm focus:outline-none focus:shadow-[white] focus:shadow-grass9","aria-label":"Volume"})]),_:1},8,["modelValue","max","min","dir"]),I("div",Xe,[R(u,{modelValue:e(t)[0],"onUpdate:modelValue":l[1]||(l[1]=r=>e(t)[0]=r),readonly:"",type:"number",min:"0",max:"100","format-value":w,"onUpdate:price":h},null,8,["modelValue"]),l[3]||(l[3]=I("span",null,"-",-1)),R(u,{modelValue:e(t)[1],"onUpdate:modelValue":l[2]||(l[2]=r=>e(t)[1]=r),readonly:"",type:"number",min:"0",max:"100","format-value":y,"onUpdate:price":S},null,8,["modelValue"])])])}}});export{rt as _};
