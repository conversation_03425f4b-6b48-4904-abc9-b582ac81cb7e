const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as g,k as y,l,d as p,e as a,c,q as i,t as f,$ as h,a0 as w,o as r,W as k,g as b,ag as N,ah as A}from"./C3gyeM1K.js";const B=N(()=>A(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(n=>n.default||n)),I={class:"flex flex-col gap-2 border-b border-gray-200"},$={class:"flex gap-2"},z={class:"flex rounded-full overflow-hidden bg-primary-200 w-8 h-8 justify-center items-center"},C={class:"flex flex-col"},E={class:"text-xs text-gray-500 font-medium"},L={class:"flex gap-1 items-center"},j={class:"flex text-sm text-gray-800 font-medium py-2 pb-4 min-h-10"},V=g({__name:"user-rate",props:{review:{}},setup(n){const{t:v}=y(),o=l(()=>{var e;return(e=n.review)==null?void 0:e.user}),u=l(()=>{var e,t,s;return(s=(t=(e=o.value)==null?void 0:e.media)==null?void 0:t.avatar)==null?void 0:s.preview}),x=l(()=>{var e,t,s;return(e=o.value)!=null&&e.firstName?`${(t=o.value)==null?void 0:t.firstName} ${((s=o.value)==null?void 0:s.lastName)||""}`:v("text.unknown")});return(e,t)=>{const s=B,_=b;return r(),p("div",I,[a("div",$,[a("div",z,[i(u)?(r(),c(s,{key:0,src:i(u),class:"w-full h-full object-cover drop-shadow-lg"},null,8,["src"])):(r(),c(_,{key:1,name:"ui:user-avatar",class:"text-primary-600"}))]),a("div",C,[a("span",E,f(i(x)),1),t[0]||(t[0]=a("span",{class:"text-xs text-gray-500 font-medium"},"10/10/2020",-1))])]),a("div",L,[(r(!0),p(h,null,w(Array(5),(D,m)=>{var d;return r(),c(_,{key:`user-rate-${m}`,name:"ui:rate-star",class:k(["h-3",((d=e.review)==null?void 0:d.rating)>=m?"text-green-500":"text-gray-300"])},null,8,["class"])}),128))]),a("div",j,f(e.review.review),1)])}}});export{V as _};
