const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as S,l as q,c as w,w as g,o as c,a,d as y,t,e,q as d,$ as P,a0 as V,ag as z,ah as M}from"./C3gyeM1K.js";import{Skeleton as O}from"./BaodNYLQ.js";import{_ as T}from"./DAA1LH-g.js";import{_ as H}from"./JNWsvxxg.js";import{_ as R}from"./BsHCAE3W.js";import G from"./CuUhnQhf.js";import{u as J}from"./BCJZ1_ur.js";import{u as K}from"./CRC5xmnh.js";const Q=z(()=>M(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(n=>n.default||n)),U={key:1,class:"text-xl font-bold"},W={key:0,class:"flex flex-col w-full gap-4"},X={class:"flex justify-between gap-4"},Z={class:"flex justify-between gap-4"},ee={class:"flex justify-between gap-4"},se={class:"flex self-end flex-col gap-4 sm:w-1/2 xs:w-full"},te={class:"grid grid-cols-2 gap-4 w-full pb-4 max-sm:grid-cols-1"},oe={class:"flex items-center gap-2"},ae={class:"w-36 text-gray-600 font-medium"},re={class:"text-gray-700"},ne={class:"flex items-center gap-2"},le={class:"w-36 text-gray-600 font-medium"},de={class:"text-gray-700"},ie={class:"flex items-center gap-2"},ce={class:"w-36 text-gray-600 font-medium"},fe={class:"text-gray-700"},me={class:"flex items-center gap-2"},ue={class:"w-36 text-gray-600 font-medium"},_e={class:"text-gray-700",dir:"ltr"},pe={class:"flex items-center gap-2"},he={class:"w-36 text-gray-600 font-medium"},xe={class:"text-gray-700"},ge={class:"flex items-center gap-2"},ye={class:"w-36 text-gray-600 font-medium"},we={class:"text-gray-700"},be={class:"flex flex-col gap-4 w-full items-center"},ve={class:"flex w-full font-semibold text-gray-700 -mb-2 text-lg max-sm:text-base"},$e={class:"flex flex-grow"},je={class:"w-24 text-center"},ke={class:"w-20 text-center"},Ie={class:"w-20 text-center"},Ne={class:"flex w-44 h-28 bg-gray-100 justify-center items-center rounded-s-lg max-sm:hidden"},Ce={class:"flex h-full flex-grow p-4 items-center"},Ae={class:"px-2 font-medium text-gray-700"},De={class:"flex w-20 justify-center"},Be={class:"flex w-20 justify-center"},Fe={class:"font-bold"},Le={class:"flex w-20 justify-center"},qe={class:"font-bold"},Pe={class:"flex w-full justify-end max-sm:justify-between"},Ye={class:"flex w-1/2 flex-col gap-2 max-sm:w-full"},Ee={class:"flex items-center w-full justify-between"},Se={class:"text-base font-normal text-gray-600"},Ve={class:"text-base font-normal text-gray-600"},ze={class:"flex items-center w-full justify-between"},Me={class:"text-base font-normal text-gray-600"},Oe={class:"text-base font-normal text-gray-600"},Te={class:"flex items-center w-full justify-between hidden"},He={class:"text-base font-normal text-gray-600"},Re={class:"flex items-center w-full justify-between"},Ge={class:"text-xl font-bold text-gray-600"},Je={class:"text-xl font-bold text-gray-600"},ts=S({__name:"information",props:{order:{},loading:{type:Boolean}},setup(n){const{priceFormat:f}=J(),Y=q(()=>{var s,r,o,i;return["+",(r=(s=n.order.user)==null?void 0:s.phone)==null?void 0:r.code,(i=(o=n.order.user)==null?void 0:o.phone)==null?void 0:i.number].join("")}),E=q(()=>{var s,r,o,i,_,p,h,x,m,u;return[(r=(s=n.order)==null?void 0:s.address)==null?void 0:r.fullAddress,(i=(o=n.order)==null?void 0:o.address)==null?void 0:i.district,(p=(_=n.order)==null?void 0:_.address)==null?void 0:p.street,(x=(h=n.order)==null?void 0:h.address)==null?void 0:x.buildingNumber,(u=(m=n.order)==null?void 0:m.address)==null?void 0:u.apartmentNumber].filter(Boolean).join(", ")});return(s,r)=>{const o=O,i=R,_=Q,p=G,h=H,x=T;return c(),w(x,null,{default:g(()=>[a(i,null,{default:g(()=>[s.loading?(c(),w(o,{key:0,class:"w-1/2 h-6"})):(c(),y("span",U,t(s.$t("orders.details")),1))]),_:1}),a(h,{class:"flex flex-col gap-4 pb-4"},{default:g(()=>{var m,u,b,v,$,j,k,I;return[s.loading?(c(),y("div",W,[e("div",X,[a(o,{class:"h-10 w-1/2"}),a(o,{class:"h-10 w-1/2"})]),e("div",Z,[a(o,{class:"h-10 w-1/2"}),a(o,{class:"h-10 w-1/2"})]),e("div",ee,[a(o,{class:"h-10 w-1/2"}),a(o,{class:"h-10 w-1/2"})]),a(o,{class:"h-10 w-full"}),a(o,{class:"h-10 w-full"}),a(o,{class:"h-10 w-full"}),e("div",se,[a(o,{class:"h-10 w-full"}),a(o,{class:"h-10 w-full"}),a(o,{class:"h-10 w-full"})])])):(c(),y(P,{key:1},[e("div",te,[e("div",oe,[e("span",ae,t(s.$t("orders.number")),1),e("span",re,t(s.order.orderId),1)]),e("div",ne,[e("span",le,t(s.$t("form.receiver-name")),1),e("span",de,t((m=s.order.user)==null?void 0:m.fullName),1)]),e("div",ie,[e("span",ce,t(s.$t("orders.date")),1),e("span",fe,t(("useDateFormat"in s?s.useDateFormat:d(K))((u=s.order)==null?void 0:u.createdAt,"DD-MM-YYYY hh:mm A")),1)]),e("div",me,[e("span",ue,t(s.$t("form.reviver-phone-number")),1),e("span",_e,t(d(Y)),1)]),e("div",pe,[e("span",he,t(s.$t("orders.number")),1),e("span",xe,t(s.order.orderId),1)]),e("div",ge,[e("span",ye,t(s.$t("form.address")),1),e("span",we,t(d(E)),1)])]),r[2]||(r[2]=e("div",{class:"flex w-full border border-gray-100 my-2"},null,-1)),e("div",be,[e("div",ve,[e("span",$e,t(s.$t("orders.product")),1),e("span",je,t(s.$t("product.quantity")),1),e("span",ke,t(s.$t("product.price")),1),e("span",Ie,t(s.$t("orders.total")),1)]),(c(!0),y(P,null,V(s.order.orderItems,l=>{var N;return c(),w(p,{key:`${l.productOrderItemId}`,class:"flex w-full border border-gray-200 rounded-lg p-px items-center text-base max-sm:text-sm",to:`/product/${(N=l.product)==null?void 0:N.slug}`},{default:g(()=>{var C,A,D,B,F,L;return[e("div",Ne,[a(_,{src:(D=(A=(C=l.product.media)==null?void 0:C.cover)==null?void 0:A[0])==null?void 0:D.preview,class:"object-contain h-full p-4"},null,8,["src"])]),e("div",Ce,[e("span",Ae,t((B=l.product)==null?void 0:B.name),1)]),e("div",De,t(l.quantity),1),e("div",Be,[e("span",Fe,t(d(f)((F=l.price)==null?void 0:F.value)),1)]),e("div",Le,[e("span",qe,t(d(f)(l.quantity*((L=l.price)==null?void 0:L.value))),1)])]}),_:2},1032,["to"])}),128))]),r[3]||(r[3]=e("div",{class:"flex w-full border border-gray-100 my-2"},null,-1)),e("div",Pe,[e("div",Ye,[e("div",Ee,[e("span",Se,t(s.$t("cart.payment-sub-amount-title",{item:s.$t("cart.item",{count:(v=(b=s.order)==null?void 0:b.orderItems)==null?void 0:v.length}),number:(j=($=s.order)==null?void 0:$.orderItems)==null?void 0:j.length})),1),e("span",Ve,t(d(f)(s.order.subTotal.value)),1)]),e("div",ze,[e("span",Me,t(s.$t("orders.delivery-service")),1),e("span",Oe,t(d(f)((k=s.order.shippingPrice)==null?void 0:k.value)),1)]),e("div",Te,[e("span",He,t(s.$t("orders.coupon-discount")),1),r[0]||(r[0]=e("span",{class:"text-base font-normal text-gray-600"}," N/A ",-1))]),r[1]||(r[1]=e("div",{class:"flex border-t border-dashed border-gray-200 w-full my-2"},null,-1)),e("div",Re,[e("span",Ge,t(s.$t("orders.total")),1),e("span",Je,t(d(f)((I=s.order.total)==null?void 0:I.value)),1)])])])],64))]}),_:1})]),_:1})}}});export{ts as _};
