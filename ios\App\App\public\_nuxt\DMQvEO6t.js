import L from"./CuUhnQhf.js";import{Skeleton as S}from"./BaodNYLQ.js";import V from"./C4nYFGHw.js";import{_ as q}from"./DAA1LH-g.js";import{_ as A}from"./JNWsvxxg.js";import{b as E,_ as I,a as _}from"./CiqmSqHP.js";import{_ as T,a as j}from"./D3J2c5MI.js";import{i as D,Y as F,l as i,c as a,w as s,o as t,e as u,a as o,t as d,h as P,q as e,p,d as f,a0 as x,$ as g}from"./C3gyeM1K.js";const Y={class:"px-4 py-4 flex justify-between items-center"},z={class:"text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl"},X=D({__name:"our-choice",setup(G){const{data:y,error:l,status:h}=F("products",{query:{limit:4,labels:"our-choices-are-for-you"}});l.value&&console.error("Error fetching new our choice:",l.value);const k=i(()=>y.value.items),c=i(()=>h.value!=="success");return(m,H)=>{const b=L,n=S,$=V,w=A,C=q;return t(),a(C,{class:"col-span-3 flex flex-col max-md:col-span-3"},{default:s(()=>[u("div",Y,[u("span",z,d(m.$t("home.our-choice-title")),1),o(b,{to:"/categories/smartphones",class:"text-primary-600 hidden max-sm:flex"},{default:s(()=>[P(d(m.$t("home.see-more")),1)]),_:1})]),o(w,{class:"grid md:grid-cols-4 xs:grid-cols-1 gap-4 px-4 place-items-center"},{default:s(()=>[o(e(E),{opts:{align:"start",slidesToScroll:"auto"},class:"w-full col-span-4"},{default:s(({canScrollNext:v,canScrollPrev:N})=>[o(e(I),null,{default:s(()=>[e(c)?(t(!0),f(g,{key:0},x(Array(4),(r,B)=>(t(),a(e(_),{key:`product-skeleton-${B}`,class:"flex flex-col rounded h-full border p-2 border-gray-200 max-w-1/4 max-w-72 me-6 w-full"},{default:s(()=>[o(n,{class:"w-full h-32 bg-gray-200 mb-2"}),o(n,{class:"w-4/5 h-5 bg-gray-200 mb-2"}),o(n,{class:"w-1/3 h-5 bg-gray-200 mb-2"})]),_:2},1024))),128)):(t(!0),f(g,{key:1},x(e(k),r=>(t(),a(e(_),{key:r.productId,class:"basis-4/10"},{default:s(()=>[(t(),a($,{key:`product-${r.productId}`,product:r,variant:"lg"},null,8,["product"]))]),_:2},1024))),128))]),_:1}),!e(c)&&N?(t(),a(e(T),{key:0})):p("",!0),!e(c)&&v?(t(),a(e(j),{key:1})):p("",!0)]),_:1})]),_:1})]),_:1})}}});export{X as _};
