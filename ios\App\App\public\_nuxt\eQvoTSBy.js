const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as L,Y as w,l as d,c as l,w as e,o as t,a as r,e as _,t as B,d as p,q as u,a0 as f,W as g,$ as x,ag as E,ah as N}from"./C3gyeM1K.js";import{Skeleton as q}from"./BaodNYLQ.js";import{_ as z}from"./DAA1LH-g.js";import{_ as I}from"./JNWsvxxg.js";import S from"./CuUhnQhf.js";const V=E(()=>N(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(n=>n.default||n)),j={class:"flex px-2 py-4"},D={class:"text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl"},F={class:"grid md:grid-cols-2 sm:grid-cols-3 xs:grid-cols-2 gap-4 group"},G=L({__name:"actions",setup(n){const{data:h,error:i,status:y}=w("categories",{query:{isFeatured:1,limit:3,order_by:"createdAt,desc"}}),v=d(()=>{var o;return(o=h.value)==null?void 0:o.slice(0,3)}),k=d(()=>y.value!=="success");return i.value&&console.error("Error fetching most popular:",i.value),(o,P)=>{const b=q,c=z,C=V,$=S,A=I;return t(),l(c,{class:"col-span-1 max-md:col-span-3"},{default:e(()=>[r(A,{class:"flex flex-col px-4"},{default:e(()=>[_("div",j,[_("span",D,B(o.$t("home.action-title")),1)]),_("div",F,[u(k)?(t(!0),p(x,{key:0},f(Array(3),(s,a)=>(t(),l(c,{key:`category-loading-${a}`,class:g({"md:col-span-2 sm:col-span-1 xs:col-span-2":!a})},{default:e(()=>[r(b,{class:"w-full h-48 bg-gray-200 rounded-lg"})]),_:2},1032,["class"]))),128)):(t(!0),p(x,{key:1},f(u(v),(s,a)=>(t(),l($,{key:s==null?void 0:s.categoryId,to:`/categories/${s.slug}`,class:g({"md:col-span-2 sm:col-span-1 xs:col-span-2":!a})},{default:e(()=>[r(c,{class:"flex items-center justify-center border border-gray-200 shadow-lg bg-gray-100 p-4 hover:border-primary-500"},{default:e(()=>{var m;return[r(C,{src:(m=s.cover)==null?void 0:m.src,alt:s.name,quality:"100",height:"100%",class:"object-cover h-40"},null,8,["src","alt"])]}),_:2},1024)]),_:2},1032,["to","class"]))),128))])]),_:1})]),_:1})}}});export{G as _};
