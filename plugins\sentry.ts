// interface Error {
// 	message?: string
// 	stack?: string | object | unknown
// }
import * as Sentry from '@sentry/nuxt'

export default defineNuxtPlugin((nuxtApp) => {
	// nuxtApp.hook('page:start', () => {
	// 	console.log('page:start')
	// 	console.log('NODE_ENV', process.env.NODE_ENV)
	// })
	// Prevents Sentry from logging
	nuxtApp.hook('vue:error', (error: Error, payload, cycle) => {
		console.log('App Error :: ', { message: error?.message, stack: error?.stack, payload, cycle })
		Sentry.captureException({ message: error?.message, stack: error?.stack, payload, cycle })
	})
	// Inside the plugin, after initializing sentry
})
