import{g as n}from"./FUKBWQwh.js";const f="rovingFocusGroup.onEntryFocus",u={bubbles:!1,cancelable:!0},s={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function i(r,t){return t!=="rtl"?r:r==="ArrowLeft"?"ArrowRight":r==="ArrowRight"?"ArrowLeft":r}function a(r,t,e){const o=i(r.key,e);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return s[o]}function w(r,t=!1){const e=n();for(const o of r)if(o===e||(o.focus({preventScroll:t}),n()!==e))return}function A(r,t){return r.map((e,o)=>r[(t+o)%r.length])}export{f as E,u as a,w as f,a as g,A as w};
