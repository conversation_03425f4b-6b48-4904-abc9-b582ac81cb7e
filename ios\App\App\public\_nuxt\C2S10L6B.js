import{u as E}from"./Bzz9boR6.js";import{u as C,o as k,a,s as u,n as S}from"./C0giEChS.js";import{r as y,m as _,n as R,R as h,l as O,d as P,o as D,s as p,p as b,q as g,a1 as v}from"./C3gyeM1K.js";import"./BsYnu6hU.js";import"./CvhKjyCU.js";k({id:u(),runtimeConfig:a(k({locale:a(u())})),tokenId:a(u()),cookieDomain:a(u()),cookieExpiry:a(S())});function $(m){let c=Promise.resolve();return C("crisp",r=>({scriptInput:{src:"https://client.crisp.chat/l.js"},schema:void 0,scriptOptions:{use(){const e=i=>{var n;return((n=window.$crisp)==null?void 0:n[i])||((...s)=>{c.then(()=>window.$crisp[i](...s))})};return{push:window.$crisp.push,do:e("do"),set:e("set"),get:e("get"),on:e("on"),off:e("off"),config:e("config"),help:e("help")}}},clientInit:()=>{var e;window.$crisp=[],window.CRISP_WEBSITE_ID=r.id,(e=r.runtimeConfig)!=null&&e.locale&&(window.CRISP_RUNTIME_CONFIG={locale:r.runtimeConfig.locale}),r.cookieDomain&&(window.CRISP_COOKIE_DOMAIN=r.cookieDomain),r.cookieExpiry&&(window.CRISP_COOKIE_EXPIRATION=r.cookieExpiry),r.tokenId&&(window.CRISP_TOKEN_ID=r.tokenId),c=new Promise(i=>{window.CRISP_READY_TRIGGER=i})}}),m)}const L={__name:"ScriptCrisp",props:{trigger:{type:[String,Array,Boolean],required:!1,default:"click"},id:{type:String,required:!0},runtimeConfig:{type:Object,required:!1},tokenId:{type:String,required:!1},cookieDomain:{type:String,required:!1},cookieExpiry:{type:Number,required:!1}},emits:["ready","error"],setup(m,{expose:c,emit:r}){const e=m,i=r,n=y(null),s=E({trigger:e.trigger,el:n}),d=y(!1),f=$({id:e.id,runtimeConfig:e.runtimeConfig,tokenId:e.tokenId,cookieDomain:e.cookieDomain,cookieExpiry:e.cookieExpiry,scriptOptions:{trigger:s}}),{onLoaded:w,status:l}=f;e.trigger==="click"&&w(o=>{o.do("chat:open")}),c({crisp:f});let t;_(()=>{R(l,o=>{o==="loaded"?(t=new MutationObserver(()=>{document.getElementById("crisp-chatbox")&&(d.value=!0,i("ready",f),t.disconnect())}),t.observe(document.body,{childList:!0,subtree:!0})):o==="error"&&i("error")})}),h(()=>{t==null||t.disconnect()});const I=O(()=>({...s instanceof Promise?s.ssrAttrs||{}:{}}));return(o,q)=>(D(),P("div",v({ref_key:"rootEl",ref:n,style:{display:d.value?"none":"block"}},I.value),[p(o.$slots,"default",{ready:d.value}),g(l)==="awaitingLoad"?p(o.$slots,"awaitingLoad",{key:0}):g(l)==="loading"||!d.value?p(o.$slots,"loading",{key:1}):g(l)==="error"?p(o.$slots,"error",{key:2}):b("",!0)],16))}};export{L as default};
