import{_ as oe,a as re,b as ne}from"./BzSOFl6X.js";import{i as de,k as ie,X as ue,r as V,l as K,ac as me,ab as L,c as fe,w as l,a2 as O,Y as ce,o as $,e as t,a as s,h as f,t as r,Z as pe,d as C,q as d,a4 as S,ad as _e,a1 as b,a0 as he,$ as be,ae as W,W as ye,K as X}from"./C3gyeM1K.js";import{u as ve,t as $e,o as Y,a as Z,s as m,n as N,b as G,F as we}from"./ZoTeemxs.js";import{_ as xe}from"./CjR7N8Rt.js";import{_ as ge}from"./CvMKnfiC.js";import{_ as Fe}from"./CaU5TI22.js";import{_ as ke}from"./BRCXRF_f.js";import{_ as Te}from"./BoqtTPyX.js";import{_ as Ve}from"./C0H2RQFE.js";import{c as Ce,P as Ie}from"./DLHC-ANp.js";const Pe={class:"flex gap-2 flex-col px-4 w-full min-w-3xl mb-2 max-sm:max-h-[650px] overflow-y-auto bg-white"},Se={class:"text-lg font-bold"},Ne={class:"grid grid-cols-2 gap-6 w-full mt-4"},De={value:null},qe=["value"],je={class:"flex justify-between items-start gap-2 col-span-2"},Ae={class:"flex flex-col gap-2"},Ue={class:"flex gap-2 items-center"},Me={class:"text-sm font-semibold"},Be={class:"text-gray-500 text-sm"},Ee={class:"flex gap-4 items-center cursor-pointer mt-2"},Re={class:"flex gap-2 items-center"},ze={for:"house",class:"text-sm"},Ke={class:"flex gap-2 items-center"},Le={for:"work",class:"text-sm"},Oe={class:"flex items-center gap-2 w-full justify-end"},We={for:"set-default"},Xe={class:"flex w-full col-span-2 justify-between items-center max-sm:flex-col max-sm:items-start"},Ye={class:"text-lg font-bold text-nowrap"},Ze={class:"flex items-center gap-2 w-full justify-end max-sm:justify-start"},Ge={for:"set-default",class:"font-semibold"},He={class:"flex justify-end gap-4 w-full my-2 max-sm:my-8 max-sm:justify-between items-center border-t pt-4"},Je={key:0},Qe={key:1},us=de({__name:"form",props:{address:{default:null}},emits:["fetch:address","close:address"],async setup(w,{emit:H}){var j,A;let x,D;const g=H,{t:i}=ie(),{data:J}=([x,D]=ue(()=>ce("lookups-website/cities")),x=await x,D(),x),I=V({...w.address}),F=V(((j=I.value)==null?void 0:j.default)===1),k=V((A=I.value)==null?void 0:A.isProfileDataShared),q=K(()=>{var e;return((e=w.address)==null?void 0:e.default)===1}),T=V(!1),n=ve({validationSchema:$e(Y({cityId:N().min(1,i("error.required")).nullish().refine(e=>e!=null,{message:i("error.required")}),district:m().min(1,i("error.required")),firstName:m().min(1,i("error.required")),lastName:m().min(1,i("error.required")),recipientName:m().min(1,i("error.required")),fullAddress:m().optional(),buildingType:m().min(1,i("error.required")),default:Z([G(),N()]).optional(),isProfileDataShared:Z([G(),N()]).optional(),phone:Y({number:m().min(5,i("error.required")),iso:m(),code:m()}).transform(e=>{const a=Ce.find(u=>u.dial_code===e.code);return{...e,iso:(a==null?void 0:a.code)||e.iso}}).refine(e=>{if(!e.iso||!e.number)return!1;const a=Ie[e.iso];return a?new RegExp(`^${a[2]}$`).test(e.number)?!0:(n.setErrors({"phone.number":i("error.phone-number-invalid")}),!1):(n.setErrors({"phone.number":i("error.phone-number-invalid")}),!1)},{message:i("error.phone-number-invalid"),path:["number"]})})),initialValues:me({...I.value}),validateOnMount:!1}),Q=K(()=>{var e;return!!((e=w.address)!=null&&e.addressId)}),ee=async e=>{const{$api:a}=O();return a(`addresses/${w.address.addressId}`,{method:"PUT",body:e})},se=async e=>{const{$api:a}=O();await a("addresses",{method:"POST",body:e})},ae=n.handleSubmit(async()=>{T.value=!0;const e={...n.values,default:F.value?1:0,isProfileDataShared:k.value,phone:{...n.values.phone}};Q.value&&ee(e).then(()=>{L.success(i("form.address-updated-success")),g("fetch:address",!1)}).finally(()=>{T.value=!1,n.resetForm()}),se(e).then(()=>{L.success(i("form.address-added-success")),g("fetch:address",!1)}).finally(()=>{T.value=!1,n.resetForm()})});return(e,a)=>{const u=xe,c=re,p=ne,_=oe,h=we,y=ge,U=Fe,le=ke,M=Te,te=Ve;return $(),fe(te,{title:e.$t("form.address-modal-title"),size:"max-w-xl",onClose:a[8]||(a[8]=P=>g("close:address"))},{body:l(()=>{var P,B,E,R,z;return[t("div",Pe,[t("span",Se,r(e.$t("form.location")),1),t("div",Ne,[s(h,{name:"cityId",class:"col-span-1"},{default:l(({componentField:o})=>[s(_,{class:"w-full flex flex-col"},{default:l(()=>[s(u,{class:"font-bold"},{default:l(()=>[f(r(e.$t("form.city"))+"* ",1)]),_:1}),s(c,null,{default:l(()=>[S(t("select",b({id:"city","onUpdate:modelValue":a[0]||(a[0]=v=>d(n).values.cityId=v),name:"city"},o,{class:"text-sm !outline-none border border-gray-200 rounded px-2 py-1 h-11"}),[t("option",De,r(e.$t("form.select-city")),1),($(!0),C(be,null,he(d(J),v=>($(),C("option",{key:`city_id_${v.value}`,value:v.value},r(v.text),9,qe))),128))],16),[[_e,d(n).values.cityId,void 0,{number:!0}]])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"district",class:"col-span-1"},{default:l(({componentField:o})=>[s(_,{class:"w-full flex flex-col"},{default:l(()=>[s(u,{class:"font-bold"},{default:l(()=>[f(r(e.$t("form.area"))+"* ",1)]),_:1}),s(c,null,{default:l(()=>[s(y,b({class:"h-11",type:"text",placeholder:e.$t("form.area")},o),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"fullAddress"},{default:l(({componentField:o})=>[s(_,{class:"w-full flex flex-col col-span-2"},{default:l(()=>[s(u,{class:"font-bold"},{default:l(()=>[f(r(e.$t("form.full-address"))+"* ",1)]),_:1}),s(c,null,{default:l(()=>[s(y,b({class:"h-11",type:"text",placeholder:e.$t("form.full-address")},o),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),t("div",je,[t("div",Ae,[t("div",Ue,[t("span",Me,r(e.$t("form.select-location")),1),t("span",Be,"("+r(e.$t("form.optional"))+")",1)]),t("div",Ee,[t("div",Re,[S(t("input",{id:"house","onUpdate:modelValue":a[1]||(a[1]=o=>d(n).values.buildingType=o),type:"radio",name:"location",value:"home",class:"w-4 h-4"},null,512),[[W,d(n).values.buildingType]]),t("label",ze,r(e.$t("form.house")),1)]),t("div",Ke,[S(t("input",{id:"work","onUpdate:modelValue":a[2]||(a[2]=o=>d(n).values.buildingType=o),type:"radio",name:"location",value:"work",class:"w-4 h-4"},null,512),[[W,d(n).values.buildingType]]),t("label",Le,r(e.$t("form.work")),1)])])]),t("div",Oe,[t("label",We,r(e.$t("form.set-default-address")),1),s(U,{id:`set-default-${(P=e.address)==null?void 0:P.addressId}`,modelValue:d(F),"onUpdate:modelValue":a[3]||(a[3]=o=>X(F)?F.value=o:null),disabled:d(q),class:ye({"opacity-50 pointer-event-none":d(q)})},null,8,["id","modelValue","disabled","class"])])]),a[9]||(a[9]=t("div",{class:"flex col-span-2"},[t("div",{class:"flex w-full border-dashed border border-gray-200 my-2"})],-1)),t("div",Xe,[t("span",Ye,r(e.$t("form.receipt-details")),1),t("div",Ze,[t("label",Ge,r(e.$t("form.use-user-address-details")),1),s(U,{id:`set-default-${(B=e.address)==null?void 0:B.addressId}`,modelValue:d(k),"onUpdate:modelValue":a[4]||(a[4]=o=>X(k)?k.value=o:null)},null,8,["id","modelValue"])])]),s(le,{title:e.$t("form.new-phone-title"),error:(R=(E=d(n).errors)==null?void 0:E.value)==null?void 0:R.phone,value:(z=e.address)==null?void 0:z.phone,onUpdate:a[5]||(a[5]=o=>{d(n).setFieldValue("phone",{number:`${o.nationalNumber}`,code:o.countryCallingCode,iso:o.countryCode})})},null,8,["title","error","value"]),s(h,{name:"firstName"},{default:l(({componentField:o})=>[s(_,{class:"w-full flex flex-col col-span-1"},{default:l(()=>[s(u,{class:"font-bold"},{default:l(()=>[f(r(e.$t("form.first-name"))+"* ",1)]),_:1}),s(c,null,{default:l(()=>[s(y,b({class:"h-11",type:"text",placeholder:e.$t("form.first-name")},o),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"lastName"},{default:l(({componentField:o})=>[s(_,{class:"w-full flex flex-col col-span-1"},{default:l(()=>[s(u,{class:"font-bold"},{default:l(()=>[f(r(e.$t("form.last-name"))+"* ",1)]),_:1}),s(c,null,{default:l(()=>[s(y,b({class:"h-11",type:"text",placeholder:e.$t("form.last-name")},o),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"recipientName"},{default:l(({componentField:o})=>[s(_,{class:"w-full flex flex-col col-span-2"},{default:l(()=>[s(u,{class:"font-bold"},{default:l(()=>[f(r(e.$t("form.recipient-name"))+"* ",1)]),_:1}),s(c,null,{default:l(()=>[s(y,b({class:"h-11",type:"text",placeholder:e.$t("form.recipient-name")},o),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1})])])]}),footer:l(()=>[t("div",He,[s(M,{variant:"outline",class:"max-sm:w-full",onClick:a[6]||(a[6]=pe(()=>g("close:address"),["prevent"]))},{default:l(()=>[f(r(e.$t("form.cancel")),1)]),_:1}),s(M,{class:"max-sm:w-full",disabled:d(T),onClick:a[7]||(a[7]=()=>d(ae)())},{default:l(()=>[e.address.addressId?($(),C("span",Je,r(e.$t("form.edit-address")),1)):($(),C("span",Qe,r(e.$t("form.save-address")),1))]),_:1},8,["disabled"])])]),_:1},8,["title"])}}});export{us as _};
