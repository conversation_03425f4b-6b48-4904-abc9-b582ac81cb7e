import{O as i,U as l,r as f,l as a,H as b}from"./C3gyeM1K.js";import{b as p}from"./BBuxlZld.js";function x(t,n){const s=typeof t=="string"&&!n?`${t}Context`:n,r=Symbol(s);return[e=>{const c=i(r,e);if(c||c===null)return c;throw new Error(`Injection \`${r.toString()}\` not found. Component must be used within ${Array.isArray(t)?`one of the following components: ${t.join(", ")}`:`\`${t}\``}`)},e=>(l(r,e),e)]}function y(){const t=b(),n=f(),s=a(()=>{var e,c;return["#text","#comment"].includes((e=n.value)==null?void 0:e.$el.nodeName)?(c=n.value)==null?void 0:c.$el.nextElementSibling:p(n)}),r=Object.assign({},t.exposed),o={};for(const e in t.props)Object.defineProperty(o,e,{enumerable:!0,configurable:!0,get:()=>t.props[e]});if(Object.keys(r).length>0)for(const e in r)Object.defineProperty(o,e,{enumerable:!0,configurable:!0,get:()=>r[e]});Object.defineProperty(o,"$el",{enumerable:!0,configurable:!0,get:()=>t.vnode.el}),t.exposed=o;function u(e){n.value=e,e&&(Object.defineProperty(o,"$el",{enumerable:!0,configurable:!0,get:()=>e instanceof Element?e:e.$el}),t.exposed=o)}return{forwardRef:u,currentRef:n,currentElement:s}}export{x as c,y as u};
