import{c as m}from"./jAU0Cazi.js";import{u,a as f}from"./D3IGe8e3.js";import{_}from"./D_nR533G.js";import{u as g}from"./C2RgRyPS.js";import{i as p,c,o as l,w as i,s as d,v as x,x as P,q as s,l as w,a1 as y}from"./C3gyeM1K.js";const B=p({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(a,{emit:o}){const r=a,e=u(o);return g(),(t,h)=>(l(),c(s(_),x(P({...r,...s(e)})),{default:i(()=>[d(t.$slots,"default")]),_:3},16))}}),z=p({__name:"DropdownMenuItem",props:{disabled:{type:<PERSON><PERSON><PERSON>},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:<PERSON>olean}},setup(a){const o=a,r=w(()=>{const{class:e,...t}=o;return t}),n=f(r);return(e,t)=>(l(),c(s(B),y(s(n),{class:s(m)("relative flex cursor-default select-none items-center rounded-sm gap-2 px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50  [&>svg]:size-4 [&>svg]:shrink-0",e.inset&&"pl-8",o.class)}),{default:i(()=>[d(e.$slots,"default")]),_:3},16,["class"]))}});export{z as _};
