import{n as w,l as L,G as u,m as H,L as T,M as I,N,H as O,J as R,O as Y,P as b,D as x,Q as E,R as W,E as Z,F as M,K as y,S as j,q as G,T as z,U,V,r as B}from"./C3gyeM1K.js";function pt(t,e){var n;const r=x();return Z(()=>{r.value=t()},{...e,flush:(n=void 0)!=null?n:"sync"}),M(r)}function gt(t,e){let n,r,o;const s=x(!0),a=()=>{s.value=!0,o()};w(t,a,{flush:"sync"});const i=typeof e=="function"?e:e.get,c=typeof e=="function"?void 0:e.set,l=b((f,m)=>(r=f,o=m,{get(){return s.value&&(n=i(n),s.value=!1),r(),n},set(d){c==null||c(d)}}));return Object.isExtensible(l)&&(l.trigger=a),l}function P(t){return I()?(N(t),!0):!1}function vt(){const t=new Set,e=s=>{t.delete(s)};return{on:s=>{t.add(s);const a=()=>e(s);return P(a),{off:a}},off:e,trigger:(...s)=>Promise.all(Array.from(t).map(a=>a(...s))),clear:()=>{t.clear()}}}function ht(t){let e=!1,n;const r=E(!0);return(...o)=>(e||(n=r.run(()=>t(...o)),e=!0),n)}const g=new WeakMap,K=(...t)=>{var e;const n=t[0],r=(e=O())==null?void 0:e.proxy;if(r==null&&!R())throw new Error("injectLocal must be called in setup");return r&&g.has(r)&&n in g.get(r)?g.get(r)[n]:Y(...t)},q=(t,e)=>{var n;const r=(n=O())==null?void 0:n.proxy;if(r==null)throw new Error("provideLocal must be called in setup");g.has(r)||g.set(r,Object.create(null));const o=g.get(r);o[t]=e,U(t,e)};function St(t,e){const n=Symbol(t.name||"InjectionState"),r=void 0;return[(...a)=>{const i=t(...a);return q(n,i),i},()=>K(n,r)]}function wt(t){let e=0,n,r;const o=()=>{e-=1,r&&e<=0&&(r.stop(),n=void 0,r=void 0)};return(...s)=>(e+=1,r||(r=E(!0),n=r.run(()=>t(...s))),P(o),n)}function yt(t,e){if(typeof Symbol<"u"){const n={...t};return Object.defineProperty(n,Symbol.iterator,{enumerable:!1,value(){let r=0;return{next:()=>({value:e[r++],done:r>e.length})}}}),n}else return Object.assign([...e],t)}function X(t){if(!y(t))return j(t);const e=new Proxy({},{get(n,r,o){return G(Reflect.get(t.value,r,o))},set(n,r,o){return y(t.value[r])&&!y(o)?t.value[r].value=o:t.value[r]=o,!0},deleteProperty(n,r){return Reflect.deleteProperty(t.value,r)},has(n,r){return Reflect.has(t.value,r)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return j(e)}function J(t){return X(L(t))}function Dt(t,...e){const n=e.flat(),r=n[0];return J(()=>Object.fromEntries(typeof r=="function"?Object.entries(z(t)).filter(([o,s])=>!r(u(s),o)):Object.entries(z(t)).filter(o=>!n.includes(o[0]))))}const Q=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const Ot=t=>typeof t<"u",tt=Object.prototype.toString,bt=t=>tt.call(t)==="[object Object]",D=()=>{},Mt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),Pt=et();function et(){var t,e;return Q&&((t=window==null?void 0:window.navigator)==null?void 0:t.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((e=window==null?void 0:window.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function k(t,e){function n(...r){return new Promise((o,s)=>{Promise.resolve(t(()=>e.apply(this,r),{fn:e,thisArg:this,args:r})).then(o).catch(s)})}return n}const $=t=>t();function nt(t,e={}){let n,r,o=D;const s=c=>{clearTimeout(c),o(),o=D};let a;return c=>{const l=u(t),f=u(e.maxWait);return n&&s(n),l<=0||f!==void 0&&f<=0?(r&&(s(r),r=null),Promise.resolve(c())):new Promise((m,d)=>{o=e.rejectOnCancel?d:m,a=c,f&&!r&&(r=setTimeout(()=>{n&&s(n),r=null,m(a())},f)),n=setTimeout(()=>{r&&s(r),r=null,m(c())},l)})}}function rt(t=$,e={}){const{initialState:n="active"}=e,r=at(n==="active");function o(){r.value=!1}function s(){r.value=!0}const a=(...i)=>{r.value&&t(...i)};return{isActive:M(r),pause:o,resume:s,eventFilter:a}}function ot(t){const e=Object.create(null);return n=>e[n]||(e[n]=t(n))}const st=/-(\w)/g,At=ot(t=>t.replace(st,(e,n)=>n?n.toUpperCase():""));function Ft(t){return t.endsWith("rem")?Number.parseFloat(t)*16:Number.parseFloat(t)}function C(t){return O()}function _t(t){return Array.isArray(t)?t:[t]}function at(...t){if(t.length!==1)return V(...t);const e=t[0];return typeof e=="function"?M(b(()=>({get:e,set:D}))):B(e)}function jt(t,e=1e4){return b((n,r)=>{let o=u(t),s;const a=()=>setTimeout(()=>{o=u(t),r()},u(e));return P(()=>{clearTimeout(s)}),{get(){return n(),o},set(i){o=i,r(),clearTimeout(s),s=a()}}})}function zt(t,e=200,n={}){return k(nt(e,n),t)}function it(t,e,n={}){const{eventFilter:r=$,...o}=n;return w(t,k(r,e),o)}function Lt(t,e,n={}){const{eventFilter:r,initialState:o="active",...s}=n,{eventFilter:a,pause:i,resume:c,isActive:l}=rt(r,{initialState:o});return{stop:it(t,e,{...s,eventFilter:a}),pause:i,resume:c,isActive:l}}const Tt=u;function xt(t,e){C()&&W(t,e)}function Et(t,e=!0,n){C()?H(t,n):e?t():T(t)}const ct=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[T\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/i,ut=/[YMDHhms]o|\[([^\]]+)\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;function lt(t,e,n,r){let o=t<12?"AM":"PM";return r&&(o=o.split("").reduce((s,a)=>s+=`${a}.`,"")),n?o.toLowerCase():o}function p(t){const e=["th","st","nd","rd"],n=t%100;return t+(e[(n-20)%10]||e[n]||e[0])}function ft(t,e,n={}){var r;const o=t.getFullYear(),s=t.getMonth(),a=t.getDate(),i=t.getHours(),c=t.getMinutes(),l=t.getSeconds(),f=t.getMilliseconds(),m=t.getDay(),d=(r=n.customMeridiem)!=null?r:lt,h=S=>{var v;return(v=S.split(" ")[1])!=null?v:""},A={Yo:()=>p(o),YY:()=>String(o).slice(-2),YYYY:()=>o,M:()=>s+1,Mo:()=>p(s+1),MM:()=>`${s+1}`.padStart(2,"0"),MMM:()=>t.toLocaleDateString(u(n.locales),{month:"short"}),MMMM:()=>t.toLocaleDateString(u(n.locales),{month:"long"}),D:()=>String(a),Do:()=>p(a),DD:()=>`${a}`.padStart(2,"0"),H:()=>String(i),Ho:()=>p(i),HH:()=>`${i}`.padStart(2,"0"),h:()=>`${i%12||12}`.padStart(1,"0"),ho:()=>p(i%12||12),hh:()=>`${i%12||12}`.padStart(2,"0"),m:()=>String(c),mo:()=>p(c),mm:()=>`${c}`.padStart(2,"0"),s:()=>String(l),so:()=>p(l),ss:()=>`${l}`.padStart(2,"0"),SSS:()=>`${f}`.padStart(3,"0"),d:()=>m,dd:()=>t.toLocaleDateString(u(n.locales),{weekday:"narrow"}),ddd:()=>t.toLocaleDateString(u(n.locales),{weekday:"short"}),dddd:()=>t.toLocaleDateString(u(n.locales),{weekday:"long"}),A:()=>d(i,c),AA:()=>d(i,c,!1,!0),a:()=>d(i,c,!0),aa:()=>d(i,c,!0,!0),z:()=>h(t.toLocaleDateString(u(n.locales),{timeZoneName:"shortOffset"})),zz:()=>h(t.toLocaleDateString(u(n.locales),{timeZoneName:"shortOffset"})),zzz:()=>h(t.toLocaleDateString(u(n.locales),{timeZoneName:"shortOffset"})),zzzz:()=>h(t.toLocaleDateString(u(n.locales),{timeZoneName:"longOffset"}))};return e.replace(ut,(S,v)=>{var F,_;return(_=v??((F=A[S])==null?void 0:F.call(A)))!=null?_:S})}function dt(t){if(t===null)return new Date(Number.NaN);if(t===void 0)return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){const e=t.match(ct);if(e){const n=e[2]-1||0,r=(e[7]||"0").substring(0,3);return new Date(e[1],n,e[3]||1,e[4]||0,e[5]||0,e[6]||0,r)}}return new Date(t)}function kt(t,e="HH:mm:ss",n={}){return L(()=>ft(dt(u(t)),u(e),n))}function $t(t,e,n){return w(t,e,{...n,immediate:!0})}function Ct(t,e,n){const r=w(t,(...o)=>(T(()=>r()),e(...o)),n);return r}export{pt as A,Dt as B,P as a,Q as b,vt as c,K as d,At as e,gt as f,bt as g,Mt as h,Pt as i,_t as j,Ot as k,Lt as l,yt as m,D as n,Tt as o,Ft as p,zt as q,Ct as r,jt as s,Et as t,kt as u,St as v,$t as w,ht as x,wt as y,xt as z};
