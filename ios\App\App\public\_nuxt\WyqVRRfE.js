const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as Y,j as G,ak as J,k as K,z as M,l as m,Y as Q,d as n,a as s,q as c,w as y,$ as d,o as a,c as U,t as _,e as l,a0 as h,p as X,W as S,ag as Z,g as ee,ah as se}from"./C3gyeM1K.js";import{_ as te,u as oe}from"./BD9sJOps.js";import{Skeleton as ae}from"./BaodNYLQ.js";import{_ as le}from"./DAA1LH-g.js";import{_ as ne}from"./JNWsvxxg.js";import{_ as ce}from"./BsHCAE3W.js";import{_ as re}from"./BoqtTPyX.js";import{u as ue}from"./BCJZ1_ur.js";import{u as ie}from"./BrsQk_-I.js";import"./CuUhnQhf.js";import"./jAU0Cazi.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";const de=Z(()=>se(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(v=>v.default||v)),_e={key:1,class:"font-bold text-lg"},pe={key:0,class:"flex flex-col gap-2"},me={class:"grid grid-cols-3 w-full p-2 gap-4"},fe={class:"flex w-full flex-col items-center gap-2"},ge={class:"flex w-full flex-col items-center gap-2"},he={class:"flex relative"},ve=["onClick"],xe={class:"text-primary-500 font-bold w-2/3 line-clamp-2"},ke={class:"font-bold text-green-1000"},we={class:"flex gap-0 w-full flex-col"},ye={class:"flex items-center font-medium text-gray-700 gap-2"},$e={class:"text-lg font-bold"},He=Y({__name:"[...ids]",setup(v){const F=G(),$=J(),{t:L}=K(),b=M(),x=ie(),{priceFormat:P}=ue(),k=m(()=>{var t;return(t=F.params)==null?void 0:t.ids}),{data:C,error:B,status:j}=Q("/compare",{query:{"varianceIds[]":k.value||[]},server:!1});B.value&&console.error("Error: in fetching product details",B.value);const A=m(()=>oe().buildSinglePage(L("compare.title"))),w=m(()=>j.value!=="success"),I=m(()=>{var t;return(t=C.value)==null?void 0:t.variances}),R=m(()=>{var t;return(t=C.value)==null?void 0:t.attributes}),D=(t,r)=>{var f;return!t||!r?"--------":((f=t.find(o=>(o==null?void 0:o.varianceId)===r))==null?void 0:f.name)??"--------"},O=t=>{if(x.removeProduct(t.varianceId),x.isEmpty)return $.push(b("/"));const r=x.products;$.push(b(`/compare/${r.join("/")}`))};return(t,r)=>{const f=te,o=ae,T=ce,q=de,N=ee,be=re,H=ne,W=le;return a(),n(d,null,[s(f,{links:c(A),loading:c(w),class:"!border-0 !shadow-none"},null,8,["links","loading"]),s(W,{class:"mt-6"},{default:y(()=>[s(T,null,{default:y(()=>[c(w)?(a(),U(o,{key:0,class:"w-52 h-6"})):(a(),n("span",_e,_(t.$t("compare.title")),1))]),_:1}),s(H,{class:"flex flex-col gap-6"},{default:y(()=>{var V;return[c(w)?(a(),n("div",pe,[l("div",me,[r[0]||(r[0]=l("div",{class:"flex w-full flex-col items-center gap-2"},null,-1)),l("div",fe,[s(o,{class:"w-2/4 h-52"}),s(o,{class:"w-4/5 h-7"}),s(o,{class:"w-4/5 h-4"})]),l("div",ge,[s(o,{class:"w-2/4 h-52"}),s(o,{class:"w-4/5 h-7"}),s(o,{class:"w-4/5 h-4"})])]),(a(!0),n(d,null,h(Array(6),(e,u)=>(a(),n("div",{key:`${e}-${u}`,class:"grid grid-cols-3 w-full p-2 gap-2"},[s(o,{class:"w-full h-10"}),s(o,{class:"w-full h-10"}),s(o,{class:"w-full h-10"})]))),128))])):(a(),n(d,{key:1},[l("div",{class:S(["grid auto-rows-auto",`grid-cols-${1+((V=c(k))==null?void 0:V.length)}`])},[r[1]||(r[1]=l("div",null,null,-1)),(a(!0),n(d,null,h(c(I),(e,u)=>{var p,i,g,z,E;return a(),n("div",{key:`${e==null?void 0:e.varianceId}-${u}`,class:"text-center px-4 w-full flex flex-col items-center"},[l("div",he,[s(q,{src:(g=(i=(p=e==null?void 0:e.media)==null?void 0:p.gallery)==null?void 0:i[0])==null?void 0:g.src,alt:e.varianceName,class:"w-32 h-32 object-contain mx-auto",width:"170",height:"170"},null,8,["src","alt"]),l("button",{class:"absolute start-0 top-0 bg-gray-100 rounded p-1 flex items-center justify-center",onClick:Ce=>O(e)},[s(N,{name:"lucide:trash-2",size:"15px",class:"text-gray-400"})],8,ve)]),l("h3",xe,_(e==null?void 0:e.name)+", "+_(e==null?void 0:e.varianceName),1),l("span",ke,_(c(P)((E=(z=e==null?void 0:e.stock)==null?void 0:z.price)==null?void 0:E.value)),1)])}),128))],2),l("div",we,[(a(!0),n(d,null,h(c(R),(e,u)=>{var p;return a(),n("div",{key:`attributes-${u}`,class:S(["grid auto-rows-auto w-full p-2",[`grid-cols-${1+((p=c(k))==null?void 0:p.length)}`,u%2===0&&"bg-[#F3F9FC]"]])},[l("div",ye,[s(N,{name:`ui:${e.icon??"colors"}`,size:"25px"},null,8,["name"]),l("span",$e,_(e.attribute),1)]),(a(!0),n(d,null,h(c(I),(i,g)=>(a(),n("div",{key:`key${g}-${i==null?void 0:i.varianceId}`,class:"text-center py-2"},_(D(e==null?void 0:e.attributeOptions,i.varianceId)),1))),128))],2)}),128))]),X("",!0)],64))]}),_:1})]),_:1})],64)}}});export{He as default};
