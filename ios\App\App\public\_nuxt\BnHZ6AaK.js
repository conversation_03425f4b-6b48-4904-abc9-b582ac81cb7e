import{i as f,d as o,e as t,t as s,$ as r,a0 as d,o as l,a as _,g as m,_ as p}from"./C3gyeM1K.js";const x={class:"bg-gray-100 col-span-3 rounded-lg overflow-hidden p-6 shadow-md"},h={class:"faq w-full flex flex-col gap-10"},u={class:"w-full flex justify-center"},g={class:"text-lg font-bold"},q={class:"w-full flex gap-6 justify-evenly items-center max-md:flex-wrap"},y={class:"flex bg-primary-600 rounded-lg text-white p-2 shadow-lg"},v={class:"flex flex-col items-center justify-center text-center gap-1"},w={class:"text-lg font-bold text-gray-700"},b={class:"text-base font-normal text-gray-500 leading-tight"},k=f({__name:"faq",setup($){const n=[{icon:"ui:faq-call",title:"home.faq-call-title",text:"home.faq-call-text"},{icon:"ui:faq-tracking",title:"home.faq-track-title",text:"home.faq-track-text"},{icon:"ui:faq-replace",title:"home.faq-replace-title",text:"home.faq-replace-text"},{icon:"ui:faq-best-sel",title:"home.faq-best-title",text:"home.faq-best-text"},{icon:"ui:faq-payments",title:"home.faq-payment-title",text:"home.faq-payment-text"}];return(e,j)=>{const c=m;return l(),o("div",x,[t("div",h,[t("div",u,[t("span",g,s(e.$t("home.faq-title")),1)]),t("div",q,[(l(),o(r,null,d(n,(a,i)=>t("div",{key:`faq-${i}`,class:"col-span-1 flex flex-col items-center justify-center gap-4 max-w-56"},[t("div",y,[_(c,{name:a.icon,size:"40px"},null,8,["name"])]),t("div",v,[t("span",w,s(e.$t(a.title)),1),t("span",b,s(e.$t(a.text)),1)])])),64))])])])}}}),I=p(k,[["__scopeId","data-v-ac024d06"]]);export{I as default};
