import u from"./BtuWRPOH.js";import{u as m}from"./BBuxlZld.js";import{i as c,u as p,r as l,n as a,d as _,p as f,q as r,a as d,o as h,z as v,am as x}from"./C3gyeM1K.js";import{q as k}from"./CRC5xmnh.js";import"./DAA1LH-g.js";import"./JNWsvxxg.js";import"./jAU0Cazi.js";import"./BsHCAE3W.js";import"./CuUhnQhf.js";const w={key:0,class:"flex flex-col w-full"},z=c({__name:"navigation",setup(g){const o=m("(min-width: 600px)"),s=p(),t=l(null),n=k(()=>{if(o.value){const e=v();x(e("/my/profile"))}},500);return a(()=>s.user,e=>{t.value=e},{immediate:!0,deep:!0}),a(()=>o,()=>{n()},{immediate:!0,deep:!0}),(e,y)=>{const i=u;return r(t)?(h(),_("div",w,[d(i,{user:r(t)},null,8,["user"])])):f("",!0)}}});export{z as default};
