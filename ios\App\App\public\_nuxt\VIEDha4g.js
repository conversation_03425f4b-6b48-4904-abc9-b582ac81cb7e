const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as te,X as se,u as ne,l as f,r as _,j as re,d as u,a as r,c as R,p as k,w as a,q as n,$ as P,Y as le,o as i,e as l,a0 as j,W as ae,t as y,ag as ie,g as me,aM as de,f as ue,h as S,Z as D,a2 as E,ah as ce,L as pe,ai as fe,_ as _e}from"./C3gyeM1K.js";import{Skeleton as ye}from"./BaodNYLQ.js";import{_ as ve}from"./JNWsvxxg.js";import{_ as he}from"./ziqIZt9y.js";import{_ as ge}from"./BoqtTPyX.js";import{_ as xe}from"./D6u6BhOR.js";import{_ as we}from"./XInyCH8Q.js";import{_ as ke}from"./C0H2RQFE.js";import"./jAU0Cazi.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";import"./BzSOFl6X.js";import"./DSkQD3L1.js";import"./BK9Yhb3N.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./ZoTeemxs.js";import"./CjR7N8Rt.js";import"./CvMKnfiC.js";import"./CvhKjyCU.js";import"./BsYnu6hU.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./FUKBWQwh.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";const be=ie(()=>ce(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(m=>m.default||m)),$e={class:"grid grid-cols-2 w-full gap-6 max-md:grid-cols-1"},Ce=["disabled","onClick"],Ie={class:"flex gap-2 border-b py-2 px-3 items-center"},Ne={class:"font-bold text-base flex-grow"},Me={class:"flex w-20 h-5 justify-end"},Pe={class:"flex w-full items-center text-sm gap-4 px-4 pb-2"},Se={key:0,class:"font-semibold text-gray-700"},Le={key:0,class:"flex w-full gap-2 items-center"},Ae={class:"flex px-4 pt-8"},Be=te({__name:"payment",props:{order:{}},emits:["set:flow"],async setup(m,{emit:O}){var z;let g,L;const A=O,{data:b,error:B,status:$}=([g,L]=se(()=>{var e;return le(`/orders/${(e=m.order)==null?void 0:e.orderId}/payment-methods`)}),g=await g,L(),g);B.value&&console.log("error on fetching shipping list",B.value);const U=ne(),T=f(()=>b==null?void 0:b.value),H=f(()=>($==null?void 0:$.value)!=="success"),v=_(null),q=f(()=>{var e;return((e=m.order)==null?void 0:e.status)!=="draft"}),C=f(()=>!!U.isLoggedIn),W=f(()=>{var e;return(e=T.value)==null?void 0:e.find(o=>o.paymentMethodId===v.value)}),I=_(!1),N=_(!1),c=_(null),X=_(((z=m.order)==null?void 0:z.paymentMethod)||null),V=_(null),Y=async e=>{v.value=e;const{$api:o}=E();return o(`/orders/${m.order.orderId}/set-payment`,{method:"POST",body:{paymentMethodId:v.value}}).then(t=>{var d,p,h,x;if(X.value=t,((d=t==null?void 0:t.paymentsMethod)==null?void 0:d.module)==="hyperpay"){debugger;N.value=!0,c.value={email:(p=t==null?void 0:t.address)==null?void 0:p.email,firstName:(h=t==null?void 0:t.address)==null?void 0:h.firstName,lastName:(x=t==null?void 0:t.address)==null?void 0:x.lastName}}})},Z=async()=>{var o,t,d;const{$api:e}=E();return e(`/orders/${m.order.orderId}/set-payment-params`,{method:"POST",body:{email:((o=c.value)==null?void 0:o.email)||"<EMAIL>",firstName:(t=c.value)==null?void 0:t.firstName,lastName:(d=c.value)==null?void 0:d.lastName}}).then(p=>{V.value=p,pe(async()=>{await fe().fetchMounted(),I.value=!0})})},G=async()=>W.value.module==="hyperpay"?Z():A("set:flow",4),J=re(),K=f(()=>`${J.path}?auth=login`);return(e,o)=>{const t=ye,d=be,p=me,h=ue,x=de("i18n-t"),Q=ve,M=ge,ee=he,oe=xe;return i(),u(P,null,[r(Q,{class:"flex-grow p-4"},{default:a(()=>[l("div",$e,[n(H)?(i(!0),u(P,{key:0},j(Array(4),(s,w)=>(i(),u("div",{key:w,class:"flex w-full max-w-full border rounded-lg p-2 gap-2 flex-col"},[r(t,{class:"w-full h-4"}),r(t,{class:"w-full h-7"})]))),128)):(i(!0),u(P,{key:1},j(n(T),s=>{var w,F;return i(),u("div",{key:s.shippingCarrierId,class:"flex w-full flex-col gap-2"},[l("button",{disabled:n(q)||s.module==="wallet"&&!n(C),class:ae(["flex flex-col border rounded-lg gap-2 cursor-pointer text-start disabled:cursor-not-allowed shadow-sm",{active:s.paymentMethodId===n(v),"bg-gray-200 opacity-50":s.module==="wallet"&&!n(C)}]),onClick:Te=>Y(s.paymentMethodId)},[l("div",Ie,[o[5]||(o[5]=l("div",{class:"check flex rounded-full w-5 h-5 border border-gray-300 p-0.5"},[l("div",{class:"child flex w-full h-full rounded-full"})],-1)),l("div",Ne,y(s.name),1),l("div",Me,[r(d,{src:(F=(w=s==null?void 0:s.media)==null?void 0:w.logo)==null?void 0:F.preview},null,8,["src"])])]),l("div",Pe,[s.module?(i(),u("span",Se,y(e.$t(`checkout.payment-method-${s.module}`))+": ",1)):k("",!0)])],10,Ce),s.module==="wallet"&&!n(C)?(i(),u("div",Le,[r(p,{name:"ui:warning",size:"18px"}),r(x,{keypath:"wallet.auth-user-note"},{login:a(()=>[r(h,{to:n(K),class:"text-primary-600 underline px-0.5"},{default:a(()=>[S(y(e.$t("auth.log-in")),1)]),_:1},8,["to"])]),_:1})])):k("",!0)])}),128))])]),_:1}),r(ee,{class:"gap-4 justify-end"},{default:a(()=>[r(M,{variant:"outline",class:"sm:min-w-24 xs:min-w-1/2",onClick:o[0]||(o[0]=D(()=>A("set:flow",2),["prevent"]))},{default:a(()=>[S(y(e.$t("form.prev")),1)]),_:1}),r(M,{disabled:!n(v),class:"sm:min-w-24 xs:min-w-1/2",onClick:o[1]||(o[1]=D(()=>G(),["prevent"]))},{default:a(()=>[S(y(e.$t("form.next")),1)]),_:1},8,["disabled"])]),_:1}),n(N)?(i(),R(oe,{key:0,user:n(c),onClose:o[2]||(o[2]=s=>N.value=!1),onUpdate:o[3]||(o[3]=s=>c.value=s)},null,8,["user"])):k("",!0),n(I)?(i(),R(ke,{key:1,dismissible:!1,onClose:o[4]||(o[4]=s=>I.value=!1)},{body:a(()=>[l("div",Ae,[r(we,{params:n(V),"call-back-url":`checkout/${e.order.orderId}/4`},null,8,["params","call-back-url"])])]),footer:a(()=>[r(M,{variant:"outline",class:"sm:min-w-24 xs:min-w-full"},{default:a(()=>[l("span",null,y(e.$t("form.cancel")),1)]),_:1})]),_:1})):k("",!0)],64)}}}),po=_e(Be,[["__scopeId","data-v-7feb6b30"]]);export{po as default};
