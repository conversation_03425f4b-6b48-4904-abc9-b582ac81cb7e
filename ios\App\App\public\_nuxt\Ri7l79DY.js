import{i as m,c as p,w as e,o as f,a,h as u,t as c,e as o,g as x}from"./C3gyeM1K.js";import{_ as v}from"./BoqtTPyX.js";import{_ as w}from"./C0H2RQFE.js";const $={class:"flex w-full flex-col justify-center items-center gap-4 py-4 px-6"},g={class:"text-center w-80"},y={class:"font-bold"},N=m({__name:"save-success",emits:["close:address"],setup(B,{emit:r}){const t=r;return(n,s)=>{const i=x,_=v,d=w;return f(),p(d,{size:"max-w-lg",onClose:s[1]||(s[1]=l=>t("close:address"))},{body:e(()=>[o("div",$,[a(i,{name:"ui:success-save-address",size:"186px"}),o("div",g,[o("span",y,c(n.$t("form.save-address-success")),1)])])]),footer:e(()=>[a(_,{class:"w-full",onClick:s[0]||(s[0]=l=>t("close:address"))},{default:e(()=>[u(c(n.$t("form.finish")),1)]),_:1})]),_:1})}}});export{N as _};
