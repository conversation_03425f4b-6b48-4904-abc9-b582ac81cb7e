const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as b,k as v,l as C,r as k,d as l,a as s,q as _,w as c,$ as d,o as r,e,ag as $,t as o,a0 as j,W as z,g as B,ah as I}from"./C3gyeM1K.js";import{_ as L,u as q}from"./BD9sJOps.js";import{_ as E}from"./DAA1LH-g.js";import{_ as N}from"./JNWsvxxg.js";import{_ as P}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const V=$(()=>I(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(n=>n.default||n)),A={class:"absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0"},D={class:"font-bold text-2xl max-sm:text-sm"},S={class:"text-xl font-semibold max-sm:text-xs"},F={class:"flex flex-col w-1/2 p-4 rounded-lg shadow gap-2 border max-sm:w-full"},H={class:"flex items-center gap-2"},O={class:"w-7 h-7 bg-primary-500 text-white flex items-center justify-center rounded-full"},R={class:"text-base font-semibold"},T={class:"text-sm text-gray-600"},W={class:"flex h-52 w-1/2 bg-sky-50 justify-center items-center rounded-lg shadow max-sm:hidden"},tt=b({__name:"how-to-buy",setup(n){const{t}=v(),u=C(()=>q().buildSinglePage(t("how.title"))),p=k([{title:t("how.title-1"),text:t("how.text-1"),icon:"ui:search"},{title:t("how.title-2"),text:t("how.text-2"),icon:"ui:quantity"},{title:t("how.title-3"),text:t("how.text-3"),icon:"ui:cart"},{title:t("how.title-4"),text:t("how.text-4"),icon:"ui:pin"},{title:t("how.title-5"),text:t("how.text-5"),icon:"ui:paymnet-method"}]);return(m,G)=>{const x=L,h=V,f=P,w=B,g=N,y=E;return r(),l(d,null,[s(x,{links:_(u),class:"!border-0 !shadow-none"},null,8,["links"]),s(y,{class:"flex flex-col w-full h-full gap-2 my-6"},{default:c(()=>[s(f,{class:"relative text-center justify-center gap-4 text-white rounded-lg p-0"},{default:c(()=>[s(h,{src:"/images/how-to-use/lg.png",sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw",srcset:"/images/how-to-use/sm.png 640w,/images/how-to-use/md.png 1024w, /images/why-action/lg.png 1920w",width:"1920",height:"1080",format:"webp",quality:"80",class:"rounded-lg object-cover"}),e("div",A,[e("h1",D,o(m.$t("how.title")),1),e("p",S,o(m.$t("how.sub-title")),1)])]),_:1}),s(g,{class:"flex flex-col gap-6 py-12"},{default:c(()=>[(r(!0),l(d,null,j(_(p),(a,i)=>(r(),l("div",{key:i,class:z(["flex w-full justify-between gap-4 items-center",{"flex-row-reverse":i%2===0}])},[e("div",F,[e("div",H,[e("span",O,o(i+1),1),e("span",R,o(a.title),1)]),e("span",T,o(a.text),1)]),e("div",W,[s(w,{name:a.icon,size:"75px"},null,8,["name"])])],2))),128))]),_:1})]),_:1})],64)}}});export{tt as default};
