import{_ as a}from"./L0mwwrGq.js";import{i as c,j as p,l as i,c as n,w as l,o,p as _,q as s,al as m,u as d}from"./C3gyeM1K.js";const y=c({__name:"index",setup(f){const r=p(),t=i(()=>{const e=d();if(e!=null&&e.isLoggedIn)return null;switch(r.query.auth){case"signup":return"AuthSignup";case"verify-otp":return"AuthVerifyOtp";case"reset-password":return"AuthResetPassword";case"forgot-password":return"AuthForgotPassword";case"login":return"AuthLogin";default:return null}});return(e,h)=>{const u=a;return o(),n(u,null,{default:l(()=>[s(t)?(o(),n(m(s(t)),{key:0})):_("",!0)]),_:1})}}});export{y as default};
