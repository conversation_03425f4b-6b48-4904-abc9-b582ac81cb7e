import{aB as b,r as y,m as w,n as q,R as k,l as B,d as P,o as $,s as d,p as z,q as u,a1 as E}from"./C3gyeM1K.js";import{u as L,o as R,a as t,s as l,n as h,d as _,l as n}from"./C0giEChS.js";import{u as A}from"./Bzz9boR6.js";import"./BsYnu6hU.js";import"./CvhKjyCU.js";R({app_id:l(),api_base:t(_([n("https://api-iam.intercom.io"),n("https://api-iam.eu.intercom.io"),n("https://api-iam.au.intercom.io")])),name:t(l()),email:t(l()),user_id:t(l()),alignment:t(_([n("left"),n("right")])),horizontal_padding:t(h()),vertical_padding:t(h())});function N(m){return L("intercom",i=>({scriptInput:{src:b("https://widget.intercom.io/widget",(i==null?void 0:i.app_id)||"")},schema:void 0,scriptOptions:{use(){return{Intercom:window.Intercom}}},clientInit:()=>{window.intercomSettings=i}}),m)}const T={__name:"ScriptIntercom",props:{appId:{type:String,required:!0},apiBase:{type:String,required:!1},name:{type:String,required:!1},email:{type:String,required:!1},userId:{type:String,required:!1},alignment:{type:String,required:!1},horizontalPadding:{type:Number,required:!1},verticalPadding:{type:Number,required:!1},trigger:{type:[String,Array,Boolean],required:!1,default:"click"}},emits:["ready","error"],setup(m,{expose:i,emit:v}){const e=m,g=v,f=y(null),p=A({trigger:e.trigger,el:f}),o=y(!1),c=N({app_id:e.appId,app_base:e.apiBase,name:e.name,email:e.email,user_id:e.userId,alignment:e.alignment,horizontal_padding:e.horizontalPadding,vertical_padding:e.verticalPadding,scriptOptions:{trigger:p}}),{status:s,onLoaded:S}=c;e.trigger==="click"&&S(r=>{r.Intercom("show")}),i({intercom:c});let a;w(()=>{q(s,r=>{r==="loading"?(a=new MutationObserver(()=>{document.getElementById("intercom-frame")&&(o.value=!0,g("ready",c),a.disconnect())}),a.observe(document.body,{childList:!0,subtree:!0})):r==="error"&&g("error")})}),k(()=>{a==null||a.disconnect()});const I=B(()=>({style:{display:o.value?"none":"block",bottom:`${e.verticalPadding||20}px`,[e.alignment||"right"]:`${e.horizontalPadding||20}px`},...p instanceof Promise?p.ssrAttrs||{}:{}}));return(r,O)=>($(),P("div",E({ref_key:"rootEl",ref:f},I.value),[d(r.$slots,"default",{ready:o.value}),u(s)==="awaitingLoad"?d(r.$slots,"awaitingLoad",{key:0}):u(s)==="loading"||!o.value?d(r.$slots,"loading",{key:1}):u(s)==="error"?d(r.$slots,"error",{key:2}):z("",!0)],16))}};export{T as default};
