import{Skeleton as w}from"./BaodNYLQ.js";import{i as k,j as $,l as i,c as l,w as a,o as r,a as e,d,f as h,W as m,q as o,h as _,t as u,$ as C,p as f}from"./C3gyeM1K.js";import{_ as B}from"./DAA1LH-g.js";import{_ as N}from"./JNWsvxxg.js";import{_ as L}from"./BsHCAE3W.js";import S from"./CdAhpRi0.js";import{_ as V}from"./C5xuNBUQ.js";const q={key:0,class:"flex py-4 w-1/2 gap-6 px-2 -bottom-[2px] relative"},W=k({__name:"index",props:{product:{},loading:{type:Boolean,default:!1}},setup(D){const s=$(),g=i(()=>`${s.path}?tab=overview#details`),x=i(()=>`${s.path}?tab=rating#details`),n=i(()=>{var t;return((t=s.query)==null?void 0:t.tab)??"overview"});return(t,T)=>{const p=w,c=h,b=L,v=N,y=B;return r(),l(y,{id:"details"},{default:a(()=>[e(b,{class:"flex items-center py-0 px-0 w-full flex-row gap-x-4 border-b-2 border-gray-200"},{default:a(()=>[t.loading?(r(),d("div",q,[e(p,{class:"w-1/5 h-8"}),e(p,{class:"w-1/5 h-8"})])):(r(),d(C,{key:1},[e(c,{to:o(g),class:m(["text-xl font-bold text-gray-500 border-b-2 leading-[60px] relative -bottom-[2px] px-5",{"text-primary-600 border-primary-600":o(n)=="overview"}])},{default:a(()=>[_(u(t.$t("product.overview-title")),1)]),_:1},8,["to","class"]),e(c,{to:o(x),class:m(["text-xl font-bold text-gray-500 border-b-2 leading-[60px] relative -bottom-[2px] px-5",{"text-primary-600 border-primary-600":o(n)=="rating"}])},{default:a(()=>[_(u(t.$t("product.client-rating-title")),1)]),_:1},8,["to","class"])],64))]),_:1}),e(v,{class:"p-0"},{default:a(()=>[o(n)=="overview"?(r(),l(S,{key:0,product:t.product,loading:t.loading},null,8,["product","loading"])):f("",!0),o(n)=="rating"?(r(),l(V,{key:1,product:t.product,loading:t.loading},null,8,["product","loading"])):f("",!0)]),_:1})]),_:1})}}});export{W as _};
