import{i as T,ai as A,r as p,l as M,d as c,e as t,a as l,w as u,W as k,q as s,o as n,t as a,g as U,p as C,a4 as E,Z as f,a5 as K,K as L,c as $,h as x}from"./C3gyeM1K.js";import{_ as O}from"./BoqtTPyX.js";import{_ as W}from"./DAA1LH-g.js";import{u as Z}from"./idjCP6w_.js";const G={key:0,class:"flex w-full flex-col gap-2"},H={class:"flex w-full justify-between items-center"},J={class:"text-2xl font-bold"},Q={class:"flex items-center gap-2"},X={class:"text-sm font-normal"},Y={key:1,class:"grid grid-cols-2 gap-2 w-full"},tt={class:"text-3xl text-primary-600 font-bold"},et={key:0,class:"col-span-1 flex items-start justify-between"},st={class:"flex gap-2 items-center text-md"},ot={class:"text-gray-600"},at={class:"text-orange-400 font-medium"},nt={class:"flex"},lt={class:"bg-orange-400 text-white text-xs font-semibold px-4 py-1 rounded-full"},ct={class:"col-span-1 flex items-center justify-end gap-6"},it={id:"price-section",class:"grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"},rt={class:"flex flex-col w-full gap-6 mt-4"},dt={key:0,class:"flex w-full justify-between gap-2"},xt=T({__name:"payment-card-mobile",props:{product:{},loading:{type:Boolean}},emits:["confirm:add-to-cart","product:pay-now"],setup(i,{emit:I}){const _=I,{hasStock:v,discountPercent:j,discountAmount:w,priceFormatted:B,offerPriceFormatted:S,discountAmountFormatted:V,stock:q,productId:N}=i.product,h=A(),o=p(1),y=p(null),g=p(!1),R=M(()=>h.hasCart(N)),z=()=>{var e;o.value<((e=q.value)==null?void 0:e.maxPerUser)&&(o.value+=1)},D=()=>{o.value>1&&(o.value=o.value-1)};Z(y,async e=>{g.value=e.boundingClientRect.top<0},"0px",!1);const F=async()=>{await h.addToList({productId:i.product.productId,quantity:o.value,varianceId:i.product.variance.varianceId}),_("confirm:add-to-cart",i.product)};return(e,r)=>{const d=U,m=O,P=W;return n(),c("div",{ref_key:"stickyRef",ref:y,class:"sm:hidden w-full relative"},[t("div",{class:k(["flex flex-col gap-4 w-full transition-all duration-300",[s(g)?"fixed bottom-0 left-0 w-full bg-white z-50 px-4 drop-shadow-2xl pb-8":"hidden"]])},[l(P,{class:"flex flex-col shadow-md p-4 max-sm:p-0 max-sm:border-0 max-sm:mt-4"},{default:u(()=>[s(v)?(n(),c("div",Y,[t("div",{class:k([s(w)?"col-span-2":"cols-span-1"])},[t("span",tt,a(s(B)),1)],2),s(w)?(n(),c("div",et,[t("div",st,[t("span",ot,a(s(S)),1),t("span",at,a(e.$t("product.discount-amount-title",{amount:s(V)})),1)]),t("div",nt,[t("span",lt,a(e.$t("product.card-discount",{amount:s(j)})),1)])])):C("",!0),t("div",ct,[t("div",it,[t("button",{class:"border-e border-gray-200 w-7 items-center flex justify-center h-7",onClick:f(z,["prevent"])},[l(d,{name:"lucide:plus",height:"12px",width:"12px"})]),E(t("input",{"onUpdate:modelValue":r[0]||(r[0]=b=>L(o)?o.value=b:null),name:"quantity",type:"number",readonly:"",class:"no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs",max:2},null,512),[[K,s(o)]]),t("button",{class:"border-s border-gray-200 w-7 h-full items-center flex justify-center",onClick:f(D,["prevent"])},[l(d,{name:"lucide:minus",width:"12px"})])])])])):(n(),c("div",G,[t("div",H,[t("div",J,a(e.$t("product.out-stock")),1),l(d,{name:"ui:out-stock",size:"25px"})]),t("div",Q,[l(d,{name:"ui:bell-ringing"}),t("span",X,a(e.$t("product.let-me-know-when-available")),1)])])),t("div",rt,[s(v)?(n(),c("div",dt,[l(m,{class:"w-full",onClick:r[1]||(r[1]=f(b=>_("product:pay-now",s(o)),["prevent"]))},{default:u(()=>[x(a(e.$t("product.pay-now")),1)]),_:1}),s(R)?C("",!0):(n(),$(m,{key:0,variant:"outline",class:"w-full",onClickOnce:F},{default:u(()=>[x(a(e.$t("product.add-to-cart")),1)]),_:1}))])):(n(),$(m,{key:1,class:"w-full mt-4"},{default:u(()=>[x(a(e.$t("product.let-me-know")),1)]),_:1}))])]),_:1})],2)],512)}}});export{xt as _};
