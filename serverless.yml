# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: actionjo
# "app" enables Serverless Framework Dashboard features and sharing them with other Services.
app: action-v21-frontend-prod-app
service: action-v21-frontend
frameworkVersion: ~4.14.3

provider:
  name: aws
  region: eu-west-1
  runtime: nodejs22.x
  vpc:
     securityGroupIds:
        - sg-0c6b6715fcdd700c5
     subnetIds:
        - subnet-060dd3bb1ec187363 # copilot-v2-prod-priv0
        - subnet-0596eb12436cec3e5 # copilot-v2-prod-priv1
  stage: prod
  # Add API Gateway configuration
  httpApi:
    cors: true
    payload: '2.0'

package:
  patterns:
    - '!**'
    - '.output/**'               # include only <PERSON><PERSON>’s build output :contentReference[oaicite:1]{index=1}

functions:
  app:
    handler: .output/server/index.handler
    url:                          # use object syntax to configure the Function URL :contentReference[oaicite:2]{index=2}
      cors:                       # allow any origin to invoke (public CORS) :contentReference[oaicite:4]{index=4}
        allowCredentials: false
    events:
      - httpApi: '*' # Catch all routes and forward to the Nuxt app
    timeout: 30
    environment:
      BASE_URL: https://stg.action.jo/api/v1/
      APP_URL: https://staging.action.jo/
      CDN_BASE_URL: https://assets.action.jo
      S3_BUCKET: action-assets-prod
      NODE_ENV: production
      DEBOUNCE_TIME: 2001
      HYPERPAY_URL: https://test.oppwa.com
      ENABLE_CACHE: true
      DEBUG_CACHE: true
      CACHE_TOKEN: zoRxkweXVc42FRZkKwDxbmHdrbYrFEq9PtPqvr4LZUuatvgXbLYYCHsSMj0qvGJg
      SENTRY_DSN: https://<EMAIL>/4509337280839680
      SENTRY_ENVIRONMENT: staging
