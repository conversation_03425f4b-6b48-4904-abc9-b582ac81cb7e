import{i as p,k as d,l as m,c as _,o as f,q as t,W as h,w,s as x,a as v,e as C,g as b}from"./C3gyeM1K.js";import{u as k}from"./CiqmSqHP.js";import{c as N}from"./jAU0Cazi.js";import{_ as g}from"./BoqtTPyX.js";const B=p({__name:"CarouselNext",props:{class:{}},setup(a){const e=a,{orientation:l,canScrollNext:n,scrollNext:r,...c}=k(),{locale:i}=d(),u=m(()=>i.value==="ar");return window.scroll={...c},(s,o)=>{const S=b;return f(),_(t(g),{disabled:!t(n),class:h(t(N)("touch-manipulation absolute h-8 w-8 p-0 shadow",t(l)==="horizontal"?"right-0 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",t(u)?"rotate-180 right-auto left-0":"",e.class)),variant:"white",onClick:t(r)},{default:w(()=>[x(s.$slots,"default",{},()=>[v(S,{name:"lucide:chevron-right",class:"h-4 w-4 text-current"}),o[0]||(o[0]=C("span",{class:"sr-only"},"Next Slide",-1))])]),_:3},8,["disabled","class","onClick"])}}}),I=p({__name:"CarouselPrevious",props:{class:{}},setup(a){const e=a,{orientation:l,canScrollPrev:n,scrollPrev:r}=k(),{locale:c}=d(),i=m(()=>c.value==="ar");return(u,s)=>{const o=b;return f(),_(t(g),{disabled:!t(n),class:h(t(N)("touch-manipulation absolute h-8 w-8  p-0 shadow",t(l)==="horizontal"?"left-0 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",t(i)?"rotate-180 left-auto right-0":"",e.class)),variant:"white",onClick:t(r)},{default:w(()=>[x(u.$slots,"default",{},()=>[v(o,{name:"lucide:chevron-left",class:"h-4 w-4 text-current"}),s[0]||(s[0]=C("span",{class:"sr-only"},"Previous Slide",-1))])]),_:3},8,["disabled","class","onClick"])}}});export{I as _,B as a};
