import{b as O}from"./CRC5xmnh.js";import{h as S,b as U}from"./BBuxlZld.js";import{r as d,n as E,ar as b,l as C,L as P,i as L,T as w,H as x,A as D}from"./C3gyeM1K.js";import{r as F}from"./ClItKS3j.js";function _(r,e){const u=d(r);function f(l){return e[u.value][l]??u.value}return{state:u,dispatch:l=>{u.value=f(l)}}}function R(r,e){var T;const u=d({}),f=d("none"),h=d(r),l=r.value?"mounted":"unmounted";let m;const i=((T=e.value)==null?void 0:T.ownerDocument.defaultView)??S,{state:c,dispatch:o}=_(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),s=t=>{var n;if(O){const p=new CustomEvent(t,{bubbles:!1,cancelable:!1});(n=e.value)==null||n.dispatchEvent(p)}};E(r,async(t,n)=>{var A;const p=n!==t;if(await P(),p){const M=f.value,v=y(e.value);t?(o("MOUNT"),s("enter"),v==="none"&&s("after-enter")):v==="none"||v==="undefined"||((A=u.value)==null?void 0:A.display)==="none"?(o("UNMOUNT"),s("leave"),s("after-leave")):n&&M!==v?(o("ANIMATION_OUT"),s("leave")):(o("UNMOUNT"),s("after-leave"))}},{immediate:!0});const a=t=>{const n=y(e.value),p=n.includes(t.animationName),A=c.value==="mounted"?"enter":"leave";if(t.target===e.value&&p&&(s(`after-${A}`),o("ANIMATION_END"),!h.value)){const M=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",m=i==null?void 0:i.setTimeout(()=>{var v;((v=e.value)==null?void 0:v.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=M)})}t.target===e.value&&n==="none"&&o("ANIMATION_END")},N=t=>{t.target===e.value&&(f.value=y(e.value))},g=E(e,(t,n)=>{t?(u.value=getComputedStyle(t),t.addEventListener("animationstart",N),t.addEventListener("animationcancel",a),t.addEventListener("animationend",a)):(o("ANIMATION_END"),m!==void 0&&(i==null||i.clearTimeout(m)),n==null||n.removeEventListener("animationstart",N),n==null||n.removeEventListener("animationcancel",a),n==null||n.removeEventListener("animationend",a))},{immediate:!0}),I=E(c,()=>{const t=y(e.value);f.value=c.value==="mounted"?t:"none"});return b(()=>{g(),I()}),{isPresent:C(()=>["mounted","unmountSuspended"].includes(c.value))}}function y(r){return r&&getComputedStyle(r).animationName||"none"}const H=L({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(r,{slots:e,expose:u}){var o;const{present:f,forceMount:h}=w(r),l=d(),{isPresent:m}=R(f,l);u({present:m});let i=e.default({present:m.value});i=F(i||[]);const c=x();if(i&&(i==null?void 0:i.length)>1){const s=(o=c==null?void 0:c.parent)!=null&&o.type.name?`<${c.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${s}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(a=>`  - ${a}`).join(`
`)].join(`
`))}return()=>h.value||f.value||m.value?D(e.default({present:m.value})[0],{ref:s=>{const a=U(s);return typeof(a==null?void 0:a.hasAttribute)>"u"||(a!=null&&a.hasAttribute("data-reka-popper-content-wrapper")?l.value=a.firstElementChild:l.value=a),a}}):null}});export{H as P};
