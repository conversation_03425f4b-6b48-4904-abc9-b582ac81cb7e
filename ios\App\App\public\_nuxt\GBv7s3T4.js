import{i as r,d as l,e as t,t as o,$ as d,a0 as m,o as a,a as p,g as x}from"./C3gyeM1K.js";const _={class:"col-span-3 w-full rounded-lg bg-white p-4"},u={class:"flex flex-col w-full gap-4"},g={class:"text-2xl font-semibold text-gray-700"},h={class:"text-xl font-normal text-gray-600 leading-10"},f={class:"grid grid-cols-3 w-full gap-4 max-md:grid-cols-1"},b={class:"flex p-3 rounded-lg shadow-lg bg-primary-600 text-white"},y={class:"flex flex-col flex-grow"},w={class:"text-lg text-gray-800 font-semibold"},v={class:"text-sm text-gray-500 font-normal"},N=r({__name:"about",setup($){const n=[{title:"home.about-stores-title",text:"home.about-stores-text",icon:"ui:stores"},{title:"home.about-employees-title",text:"home.about-employees-text",icon:"ui:employees"},{title:"home.about-products-title",text:"home.about-products-text",icon:"ui:products"}];return(e,k)=>{const i=x;return a(),l("div",_,[t("div",u,[t("span",g,o(e.$t("home.about-title")),1),t("p",h,o(e.$t("home.about-text")),1),t("div",f,[(a(),l(d,null,m(n,(s,c)=>t("div",{key:c,class:"flex gap-2 items-center bg-sky-50 py-2 px-2 rounded-lg"},[t("div",b,[p(i,{name:s.icon,size:"35px",class:"max-w-9"},null,8,["name"])]),t("div",y,[t("span",w,o(e.$t(s.title)),1),t("p",v,o(e.$t(s.text)),1)])])),64))])])])}}});export{N as _};
