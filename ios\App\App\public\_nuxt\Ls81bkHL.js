import{i as c,l,d as t,p as u,q as n,o as r}from"./C3gyeM1K.js";const m={class:"bg-foreground/20 flex h-4 w-6 overflow-hidden rounded-sm"},p=["src","alt","title"],i=c({__name:"flags",props:{country:{},countryName:{}},setup(s){const a=s,o=l(()=>{var e;return`https://flagcdn.com/w40/${(e=a.country)==null?void 0:e.toLowerCase()}.png`});return(e,d)=>(r(),t("span",m,[e.country&&n(o)?(r(),t("img",{key:0,src:n(o),alt:e.countryName,title:e.countryName},null,8,p)):u("",!0)]))}});export{i as _};
