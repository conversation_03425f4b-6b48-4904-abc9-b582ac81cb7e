const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as q,r as h,l as C,an as P,d as r,p as d,q as t,W as N,a as l,e as o,w as _,Z as v,a4 as O,a5 as V,K as B,o as i,ag as D,h as z,t as m,g as L,ah as $}from"./C3gyeM1K.js";import A from"./CuUhnQhf.js";import{u as E}from"./BGs9QzNZ.js";import{q as F}from"./CRC5xmnh.js";const M=D(()=>$(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(a=>a.default||a)),R={class:"flex w-full border border-gray-200 rounded-lg overflow-hidden transition-all ease-in-out duration-300"},T={class:"flex p-2 w-full h-full justify-center items-center"},U={class:"flex flex-col col-span-2 justify-evenly px-2 h-full overflow-hidden"},K={class:"flex w-full items-start justify-between"},Q={class:"text-green-600 text-lg font-bold"},S={class:"flex justify-between items-center gap-2"},W={class:"flex items-start justify-between gap-4"},Z={class:"flex flex-col"},G={key:0,class:"flex gap-2 items-center text-md"},H={class:"text-gray-600 line-through"},J={key:0,class:"flex"},X={class:"bg-orange-400 text-white text-xs font-semibold px-4 py-1 rounded-full"},Y={key:0,class:"flex items-center gap-6"},ee={id:"price-section",class:"grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"},ae=q({__name:"card",props:{product:{},viewOnly:{type:Boolean}},emits:["remove:cart","update:quantity"],setup(a,{emit:g}){const x=g,e=h(null),n=h(1),w=C(()=>{var s,c,u;return(u=(c=(s=e.value.media)==null?void 0:s.cover)==null?void 0:c[0])==null?void 0:u.src}),b=()=>{x("remove:cart",a.product.cartId)},k=()=>{var s;n.value<((s=e.value.variance.stock)==null?void 0:s.maxPerUser)&&(n.value+=1,y())},I=()=>{n.value>1&&(n.value=n.value-1,y())},y=F(async()=>{x("update:quantity",{quantity:n.value,productId:a.product.productId,varianceId:a.product.varianceId,bundleId:null})},1e3);return P(()=>{e.value=E(a.product),n.value=a.product.quantity}),(s,c)=>{const u=M,f=A,p=L;return i(),r("div",R,[t(e)?(i(),r("div",{key:0,class:N(["grid grid-cols-3 gap-1",[s.viewOnly?"h-28":"h-36"]])},[l(f,{to:`/product/${t(e).variance.slug}`,disabled:s.viewOnly,class:"flex h-full col-span-1 bg-gray-100 flex-col justify-center items-center overflow-hidden box-border p-4"},{default:_(()=>[o("div",T,[l(u,{src:t(w),alt:t(e).name,class:"object-contain min-w-28"},null,8,["src","alt"])])]),_:1},8,["to","disabled"]),o("div",U,[o("div",K,[l(f,{to:`/product/${t(e).variance.slug}`,class:"truncate-2-line text-sm font-semibold"},{default:_(()=>[z(m(t(e).name),1)]),_:1},8,["to"]),s.viewOnly?d("",!0):(i(),r("button",{key:0,class:"flex items-center",onClick:v(b,["prevent"])},[l(p,{name:"lucide:trash-2",size:"20px",class:"text-gray-400"})]))]),l(f,{class:"flex w-full gap-2",to:`/product/${t(e).variance.slug}`},{default:_(()=>[o("span",Q,m(t(e).priceFormatted),1),o("div",S,[o("div",W,[o("div",Z,[t(e).discountAmount?(i(),r("div",G,[o("span",H,m(t(e).offerPriceFormatted),1)])):d("",!0)]),t(e).discountPercent?(i(),r("div",J,[o("span",X,m(s.$t("product.card-discount",{amount:t(e).discountPercent})),1)])):d("",!0)])])]),_:1},8,["to"]),s.viewOnly?d("",!0):(i(),r("div",Y,[o("div",ee,[o("button",{class:"border-e border-gray-200 w-7 items-center flex justify-center h-7",onClick:v(k,["prevent"])},[l(p,{name:"lucide:plus",height:"12px",width:"12px"})]),O(o("input",{"onUpdate:modelValue":c[0]||(c[0]=j=>B(n)?n.value=j:null),name:"quantity",type:"number",readonly:"",class:"no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs",max:2},null,512),[[V,t(n)]]),o("button",{class:"border-s border-gray-200 w-7 h-full items-center flex justify-center",onClick:v(I,["prevent"])},[l(p,{name:"lucide:minus",width:"12px"})])])]))])],2)):d("",!0)])}}});export{ae as _};
