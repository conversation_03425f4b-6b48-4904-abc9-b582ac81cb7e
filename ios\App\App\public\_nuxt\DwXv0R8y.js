import{b}from"./BBuxlZld.js";import{r as c,l as u,m as v}from"./C3gyeM1K.js";function m(d){const r=c(),l=u(()=>{var e;return((e=r.value)==null?void 0:e.width)??0}),a=u(()=>{var e;return((e=r.value)==null?void 0:e.height)??0});return v(()=>{const e=b(d);if(e){r.value={width:e.offsetWidth,height:e.offsetHeight};const n=new ResizeObserver(t=>{if(!Array.isArray(t)||!t.length)return;const h=t[0];let i,o;if("borderBoxSize"in h){const s=h.borderBoxSize,f=Array.isArray(s)?s[0]:s;i=f.inlineSize,o=f.blockSize}else i=e.offsetWidth,o=e.offsetHeight;r.value={width:i,height:o}});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else r.value=void 0}),{width:l,height:a}}export{m as u};
