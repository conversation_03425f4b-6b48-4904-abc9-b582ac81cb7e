import{b as k}from"./BBuxlZld.js";import{n as E,ar as S}from"./C3gyeM1K.js";var D=function(r){if(typeof document>"u")return null;var a=Array.isArray(r)?r[0]:r;return a.ownerDocument.body},f=new WeakMap,v=new WeakMap,p={},h=0,b=function(r){return r&&(r.host||b(r.parentNode))},O=function(r,a){return a.map(function(t){if(r.contains(t))return t;var u=b(t);return u&&r.contains(u)?u:(console.error("aria-hidden",t,"in not contained inside",r,". Doing nothing"),null)}).filter(function(t){return!!t})},C=function(r,a,t,u){var i=O(a,Array.isArray(r)?r:[r]);p[t]||(p[t]=new WeakMap);var s=p[t],l=[],c=new Set,m=new Set(i),y=function(e){!e||c.has(e)||(c.add(e),y(e.parentNode))};i.forEach(y);var A=function(e){!e||m.has(e)||Array.prototype.forEach.call(e.children,function(n){if(c.has(n))A(n);else try{var o=n.getAttribute(u),d=o!==null&&o!=="false",w=(f.get(n)||0)+1,M=(s.get(n)||0)+1;f.set(n,w),s.set(n,M),l.push(n),w===1&&d&&v.set(n,!0),M===1&&n.setAttribute(t,"true"),d||n.setAttribute(u,"true")}catch(W){console.error("aria-hidden: cannot operate on ",n,W)}})};return A(a),c.clear(),h++,function(){l.forEach(function(e){var n=f.get(e)-1,o=s.get(e)-1;f.set(e,n),s.set(e,o),n||(v.has(e)||e.removeAttribute(u),v.delete(e)),o||e.removeAttribute(t)}),h--,h||(f=new WeakMap,f=new WeakMap,v=new WeakMap,p={})}},P=function(r,a,t){t===void 0&&(t="data-aria-hidden");var u=Array.from(Array.isArray(r)?r:[r]),i=D(r);return i?(u.push.apply(u,Array.from(i.querySelectorAll("[aria-live]"))),C(u,i,t,"aria-hidden")):function(){return null}};function B(r){let a;E(()=>k(r),t=>{t?a=P(t):a&&a()}),S(()=>{a&&a()})}export{B as u};
