import{S as p}from"./ClItKS3j.js";import{O as m,i,c as d,o as n,w as _,s as c,q as s,d as I,W as F,U as $,G as h}from"./C3gyeM1K.js";import{c as x}from"./jAU0Cazi.js";import{u as C}from"./DSkQD3L1.js";import{c as E,d as b,e as g,f as y,g as M,E as v}from"./ZoTeemxs.js";const l=Symbol();function u(){const t=m(M),o=m(l);if(!t)throw new Error("useFormField should be used within <FormField>");const{name:e}=t,r=o,a={valid:y(e),isDirty:g(e),isTouched:b(e),error:E(e)};return{id:r,name:e,formItemId:`${r}-form-item`,formDescriptionId:`${r}-form-item-description`,formMessageId:`${r}-form-item-message`,...a}}const O=i({__name:"FormControl",setup(t){const{error:o,formItemId:e,formDescriptionId:r,formMessageId:a}=u();return(f,w)=>(n(),d(s(p),{id:s(e),"aria-describedby":s(o)?`${s(r)} ${s(a)}`:`${s(r)}`,"aria-invalid":!!s(o)},{default:_(()=>[c(f.$slots,"default")]),_:3},8,["id","aria-describedby","aria-invalid"]))}}),K=i({__name:"FormItem",props:{class:{}},setup(t){const o=t,e=C();return $(l,e),(r,a)=>(n(),I("div",{class:F(s(x)("space-y-2",o.class))},[c(r.$slots,"default")],2))}}),N=i({__name:"FormMessage",setup(t){const{name:o,formMessageId:e}=u();return(r,a)=>(n(),d(s(v),{id:s(e),as:"p",name:h(s(o)),class:"text-xs font-normal text-destructive"},null,8,["id","name"]))}});export{K as _,O as a,N as b,u};
