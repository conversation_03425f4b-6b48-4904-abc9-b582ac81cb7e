const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as Q,u as W,r as X,l as n,m as Y,d as ee,a as m,c as f,p as u,w as r,q as l,aN as te,$ as se,o as i,e as t,t as s,h as p,ag as oe,Z as V,a2 as le,ai as ae,ah as re,z as ne,am as de,_ as ie}from"./C3gyeM1K.js";import{_ as ce}from"./BoqtTPyX.js";import{_ as me}from"./JNWsvxxg.js";import{_ as fe}from"./ziqIZt9y.js";import ue from"./CuUhnQhf.js";import{_ as pe}from"./C0H2RQFE.js";import{u as _e}from"./BCJZ1_ur.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";const he=oe(()=>re(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(a=>a.default||a)),xe={class:"flex flex-col w-full gap-6"},ve={class:"flex flex-col gap-2"},ge={class:"flex justify-between items-center"},ye={class:"font-bold"},we={class:"flex border rounded-lg gap-4 items-center p-4 md:max-w-sm sm:w-full"},be={class:"flex flex-col flex-grow gap-2 text-sm"},ke={class:"font-bold text-base"},$e={class:"flex w-full items-center gap-4"},Ce={class:"w-24 text-gray-600"},Me={class:"text-gray-700"},Ne={class:"flex w-full items-center gap-4"},je={class:"w-24 text-gray-600"},Le={class:"text-gray-700 max-w-56"},Se={class:"flex w-full items-center gap-4"},Be={class:"w-24 text-gray-600"},Ie={class:"text-gray-700",dir:"ltr"},Pe={class:"flex flex-col gap-2"},ze={class:"flex justify-between items-center"},Ae={class:"font-bold"},Te={class:"active flex flex-col border rounded-lg gap-2 md:max-w-sm sm:w-full"},Ve={class:"flex gap-2 border-b p-2"},Fe={class:"font-bold text-base flex-grow"},Ee={class:"font-bold"},De={class:"flex w-full items-center text-sm gap-4 px-4 pb-2"},Oe={class:"w-18 text-gray-600"},qe={class:"flex flex-col gap-2"},Re={class:"flex justify-between items-center"},Ue={class:"font-bold"},Ze={class:"active flex flex-col border rounded-lg gap-2 md:max-w-sm sm:w-full"},Ge={class:"flex gap-2 border-b py-2 px-3 items-center"},He={class:"font-bold text-base flex-grow"},Je={class:"flex w-20 h-5 justify-end"},Ke={class:"flex w-full items-center text-sm gap-4 px-4 pb-2"},Qe={class:"font-semibold text-gray-700"},We={class:"flex flex-col justify-center items-center w-full gap-4 p-4"},Xe={class:"text-base font-bold w-sm px-2 text-center"},Ye={class:"flex w-full px-4"},et=Q({__name:"reviews",props:{order:{}},emits:["set:flow"],setup(a,{emit:F}){const{priceFormat:E}=_e(),_=F,D=W(),x=X(!1),v=n(()=>{var e;return(e=a.order)==null?void 0:e.paymentMethod}),d=n(()=>{var e;return(e=a.order)==null?void 0:e.address}),O=n(()=>{var e;return(e=a.order)==null?void 0:e.shippingCarrier}),q=async()=>{const{$api:e}=le();e(`/orders/${a.order.orderId}/finalize`,{method:"POST"}).then(async()=>{x.value=!0,await ae().fetchMounted()})},g=n(()=>{var e;return`/my/orders/${(e=a.order)==null?void 0:e.orderId}/tracking`}),R=n(()=>{var e;return((e=a.order.paymentMethod)==null?void 0:e.module)!=="hyperpay"}),h=n(()=>{var e;return a.order.status!=="draft"||((e=a.order.paymentMethod)==null?void 0:e.module)==="hyperpay"}),y=n(()=>!!D.isLoggedIn),U=n(()=>{var e;return((e=a.order.paymentMethod)==null?void 0:e.module)!=="hyperpay"}),Z=()=>{if(!y.value)return;const e=ne();de(e(g.value))};return Y(()=>{var e;x.value=((e=a.order.paymentMethod)==null?void 0:e.module)==="hyperpay"}),(e,o)=>{const c=ce,w=he,G=me,H=fe,J=ue,K=pe;return i(),ee(se,null,[m(G,{class:"flex-grow p-4"},{default:r(()=>{var b,k,$,C,M,N,j,L,S,B,I,P,z,A,T;return[t("div",xe,[t("div",ve,[t("div",ge,[t("span",ye,s(e.$t("checkout.contact-title")),1),l(h)?u("",!0):(i(),f(c,{key:0,variant:"text",onClick:o[0]||(o[0]=()=>_("set:flow",1))},{default:r(()=>[p(s(e.$t("form.edit")),1)]),_:1}))]),t("div",we,[t("div",be,[t("div",ke,s(e.$t(`form.${(b=l(d))==null?void 0:b.buildingType}`)),1),t("div",$e,[t("span",Ce,s(e.$t("form.receiver-name"))+": ",1),t("span",Me,s((k=l(d))==null?void 0:k.recipientName),1)]),t("div",Ne,[t("span",je,s(e.$t("form.address")),1),t("span",Le,s([($=l(d))==null?void 0:$.fullAddress,(C=l(d))==null?void 0:C.district,(M=l(d))==null?void 0:M.street].filter(Boolean).join(", ")),1)]),t("div",Se,[t("span",Be,s(e.$t("form.phone")),1),t("span",Ie,s(["+",((N=l(d))==null?void 0:N.phone.code)+" ",(j=l(d))==null?void 0:j.phone.number].join("")),1)])])])]),o[8]||(o[8]=t("div",{class:"flex w-full border-dashed border border-gray-100 mt-4"},null,-1)),t("div",Pe,[t("div",ze,[t("span",Ae,s(e.$t("checkout.shipping-title")),1),l(h)?u("",!0):(i(),f(c,{key:0,variant:"text",onClick:o[1]||(o[1]=()=>_("set:flow",2))},{default:r(()=>[p(s(e.$t("form.edit")),1)]),_:1}))]),t("div",Te,[t("div",Ve,[o[5]||(o[5]=t("div",{class:"check flex rounded-full w-5 h-5 border p-0.5"},[t("div",{class:"child flex w-full h-full rounded-full"})],-1)),t("div",Fe,s((L=l(O))==null?void 0:L.label),1),t("div",Ee,s(l(E)((B=(S=e.order)==null?void 0:S.shippingPrice)==null?void 0:B.value)),1)]),t("div",De,[t("span",Oe,s(e.$t("form.the-delivery"))+": ",1),o[6]||(o[6]=t("span",{class:"text-gray-700"}," N/A ",-1))])])]),o[9]||(o[9]=t("div",{class:"flex w-full border-dashed border border-gray-100 mt-4"},null,-1)),t("div",qe,[t("div",Re,[t("span",Ue,s(e.$t("checkout.pay-title")),1),l(h)?u("",!0):(i(),f(c,{key:0,variant:"text",onClick:o[2]||(o[2]=()=>_("set:flow",3))},{default:r(()=>[p(s(e.$t("form.edit")),1)]),_:1}))]),t("div",Ze,[t("div",Ge,[o[7]||(o[7]=t("div",{class:"check flex rounded-full w-5 h-5 border p-0.5"},[t("div",{class:"child flex w-full h-full rounded-full"})],-1)),t("div",He,s((I=l(v))==null?void 0:I.name),1),t("div",Je,[m(w,{src:(A=(z=(P=l(v))==null?void 0:P.media)==null?void 0:z.logo)==null?void 0:A.preview},null,8,["src"])])]),t("div",Ke,[t("span",Qe,s(e.$t(`checkout.payment-method-${(T=l(v))==null?void 0:T.module}`))+": ",1)])])])])]}),_:1}),m(H,{class:"gap-4 justify-end max-sm:justify-normal mt-6"},{default:r(()=>[l(U)?(i(),f(c,{key:0,variant:"outline",class:"sm:w-32 xs:w-1/2",onClick:o[3]||(o[3]=V(()=>_("set:flow",3),["prevent"]))},{default:r(()=>[p(s(e.$t("form.prev")),1)]),_:1})):u("",!0),l(R)?(i(),f(c,{key:1,class:"sm:w-32 xs:w-1/2",disabled:l(h),onClick:o[4]||(o[4]=V(()=>q(),["prevent"]))},{default:r(()=>[p(s(e.$t("checkout.set-order")),1)]),_:1},8,["disabled"])):u("",!0)]),_:1}),l(x)?(i(),f(K,{key:0,dismissible:!1,onClose:Z},te({body:r(()=>[t("div",We,[m(w,{src:"/images/success.gif",class:"w-44"}),t("div",Xe,s(e.$t("checkout.success-message-text")),1)])]),_:2},[l(y)?{name:"footer",fn:r(()=>[t("div",Ye,[m(c,{class:"w-full","as-child":""},{default:r(()=>[m(J,{to:l(g)},{default:r(()=>[p(s(e.$t("checkout.check-my-order")),1)]),_:1},8,["to"])]),_:1})])]),key:"0"}:void 0]),1024)):u("",!0)],64)}}}),Ct=ie(et,[["__scopeId","data-v-f5c16484"]]);export{Ct as default};
