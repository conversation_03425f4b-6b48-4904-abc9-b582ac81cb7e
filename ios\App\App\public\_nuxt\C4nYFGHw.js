const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./BaodNYLQ.js","./jAU0Cazi.js","./C3gyeM1K.js","./entry.CGfGZREn.css","./DerKnm_3.js"])))=>i.map(i=>d[i]);
import{i as G,aj as J,l as p,r as I,n as N,d as _,a as n,c as A,p as v,w as c,W as u,q as o,$ as K,o as d,e as s,ag as D,Z as V,g as Q,t as f,ah as E,_ as U}from"./C3gyeM1K.js";import{_ as X}from"./BoqtTPyX.js";import Y from"./CuUhnQhf.js";import{_ as P}from"./DAA1LH-g.js";import{_ as tt}from"./JNWsvxxg.js";import{_ as et}from"./lU3sMj3q.js";import{_ as ot}from"./C0H2RQFE.js";import{u as at}from"./BCJZ1_ur.js";import{u as st}from"./BrsQk_-I.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./BGs9QzNZ.js";import"./CRC5xmnh.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";const rt=D(()=>E(()=>import("./BaodNYLQ.js"),__vite__mapDeps([0,1,2,3]),import.meta.url).then(a=>a.Skeleton||a)),nt=D(()=>E(()=>import("./DerKnm_3.js"),__vite__mapDeps([4,2,3]),import.meta.url).then(a=>a.default||a)),it={class:"header flex bg-gray-100 p-2 relative items-center justify-center overflow-hidden max-md:max-h-36 min-w-full"},lt={class:"flex h-full py-2 overflow-hidden items-center justify-center"},ct={class:"actions flex flex-col absolute gap-1 top-2 left-2 text-[#ADAFB6] group-hover:text-[#404553]"},ut={key:0,class:"absolute pointer-events-none left-0 top-0 w-full h-full bg-gray-100 bg-opacity-80 z-10 flex justify-center items-center"},dt={class:"out-stock-text"},mt={class:"body"},pt={class:"name"},vt={class:"text-sm truncate-2-line leading-1"},ft={class:"price"},_t={class:"flex w-full p-2 bg-[#F3F9FC] justify-between items-center gap-2"},ht={key:0,class:"flex gap-2 items-center"},xt={class:"text-md font-bold text-green-700 max-sm:text-sm"},yt={key:0,class:"text-xs font-semibold text-gray-500 line-through max-sm:text-xs max-sm:font-semibold"},gt={key:1,class:"flex rounded-3xl items-center py-1 px-3 bg-orange-500 max-sm:px-1 max-sm:py-0.5"},bt={class:"text-white text-xs font-semibold text-nowrap max-sm:text-2xs"},wt={class:"flex w-full justify-end items-center gap-4"},Ct={class:"flex w-full px-4 justify-center items-center mb-4"},$t=G({__name:"product-card",props:{product:{},loading:{type:Boolean},variant:{default:"md"},class:{default:""}},setup(a){const{priceFormat:j}=at(),b=J(),w=st(),O=p(()=>{var t,e,r,i,g;return`${(g=(i=(r=(e=(t=a.product)==null?void 0:t.variance)==null?void 0:e.media)==null?void 0:r.cover)==null?void 0:i[0])==null?void 0:g.preview}`}),h=p(()=>{var t,e;return!((e=(t=a.product)==null?void 0:t.variance)!=null&&e.stock)}),m=p(()=>{var t,e,r,i;return Number((i=(r=(e=(t=a.product)==null?void 0:t.variance)==null?void 0:e.stock)==null?void 0:r.price)==null?void 0:i.value)}),l=p(()=>{var t,e,r,i;return Number((i=(r=(e=(t=a.product)==null?void 0:t.variance)==null?void 0:e.stock)==null?void 0:r.priceBeforeOffer)==null?void 0:i.value)}),x=I(!1),C=I(!1),z=I(!1),M=async()=>{var t,e;return x.value?b.removeFromList((e=a.product)==null?void 0:e.productId):b.addToList((t=a.product)==null?void 0:t.productId)};N(()=>b.list,()=>{var t;x.value=b.hasFav((t=a.product)==null?void 0:t.productId)},{immediate:!0,deep:!0}),N(()=>w.products,()=>{var t,e;C.value=w.hasVariance((e=(t=a.product)==null?void 0:t.variance)==null?void 0:e.varianceId)},{immediate:!0,deep:!0});const T=async()=>{var t,e,r,i;return C.value?w.removeProduct((i=(r=a.product)==null?void 0:r.variance)==null?void 0:i.varianceId):w.setProduct((e=(t=a.product)==null?void 0:t.variance)==null?void 0:e.varianceId)},F=p(()=>!(l!=null&&l.value)||(l==null?void 0:l.value)-(m==null?void 0:m.value)<=0?0:Math.abs((l==null?void 0:l.value)-(m==null?void 0:m.value))),L=p(()=>!F.value||h.value?0:(F.value/l.value*100).toFixed(0)),y=p(()=>a.variant==="horizontal");return(t,e)=>{const r=rt,i=nt,g=Q,$=X,B=Y,q=tt,R=P,H=et,W=ot;return d(),_(K,null,[n(R,{class:u(`card-${t.variant} ${t.loading?"":"group hover:border-primary-600"} ${a.class}`)},{default:c(()=>[n(q,{class:"p-0"},{default:c(()=>[t.loading?(d(),_("div",{key:0,class:u(["space-y-2 p-2 gap-4",{"flex flex-row":o(y)}])},[n(r,{class:u(["header w-full h-44 rounded-lg",{"w-1/2":o(y)}])},null,8,["class"]),s("div",{class:u(["flex flex-col gap-2",{"w-1/2 justify-evenly":o(y)}])},[n(r,{class:u(["h-6 rounded w-3/4",{"h-12 w-full":o(y)}])},null,8,["class"]),n(r,{class:u(["h-4 rounded w-2/4",{"h-10 w-2/3":o(y)}])},null,8,["class"])],2)],2)):v("",!0),t.loading?v("",!0):(d(),A(B,{key:1,class:"content group",to:`/product/${t.product.slug}`},{default:c(()=>{var k,S;return[s("div",it,[s("div",lt,[n(i,{src:o(O),alt:(k=t.product)==null?void 0:k.name,class:"aspect-square object-contain",sizes:{xs:"112px"},width:"201",height:"201",format:"webp",fit:"contain",provider:"backend"},null,8,["src","alt"])]),s("div",ct,[n($,{size:"icon",variant:"icon",class:u(["bg-white rounded border hover:text-primary-600 hover:!border-primary-600 group-hover:border-[#404553] transition-colors duration-300",{"!bg-primary-600 !text-white !border-primary-600":o(x),"border-0":o(h)}]),onClick:e[0]||(e[0]=V(Z=>M(),["prevent"]))},{default:c(()=>[n(g,{name:`${o(x)?"ui:heart-fill":"ui:heart"}`,size:t.variant!=="sm"?"19px":"12px",class:u({"text-white":o(x)})},null,8,["name","size","class"])]),_:1},8,["class"]),n($,{size:"icon",variant:"icon",class:u(["bg-white group rounded border hover:text-primary-500 hover:!border-primary-500 group-hover:border-[#404553]",{"!bg-primary-600 !text-white":o(C),"border-0":o(h)}]),onClick:e[1]||(e[1]=V(Z=>T(),["prevent"]))},{default:c(()=>[n(g,{name:"ui:card-cart",size:t.variant!=="sm"?"19px":"12px",class:u({"text-white":o(C)})},null,8,["size","class"])]),_:1},8,["class"])]),o(h)?(d(),_("div",ut,[s("span",dt,f(t.$t("product.out-stock")),1)])):v("",!0)]),s("div",mt,[s("div",pt,[s("span",vt,f((S=t.product)==null?void 0:S.name),1)]),s("div",ft,[s("div",_t,[o(h)?v("",!0):(d(),_("div",ht,[s("span",xt,f(o(j)(o(m))),1),o(F)?(d(),_("span",yt,f(o(j)(o(l))),1)):v("",!0)])),o(L)?(d(),_("div",gt,[s("span",bt,f(t.$t("product.card-discount",{amount:o(L)})),1)])):v("",!0)])])])]}),_:1},8,["to"]))]),_:1})]),_:1},8,["class"]),o(z)?(d(),A(W,{key:0,title:t.$t("cart-list.add-item-title"),onClose:e[3]||(e[3]=k=>z.value=null)},{footer:c(()=>[s("div",wt,[n($,{variant:"outline",onClickOnce:e[2]||(e[2]=k=>z.value=null)},{default:c(()=>[s("span",null,f(t.$t("cart-list.confirm-continue")),1)]),_:1}),n($,{"as-child":""},{default:c(()=>[n(B,{to:"/cart"},{default:c(()=>[s("span",null,f(t.$t("cart-list.confirm-continue-pay")),1)]),_:1})]),_:1})])]),body:c(()=>[s("div",Ct,[n(H,{product:t.product,"view-only":!0},null,8,["product"])])]),_:1},8,["title"])):v("",!0)],64)}}}),Yt=U($t,[["__scopeId","data-v-399d3ee6"]]);export{Yt as default};
