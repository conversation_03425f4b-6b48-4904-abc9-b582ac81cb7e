<script setup lang="ts">
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '~/components/ui/carousel'
import type { Item as Product } from '~/interfaces/product/product-list'

const limit = 10
const { data, error, status } = useApi<Product[]>('products/history', {
	query: {
		limit: limit,
	},
})

if (error.value) {
	console.error('Error fetching history:', error.value)
}

const products = computed<Product[]>(() => data.value as Product[])
const isLoading = computed<boolean>(() => status.value !== 'success')
</script>

<template>
	<Card class="col-span-3 flex flex-col max-md:col-span-3">
		<div class="px-4 py-4 flex justify-between items-center">
			<h2 class="text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl">
				{{ $t('home.history-title') }}
			</h2>

			<NuxtLinkLocale
				:to="`/category/smartphones`"
				class="max-sm:text-xs text-primary-600 hidden max-sm:flex"
			>
				{{ $t('home.see-more') }}
			</NuxtLinkLocale>
		</div>
		<CardContent class="flex w-full gap-4 px-4 place-items-center">
			<Carousel
				:opts="{ align: 'start', slidesToScroll: 'auto' }"
				class="w-full"
			>
				<template #default="{ canScrollNext, canScrollPrev }">
					<CarouselContent>
						<template
							v-if="isLoading"
						>
							<CarouselItem
								v-for="(_, index) in Array(limit)"
								:key="`product-skeleton-${index}`"
								class="basis-2/12 flex flex-col rounded h-full border p-2 border-gray-200 w-44 me-4"
							>
								<Skeleton class="w-full h-40 bg-gray-200 mb-2" />
								<Skeleton class="w-4/5 h-5 bg-gray-200 mb-2" />
								<Skeleton class="w-4/5 h-5 bg-gray-200 mb-2" />
								<Skeleton class="w-1/3 h-5 bg-gray-200 mb-2" />
							</CarouselItem>
						</template>
						<template v-else>
							<CarouselItem
								v-for="product in products"
								:key="product.productId"
								class="max-sm:max-w-52 max-sm:w-full sm:basis-4/10 max-w-72 !p-0"
							>
								<ProductCard
									:key="`product-${product.productId}`"
									:product="product"
								/>
							</CarouselItem>
						</template>
					</CarouselContent>
					<CarouselPrevious v-if="!isLoading && canScrollPrev" />
					<CarouselNext v-if="!isLoading && canScrollNext" />
				</template>
			</Carousel>
		</CardContent>
	</Card>
</template>
