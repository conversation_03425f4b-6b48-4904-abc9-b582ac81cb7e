import{_ as a}from"./BispfOyS.js";import{i as e,c as o,w as n,W as _,o as r,h as p,t as c,_ as l}from"./C3gyeM1K.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";const i=e({__name:"wallet",props:{status:{},size:{}},setup(m){return(s,u)=>{const t=a;return r(),o(t,{class:_(`wallet-status ${s.status} ${s.size}`)},{default:n(()=>[p(c(s.$t(`wallet.status-${s.status}`)),1)]),_:1},8,["class"])}}}),h=l(i,[["__scopeId","data-v-9b52e8fe"]]);export{h as default};
