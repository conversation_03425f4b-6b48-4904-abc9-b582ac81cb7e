import{s as re,A as se,b as le}from"./CRC5xmnh.js";import{g as ae}from"./FUKBWQwh.js";import{c as zt,u as Nt}from"./C2RgRyPS.js";import{P as It}from"./ClItKS3j.js";import{l as S,r as I,D as ce,n as pt,M as fe,N as ue,aX as Q,q as B,i as At,s as Ct,aY as jt,c as de,o as Yt,w as qt,aW as me,E as Xt,d as pe,a as he,a1 as ge,aq as we}from"./C3gyeM1K.js";import{u as xe}from"./DwXv0R8y.js";const ye=["top","right","bottom","left"],U=Math.min,$=Math.max,at=Math.round,lt=Math.floor,H=t=>({x:t,y:t}),ve={left:"right",right:"left",bottom:"top",top:"bottom"},be={start:"end",end:"start"};function wt(t,e,n){return $(t,U(e,n))}function j(t,e){return typeof t=="function"?t(e):t}function Y(t){return t.split("-")[0]}function ot(t){return t.split("-")[1]}function Ot(t){return t==="x"?"y":"x"}function Pt(t){return t==="y"?"height":"width"}function G(t){return["top","bottom"].includes(Y(t))?"y":"x"}function Rt(t){return Ot(G(t))}function Ae(t,e,n){n===void 0&&(n=!1);const o=ot(t),i=Rt(t),s=Pt(i);let r=i==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(r=ct(r)),[r,ct(r)]}function Ce(t){const e=ct(t);return[xt(t),e,xt(e)]}function xt(t){return t.replace(/start|end/g,e=>be[e])}function Oe(t,e,n){const o=["left","right"],i=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?s:r;default:return[]}}function Pe(t,e,n,o){const i=ot(t);let s=Oe(Y(t),n==="start",o);return i&&(s=s.map(r=>r+"-"+i),e&&(s=s.concat(s.map(xt)))),s}function ct(t){return t.replace(/left|right|bottom|top/g,e=>ve[e])}function Re(t){return{top:0,right:0,bottom:0,left:0,...t}}function Ut(t){return typeof t!="number"?Re(t):{top:t,right:t,bottom:t,left:t}}function ft(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function kt(t,e,n){let{reference:o,floating:i}=t;const s=G(e),r=Rt(e),l=Pt(r),c=Y(e),a=s==="y",u=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,m=o[l]/2-i[l]/2;let d;switch(c){case"top":d={x:u,y:o.y-i.height};break;case"bottom":d={x:u,y:o.y+o.height};break;case"right":d={x:o.x+o.width,y:f};break;case"left":d={x:o.x-i.width,y:f};break;default:d={x:o.x,y:o.y}}switch(ot(e)){case"start":d[r]-=m*(n&&a?-1:1);break;case"end":d[r]+=m*(n&&a?-1:1);break}return d}const Se=async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:s=[],platform:r}=n,l=s.filter(Boolean),c=await(r.isRTL==null?void 0:r.isRTL(e));let a=await r.getElementRects({reference:t,floating:e,strategy:i}),{x:u,y:f}=kt(a,o,c),m=o,d={},p=0;for(let h=0;h<l.length;h++){const{name:g,fn:w}=l[h],{x,y,data:v,reset:A}=await w({x:u,y:f,initialPlacement:o,placement:m,strategy:i,middlewareData:d,rects:a,platform:r,elements:{reference:t,floating:e}});u=x??u,f=y??f,d={...d,[g]:{...d[g],...v}},A&&p<=50&&(p++,typeof A=="object"&&(A.placement&&(m=A.placement),A.rects&&(a=A.rects===!0?await r.getElementRects({reference:t,floating:e,strategy:i}):A.rects),{x:u,y:f}=kt(a,m,c)),h=-1)}return{x:u,y:f,placement:m,strategy:i,middlewareData:d}};async function it(t,e){var n;e===void 0&&(e={});const{x:o,y:i,platform:s,rects:r,elements:l,strategy:c}=t,{boundary:a="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:m=!1,padding:d=0}=j(e,t),p=Ut(d),g=l[m?f==="floating"?"reference":"floating":f],w=ft(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:a,rootBoundary:u,strategy:c})),x=f==="floating"?{x:o,y:i,width:r.floating.width,height:r.floating.height}:r.reference,y=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),v=await(s.isElement==null?void 0:s.isElement(y))?await(s.getScale==null?void 0:s.getScale(y))||{x:1,y:1}:{x:1,y:1},A=ft(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:x,offsetParent:y,strategy:c}):x);return{top:(w.top-A.top+p.top)/v.y,bottom:(A.bottom-w.bottom+p.bottom)/v.y,left:(w.left-A.left+p.left)/v.x,right:(A.right-w.right+p.right)/v.x}}const Ee=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:s,platform:r,elements:l,middlewareData:c}=e,{element:a,padding:u=0}=j(t,e)||{};if(a==null)return{};const f=Ut(u),m={x:n,y:o},d=Rt(i),p=Pt(d),h=await r.getDimensions(a),g=d==="y",w=g?"top":"left",x=g?"bottom":"right",y=g?"clientHeight":"clientWidth",v=s.reference[p]+s.reference[d]-m[d]-s.floating[p],A=m[d]-s.reference[d],R=await(r.getOffsetParent==null?void 0:r.getOffsetParent(a));let b=R?R[y]:0;(!b||!await(r.isElement==null?void 0:r.isElement(R)))&&(b=l.floating[y]||s.floating[p]);const O=v/2-A/2,E=b/2-h[p]/2-1,L=U(f[w],E),C=U(f[x],E),T=L,k=b-h[p]-C,P=b/2-h[p]/2+O,V=wt(T,P,k),W=!c.arrow&&ot(i)!=null&&P!==V&&s.reference[p]/2-(P<T?L:C)-h[p]/2<0,D=W?P<T?P-T:P-k:0;return{[d]:m[d]+D,data:{[d]:V,centerOffset:P-V-D,...W&&{alignmentOffset:D}},reset:W}}}),De=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:s,rects:r,initialPlacement:l,platform:c,elements:a}=e,{mainAxis:u=!0,crossAxis:f=!0,fallbackPlacements:m,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:h=!0,...g}=j(t,e);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const w=Y(i),x=G(l),y=Y(l)===l,v=await(c.isRTL==null?void 0:c.isRTL(a.floating)),A=m||(y||!h?[ct(l)]:Ce(l)),R=p!=="none";!m&&R&&A.push(...Pe(l,h,p,v));const b=[l,...A],O=await it(e,g),E=[];let L=((o=s.flip)==null?void 0:o.overflows)||[];if(u&&E.push(O[w]),f){const P=Ae(i,r,v);E.push(O[P[0]],O[P[1]])}if(L=[...L,{placement:i,overflows:E}],!E.every(P=>P<=0)){var C,T;const P=(((C=s.flip)==null?void 0:C.index)||0)+1,V=b[P];if(V)return{data:{index:P,overflows:L},reset:{placement:V}};let W=(T=L.filter(D=>D.overflows[0]<=0).sort((D,q)=>D.overflows[1]-q.overflows[1])[0])==null?void 0:T.placement;if(!W)switch(d){case"bestFit":{var k;const D=(k=L.filter(q=>{if(R){const X=G(q.placement);return X===x||X==="y"}return!0}).map(q=>[q.placement,q.overflows.filter(X=>X>0).reduce((X,ie)=>X+ie,0)]).sort((q,X)=>q[1]-X[1])[0])==null?void 0:k[0];D&&(W=D);break}case"initialPlacement":W=l;break}if(i!==W)return{reset:{placement:W}}}return{}}}};function $t(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Mt(t){return ye.some(e=>t[e]>=0)}const Te=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:o="referenceHidden",...i}=j(t,e);switch(o){case"referenceHidden":{const s=await it(e,{...i,elementContext:"reference"}),r=$t(s,n.reference);return{data:{referenceHiddenOffsets:r,referenceHidden:Mt(r)}}}case"escaped":{const s=await it(e,{...i,altBoundary:!0}),r=$t(s,n.floating);return{data:{escapedOffsets:r,escaped:Mt(r)}}}default:return{}}}}};async function Le(t,e){const{placement:n,platform:o,elements:i}=t,s=await(o.isRTL==null?void 0:o.isRTL(i.floating)),r=Y(n),l=ot(n),c=G(n)==="y",a=["left","top"].includes(r)?-1:1,u=s&&c?-1:1,f=j(e,t);let{mainAxis:m,crossAxis:d,alignmentAxis:p}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof p=="number"&&(d=l==="end"?p*-1:p),c?{x:d*u,y:m*a}:{x:m*a,y:d*u}}const ke=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:s,placement:r,middlewareData:l}=e,c=await Le(e,t);return r===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:i+c.x,y:s+c.y,data:{...c,placement:r}}}}},$e=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:s=!0,crossAxis:r=!1,limiter:l={fn:g=>{let{x:w,y:x}=g;return{x:w,y:x}}},...c}=j(t,e),a={x:n,y:o},u=await it(e,c),f=G(Y(i)),m=Ot(f);let d=a[m],p=a[f];if(s){const g=m==="y"?"top":"left",w=m==="y"?"bottom":"right",x=d+u[g],y=d-u[w];d=wt(x,d,y)}if(r){const g=f==="y"?"top":"left",w=f==="y"?"bottom":"right",x=p+u[g],y=p-u[w];p=wt(x,p,y)}const h=l.fn({...e,[m]:d,[f]:p});return{...h,data:{x:h.x-n,y:h.y-o,enabled:{[m]:s,[f]:r}}}}}},Me=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:i,rects:s,middlewareData:r}=e,{offset:l=0,mainAxis:c=!0,crossAxis:a=!0}=j(t,e),u={x:n,y:o},f=G(i),m=Ot(f);let d=u[m],p=u[f];const h=j(l,e),g=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const y=m==="y"?"height":"width",v=s.reference[m]-s.floating[y]+g.mainAxis,A=s.reference[m]+s.reference[y]-g.mainAxis;d<v?d=v:d>A&&(d=A)}if(a){var w,x;const y=m==="y"?"width":"height",v=["top","left"].includes(Y(i)),A=s.reference[f]-s.floating[y]+(v&&((w=r.offset)==null?void 0:w[f])||0)+(v?0:g.crossAxis),R=s.reference[f]+s.reference[y]+(v?0:((x=r.offset)==null?void 0:x[f])||0)-(v?g.crossAxis:0);p<A?p=A:p>R&&(p=R)}return{[m]:d,[f]:p}}}},Be=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:i,rects:s,platform:r,elements:l}=e,{apply:c=()=>{},...a}=j(t,e),u=await it(e,a),f=Y(i),m=ot(i),d=G(i)==="y",{width:p,height:h}=s.floating;let g,w;f==="top"||f==="bottom"?(g=f,w=m===(await(r.isRTL==null?void 0:r.isRTL(l.floating))?"start":"end")?"left":"right"):(w=f,g=m==="end"?"top":"bottom");const x=h-u.top-u.bottom,y=p-u.left-u.right,v=U(h-u[g],x),A=U(p-u[w],y),R=!e.middlewareData.shift;let b=v,O=A;if((n=e.middlewareData.shift)!=null&&n.enabled.x&&(O=y),(o=e.middlewareData.shift)!=null&&o.enabled.y&&(b=x),R&&!m){const L=$(u.left,0),C=$(u.right,0),T=$(u.top,0),k=$(u.bottom,0);d?O=p-2*(L!==0||C!==0?L+C:$(u.left,u.right)):b=h-2*(T!==0||k!==0?T+k:$(u.top,u.bottom))}await c({...e,availableWidth:O,availableHeight:b});const E=await r.getDimensions(l.floating);return p!==E.width||h!==E.height?{reset:{rects:!0}}:{}}}};function ut(){return typeof window<"u"}function J(t){return St(t)?(t.nodeName||"").toLowerCase():"#document"}function M(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function N(t){var e;return(e=(St(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function St(t){return ut()?t instanceof Node||t instanceof M(t).Node:!1}function F(t){return ut()?t instanceof Element||t instanceof M(t).Element:!1}function z(t){return ut()?t instanceof HTMLElement||t instanceof M(t).HTMLElement:!1}function Bt(t){return!ut()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof M(t).ShadowRoot}function st(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=_(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function Ve(t){return["table","td","th"].includes(J(t))}function dt(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function Et(t){const e=Dt(),n=F(t)?_(t):t;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function We(t){let e=K(t);for(;z(e)&&!nt(e);){if(Et(e))return e;if(dt(e))return null;e=K(e)}return null}function Dt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function nt(t){return["html","body","#document"].includes(J(t))}function _(t){return M(t).getComputedStyle(t)}function mt(t){return F(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function K(t){if(J(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Bt(t)&&t.host||N(t);return Bt(e)?e.host:e}function Gt(t){const e=K(t);return nt(e)?t.ownerDocument?t.ownerDocument.body:t.body:z(e)&&st(e)?e:Gt(e)}function rt(t,e,n){var o;e===void 0&&(e=[]),n===void 0&&(n=!0);const i=Gt(t),s=i===((o=t.ownerDocument)==null?void 0:o.body),r=M(i);if(s){const l=yt(r);return e.concat(r,r.visualViewport||[],st(i)?i:[],l&&n?rt(l):[])}return e.concat(i,rt(i,[],n))}function yt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Kt(t){const e=_(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=z(t),s=i?t.offsetWidth:n,r=i?t.offsetHeight:o,l=at(n)!==s||at(o)!==r;return l&&(n=s,o=r),{width:n,height:o,$:l}}function Tt(t){return F(t)?t:t.contextElement}function et(t){const e=Tt(t);if(!z(e))return H(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:s}=Kt(e);let r=(s?at(n.width):n.width)/o,l=(s?at(n.height):n.height)/i;return(!r||!Number.isFinite(r))&&(r=1),(!l||!Number.isFinite(l))&&(l=1),{x:r,y:l}}const Fe=H(0);function Zt(t){const e=M(t);return!Dt()||!e.visualViewport?Fe:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function _e(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==M(t)?!1:e}function Z(t,e,n,o){e===void 0&&(e=!1),n===void 0&&(n=!1);const i=t.getBoundingClientRect(),s=Tt(t);let r=H(1);e&&(o?F(o)&&(r=et(o)):r=et(t));const l=_e(s,n,o)?Zt(s):H(0);let c=(i.left+l.x)/r.x,a=(i.top+l.y)/r.y,u=i.width/r.x,f=i.height/r.y;if(s){const m=M(s),d=o&&F(o)?M(o):o;let p=m,h=yt(p);for(;h&&o&&d!==p;){const g=et(h),w=h.getBoundingClientRect(),x=_(h),y=w.left+(h.clientLeft+parseFloat(x.paddingLeft))*g.x,v=w.top+(h.clientTop+parseFloat(x.paddingTop))*g.y;c*=g.x,a*=g.y,u*=g.x,f*=g.y,c+=y,a+=v,p=M(h),h=yt(p)}}return ft({width:u,height:f,x:c,y:a})}function Lt(t,e){const n=mt(t).scrollLeft;return e?e.left+n:Z(N(t)).left+n}function Jt(t,e,n){n===void 0&&(n=!1);const o=t.getBoundingClientRect(),i=o.left+e.scrollLeft-(n?0:Lt(t,o)),s=o.top+e.scrollTop;return{x:i,y:s}}function He(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const s=i==="fixed",r=N(o),l=e?dt(e.floating):!1;if(o===r||l&&s)return n;let c={scrollLeft:0,scrollTop:0},a=H(1);const u=H(0),f=z(o);if((f||!f&&!s)&&((J(o)!=="body"||st(r))&&(c=mt(o)),z(o))){const d=Z(o);a=et(o),u.x=d.x+o.clientLeft,u.y=d.y+o.clientTop}const m=r&&!f&&!s?Jt(r,c,!0):H(0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+u.x+m.x,y:n.y*a.y-c.scrollTop*a.y+u.y+m.y}}function ze(t){return Array.from(t.getClientRects())}function Ne(t){const e=N(t),n=mt(t),o=t.ownerDocument.body,i=$(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),s=$(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let r=-n.scrollLeft+Lt(t);const l=-n.scrollTop;return _(o).direction==="rtl"&&(r+=$(e.clientWidth,o.clientWidth)-i),{width:i,height:s,x:r,y:l}}function Ie(t,e){const n=M(t),o=N(t),i=n.visualViewport;let s=o.clientWidth,r=o.clientHeight,l=0,c=0;if(i){s=i.width,r=i.height;const a=Dt();(!a||a&&e==="fixed")&&(l=i.offsetLeft,c=i.offsetTop)}return{width:s,height:r,x:l,y:c}}function je(t,e){const n=Z(t,!0,e==="fixed"),o=n.top+t.clientTop,i=n.left+t.clientLeft,s=z(t)?et(t):H(1),r=t.clientWidth*s.x,l=t.clientHeight*s.y,c=i*s.x,a=o*s.y;return{width:r,height:l,x:c,y:a}}function Vt(t,e,n){let o;if(e==="viewport")o=Ie(t,n);else if(e==="document")o=Ne(N(t));else if(F(e))o=je(e,n);else{const i=Zt(t);o={x:e.x-i.x,y:e.y-i.y,width:e.width,height:e.height}}return ft(o)}function Qt(t,e){const n=K(t);return n===e||!F(n)||nt(n)?!1:_(n).position==="fixed"||Qt(n,e)}function Ye(t,e){const n=e.get(t);if(n)return n;let o=rt(t,[],!1).filter(l=>F(l)&&J(l)!=="body"),i=null;const s=_(t).position==="fixed";let r=s?K(t):t;for(;F(r)&&!nt(r);){const l=_(r),c=Et(r);!c&&l.position==="fixed"&&(i=null),(s?!c&&!i:!c&&l.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||st(r)&&!c&&Qt(t,r))?o=o.filter(u=>u!==r):i=l,r=K(r)}return e.set(t,o),o}function qe(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const r=[...n==="clippingAncestors"?dt(e)?[]:Ye(e,this._c):[].concat(n),o],l=r[0],c=r.reduce((a,u)=>{const f=Vt(e,u,i);return a.top=$(f.top,a.top),a.right=U(f.right,a.right),a.bottom=U(f.bottom,a.bottom),a.left=$(f.left,a.left),a},Vt(e,l,i));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Xe(t){const{width:e,height:n}=Kt(t);return{width:e,height:n}}function Ue(t,e,n){const o=z(e),i=N(e),s=n==="fixed",r=Z(t,!0,s,e);let l={scrollLeft:0,scrollTop:0};const c=H(0);if(o||!o&&!s)if((J(e)!=="body"||st(i))&&(l=mt(e)),o){const m=Z(e,!0,s,e);c.x=m.x+e.clientLeft,c.y=m.y+e.clientTop}else i&&(c.x=Lt(i));const a=i&&!o&&!s?Jt(i,l):H(0),u=r.left+l.scrollLeft-c.x-a.x,f=r.top+l.scrollTop-c.y-a.y;return{x:u,y:f,width:r.width,height:r.height}}function ht(t){return _(t).position==="static"}function Wt(t,e){if(!z(t)||_(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return N(t)===n&&(n=n.ownerDocument.body),n}function te(t,e){const n=M(t);if(dt(t))return n;if(!z(t)){let i=K(t);for(;i&&!nt(i);){if(F(i)&&!ht(i))return i;i=K(i)}return n}let o=Wt(t,e);for(;o&&Ve(o)&&ht(o);)o=Wt(o,e);return o&&nt(o)&&ht(o)&&!Et(o)?n:o||We(t)||n}const Ge=async function(t){const e=this.getOffsetParent||te,n=this.getDimensions,o=await n(t.floating);return{reference:Ue(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Ke(t){return _(t).direction==="rtl"}const Ze={convertOffsetParentRelativeRectToViewportRelativeRect:He,getDocumentElement:N,getClippingRect:qe,getOffsetParent:te,getElementRects:Ge,getClientRects:ze,getDimensions:Xe,getScale:et,isElement:F,isRTL:Ke};function ee(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function Je(t,e){let n=null,o;const i=N(t);function s(){var l;clearTimeout(o),(l=n)==null||l.disconnect(),n=null}function r(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),s();const a=t.getBoundingClientRect(),{left:u,top:f,width:m,height:d}=a;if(l||e(),!m||!d)return;const p=lt(f),h=lt(i.clientWidth-(u+m)),g=lt(i.clientHeight-(f+d)),w=lt(u),y={rootMargin:-p+"px "+-h+"px "+-g+"px "+-w+"px",threshold:$(0,U(1,c))||1};let v=!0;function A(R){const b=R[0].intersectionRatio;if(b!==c){if(!v)return r();b?r(!1,b):o=setTimeout(()=>{r(!1,1e-7)},1e3)}b===1&&!ee(a,t.getBoundingClientRect())&&r(),v=!1}try{n=new IntersectionObserver(A,{...y,root:i.ownerDocument})}catch{n=new IntersectionObserver(A,y)}n.observe(t)}return r(!0),s}function Qe(t,e,n,o){o===void 0&&(o={});const{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:r=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=o,a=Tt(t),u=i||s?[...a?rt(a):[],...rt(e)]:[];u.forEach(w=>{i&&w.addEventListener("scroll",n,{passive:!0}),s&&w.addEventListener("resize",n)});const f=a&&l?Je(a,n):null;let m=-1,d=null;r&&(d=new ResizeObserver(w=>{let[x]=w;x&&x.target===a&&d&&(d.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var y;(y=d)==null||y.observe(e)})),n()}),a&&!c&&d.observe(a),d.observe(e));let p,h=c?Z(t):null;c&&g();function g(){const w=Z(t);h&&!ee(h,w)&&n(),h=w,p=requestAnimationFrame(g)}return n(),()=>{var w;u.forEach(x=>{i&&x.removeEventListener("scroll",n),s&&x.removeEventListener("resize",n)}),f==null||f(),(w=d)==null||w.disconnect(),d=null,c&&cancelAnimationFrame(p)}}const tn=ke,en=$e,Ft=De,nn=Be,on=Te,rn=Ee,sn=Me,ln=(t,e,n)=>{const o=new Map,i={platform:Ze,...n},s={...i.platform,_c:o};return Se(t,e,{...i,platform:s})};function an(t){return t!=null&&typeof t=="object"&&"$el"in t}function vt(t){if(an(t)){const e=t.$el;return St(e)&&J(e)==="#comment"?null:e}return t}function tt(t){return typeof t=="function"?t():B(t)}function cn(t){return{name:"arrow",options:t,fn(e){const n=vt(tt(t.element));return n==null?{}:rn({element:n,padding:t.padding}).fn(e)}}}function ne(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function _t(t,e){const n=ne(t);return Math.round(e*n)/n}function fn(t,e,n){n===void 0&&(n={});const o=n.whileElementsMounted,i=S(()=>{var b;return(b=tt(n.open))!=null?b:!0}),s=S(()=>tt(n.middleware)),r=S(()=>{var b;return(b=tt(n.placement))!=null?b:"bottom"}),l=S(()=>{var b;return(b=tt(n.strategy))!=null?b:"absolute"}),c=S(()=>{var b;return(b=tt(n.transform))!=null?b:!0}),a=S(()=>vt(t.value)),u=S(()=>vt(e.value)),f=I(0),m=I(0),d=I(l.value),p=I(r.value),h=ce({}),g=I(!1),w=S(()=>{const b={position:d.value,left:"0",top:"0"};if(!u.value)return b;const O=_t(u.value,f.value),E=_t(u.value,m.value);return c.value?{...b,transform:"translate("+O+"px, "+E+"px)",...ne(u.value)>=1.5&&{willChange:"transform"}}:{position:d.value,left:O+"px",top:E+"px"}});let x;function y(){if(a.value==null||u.value==null)return;const b=i.value;ln(a.value,u.value,{middleware:s.value,placement:r.value,strategy:l.value}).then(O=>{f.value=O.x,m.value=O.y,d.value=O.strategy,p.value=O.placement,h.value=O.middlewareData,g.value=b!==!1})}function v(){typeof x=="function"&&(x(),x=void 0)}function A(){if(v(),o===void 0){y();return}if(a.value!=null&&u.value!=null){x=o(a.value,u.value,y);return}}function R(){i.value||(g.value=!1)}return pt([s,r,l,i],y,{flush:"sync"}),pt([a,u],A,{flush:"sync"}),pt(i,R,{flush:"sync"}),fe()&&ue(v),{x:Q(f),y:Q(m),strategy:Q(d),placement:Q(p),middlewareData:Q(h),isPositioned:Q(g),floatingStyles:w,update:y}}const[oe,un]=zt("PopperRoot"),On=At({inheritAttrs:!1,__name:"PopperRoot",setup(t){const e=I();return un({anchor:e,onAnchorChange:n=>e.value=n}),(n,o)=>Ct(n.$slots,"default")}});function Pn(t){const e=re("",1e3);return{search:e,handleTypeaheadSearch:(i,s)=>{e.value=e.value+i;{const r=ae(),l=s.map(m=>{var d,p;return{...m,textValue:((d=m.value)==null?void 0:d.textValue)??((p=m.ref.textContent)==null?void 0:p.trim())??""}}),c=l.find(m=>m.ref===r),a=l.map(m=>m.textValue),u=mn(a,e.value,c==null?void 0:c.textValue),f=l.find(m=>m.textValue===u);return f&&f.ref.focus(),f==null?void 0:f.ref}},resetTypeahead:()=>{e.value=""}}}function dn(t,e){return t.map((n,o)=>t[(e+o)%t.length])}function mn(t,e,n){const i=e.length>1&&Array.from(e).every(a=>a===e[0])?e[0]:e,s=n?t.indexOf(n):-1;let r=dn(t,Math.max(s,0));i.length===1&&(r=r.filter(a=>a!==n));const c=r.find(a=>a.toLowerCase().startsWith(i.toLowerCase()));return c!==n?c:void 0}const Rn=At({__name:"PopperAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(t){const e=t,{forwardRef:n,currentElement:o}=Nt(),i=oe();return jt(()=>{i.onAnchorChange(e.reference??o.value)}),(s,r)=>(Yt(),de(B(It),{ref:B(n),as:s.as,"as-child":s.asChild},{default:qt(()=>[Ct(s.$slots,"default")]),_:3},8,["as","as-child"]))}});function pn(t){return t!==null}function hn(t){return{name:"transformOrigin",options:t,fn(e){var g,w,x;const{placement:n,rects:o,middlewareData:i}=e,r=((g=i.arrow)==null?void 0:g.centerOffset)!==0,l=r?0:t.arrowWidth,c=r?0:t.arrowHeight,[a,u]=bt(n),f={start:"0%",center:"50%",end:"100%"}[u],m=(((w=i.arrow)==null?void 0:w.x)??0)+l/2,d=(((x=i.arrow)==null?void 0:x.y)??0)+c/2;let p="",h="";return a==="bottom"?(p=r?f:`${m}px`,h=`${-c}px`):a==="top"?(p=r?f:`${m}px`,h=`${o.floating.height+c}px`):a==="right"?(p=`${-c}px`,h=r?f:`${d}px`):a==="left"&&(p=`${o.floating.width+c}px`,h=r?f:`${d}px`),{data:{x:p,y:h}}}}}function bt(t){const[e,n="center"]=t.split("-");return[e,n]}const gn={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[Sn,wn]=zt("PopperContent"),En=At({inheritAttrs:!1,__name:"PopperContent",props:me({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...gn}),emits:["placed"],setup(t,{emit:e}){const n=t,o=e,i=oe(),{forwardRef:s,currentElement:r}=Nt(),l=I(),c=I(),{width:a,height:u}=xe(c),f=S(()=>n.side+(n.align!=="center"?`-${n.align}`:"")),m=S(()=>typeof n.collisionPadding=="number"?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),d=S(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),p=S(()=>({padding:m.value,boundary:d.value.filter(pn),altBoundary:d.value.length>0})),h=se(()=>[tn({mainAxis:n.sideOffset+u.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&Ft({...p.value}),n.avoidCollisions&&en({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky==="partial"?sn():void 0,...p.value}),!n.prioritizePosition&&n.avoidCollisions&&Ft({...p.value}),nn({...p.value,apply:({elements:C,rects:T,availableWidth:k,availableHeight:P})=>{const{width:V,height:W}=T.reference,D=C.floating.style;D.setProperty("--reka-popper-available-width",`${k}px`),D.setProperty("--reka-popper-available-height",`${P}px`),D.setProperty("--reka-popper-anchor-width",`${V}px`),D.setProperty("--reka-popper-anchor-height",`${W}px`)}}),c.value&&cn({element:c.value,padding:n.arrowPadding}),hn({arrowWidth:a.value,arrowHeight:u.value}),n.hideWhenDetached&&on({strategy:"referenceHidden",...p.value})]),g=S(()=>n.reference??i.anchor.value),{floatingStyles:w,placement:x,isPositioned:y,middlewareData:v}=fn(g,l,{strategy:n.positionStrategy,placement:f,whileElementsMounted:(...C)=>Qe(...C,{layoutShift:!n.disableUpdateOnLayoutShift,animationFrame:n.updatePositionStrategy==="always"}),middleware:h}),A=S(()=>bt(x.value)[0]),R=S(()=>bt(x.value)[1]);jt(()=>{y.value&&o("placed")});const b=S(()=>{var C;return((C=v.value.arrow)==null?void 0:C.centerOffset)!==0}),O=I("");Xt(()=>{r.value&&(O.value=window.getComputedStyle(r.value).zIndex)});const E=S(()=>{var C;return((C=v.value.arrow)==null?void 0:C.x)??0}),L=S(()=>{var C;return((C=v.value.arrow)==null?void 0:C.y)??0});return wn({placedSide:A,onArrowChange:C=>c.value=C,arrowX:E,arrowY:L,shouldHideArrow:b}),(C,T)=>{var k,P,V;return Yt(),pe("div",{ref_key:"floatingRef",ref:l,"data-reka-popper-content-wrapper":"",style:we({...B(w),transform:B(y)?B(w).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:O.value,"--reka-popper-transform-origin":[(k=B(v).transformOrigin)==null?void 0:k.x,(P=B(v).transformOrigin)==null?void 0:P.y].join(" "),...((V=B(v).hide)==null?void 0:V.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[he(B(It),ge({ref:B(s)},C.$attrs,{"as-child":n.asChild,as:C.as,"data-side":A.value,"data-align":R.value,style:{animation:B(y)?void 0:"none"}}),{default:qt(()=>[Ct(C.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}});let gt=0;function Dn(){Xt(t=>{if(!le)return;const e=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",e[0]??Ht()),document.body.insertAdjacentElement("beforeend",e[1]??Ht()),gt++,t(()=>{gt===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(n=>n.remove()),gt--})})}function Ht(){const t=document.createElement("span");return t.setAttribute("data-reka-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}export{gn as P,On as _,Rn as a,Pn as b,En as c,Dn as u};
