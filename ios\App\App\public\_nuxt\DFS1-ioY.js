const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as K,aj as W,ai as Z,r as x,l as G,n as F,d,a as t,w as p,o,p as C,q as l,e as s,t as c,a4 as H,Z as _,g as J,a5 as Q,K as X,$,c as h,h as g,W as L,a0 as Y,ag as M,ah as ee}from"./C3gyeM1K.js";import{Skeleton as te}from"./BaodNYLQ.js";import{_ as ae}from"./BoqtTPyX.js";import{_ as se}from"./DAA1LH-g.js";import{_ as oe}from"./BxRYGYnW.js";import{u as re}from"./BrsQk_-I.js";const ne=M(()=>ee(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(r=>r.default||r)),le={class:"flex flex-col w-1/2 gap-4 max-md:w-full"},ce={key:0,class:"flex flex-col py-4 gap-2"},de={key:1,class:"max-sm:hidden flex items-center py-6 gap-6"},ie={id:"price-section",class:"grid grid-cols-[auto_1fr_auto] border border-gray-200 rounded max-w-44 items-center overflow-hidden"},ue={class:"text-sm font-medium text-orange-400"},me={key:2,class:"gap-4 flex-col flex"},pe={class:"flex flex-col w-full gap-6 max-sm:hidden"},fe={class:"flex justify-center items-center gap-2"},ve={key:0},ye={key:1},xe={class:"flex p-2 bg-gray-100 text-md font-md rounded items-center mb-4"},_e={class:"col-span-1 flex flex-wrap items-center justify-start"},he={class:"px-2 text-xs"},ge={class:"flex p-2 bg-gray-100 text-md font-md rounded items-center"},we={class:"px-2 text-xs"},Ve=K({__name:"payment-card",props:{product:{},loading:{type:Boolean}},emits:["confirm:add-to-cart","product:pay-now","product:compare"],setup(r,{emit:T}){const v=W(),I=Z(),S=re(),w=T,{stock:j,hasStock:V,productId:z}=r.product,y=x(!1),k=x(!1),b=x(!1),n=x(1),N=G(()=>I.hasCart(z)),D=()=>{var e;n.value<((e=j.value)==null?void 0:e.maxPerUser)&&(n.value+=1)},A=()=>{n.value>1&&(n.value=n.value-1)},E=async()=>{var e,a;return y.value?v.removeFromList((a=r.product)==null?void 0:a.productId):v.addToList((e=r.product)==null?void 0:e.productId)};F(()=>v.list,()=>{var e;y.value=v.hasFav((e=r.product)==null?void 0:e.productId)},{immediate:!0,deep:!0});const P=async()=>{b.value=!0,await I.addToList({productId:r.product.productId,quantity:n.value,varianceId:r.product.variance.varianceId}),b.value=!1,w("confirm:add-to-cart",r.product)};F(()=>S.products,()=>{var e,a;k.value=S.hasVariance((a=(e=r.product)==null?void 0:e.variance)==null?void 0:a.varianceId)},{immediate:!0,deep:!0});const O=async()=>{var e,a;w("product:compare",(a=(e=r.product)==null?void 0:e.variance)==null?void 0:a.varianceId)};return(e,a)=>{const m=te,u=J,f=ae,R=ne,U=se;return o(),d("div",le,[t(U,{class:"flex flex-col shadow-md p-4 max-sm:p-0 max-sm:border-0 max-sm:mt-4"},{default:p(()=>[t(oe,{class:"md:flex sm:hidden xs:flex",loading:e.loading,product:e.product},null,8,["loading","product"]),e.loading?(o(),d("div",ce,[t(m,{class:"w-full h-6"}),t(m,{class:"w-full h-6"})])):l(V)?(o(),d("div",de,[s("span",null,c(e.$t("product.quantity"))+": ",1),s("div",ie,[s("button",{class:"border-e border-gray-200 w-7 items-center flex justify-center h-7",onClick:_(D,["prevent"])},[t(u,{name:"lucide:plus",height:"12px",width:"12px"})]),H(s("input",{"onUpdate:modelValue":a[0]||(a[0]=i=>X(n)?n.value=i:null),name:"quantity",type:"number",readonly:"",class:"no-spinner border-none outline-none col-span-1 w-auto max-w-10 text-center text-xs",max:2},null,512),[[Q,l(n)]]),s("button",{class:"border-s border-gray-200 w-7 h-full items-center flex justify-center",onClick:_(A,["prevent"])},[t(u,{name:"lucide:minus",width:"12px"})])]),s("span",ue,c(e.$t("product.quantity-will-finished")),1)])):C("",!0),e.loading?(o(),d("div",me,[t(m,{class:"w-full h-7"}),t(m,{class:"w-full h-7 bg-primary-200"}),t(m,{class:"w-full h-7"}),t(m,{class:"w-full h-7"})])):(o(),d($,{key:3},[s("div",pe,[l(V)?(o(),d($,{key:0},[t(f,{class:"w-full",onClick:a[1]||(a[1]=_(i=>w("product:pay-now",l(n)),["prevent"]))},{default:p(()=>[g(c(e.$t("product.pay-now")),1)]),_:1}),t(f,{variant:"outline",class:L(["w-full",{hidden:l(N)}]),size:"lg",loading:l(b),onClickOnce:P},{default:p(()=>[g(c(e.$t("product.add-to-cart")),1)]),_:1},8,["class","loading"])],64)):(o(),h(f,{key:1,class:"w-full mt-4"},{default:p(()=>[g(c(e.$t("product.let-me-know")),1)]),_:1})),s("div",fe,[t(f,{variant:"white",onClick:E},{default:p(()=>[l(y)?C("",!0):(o(),h(u,{key:0,name:"ui:heart"})),l(y)?(o(),h(u,{key:1,class:"text-primary-600",name:"ui:heart-fill"})):C("",!0),g(" "+c(e.$t("product.add-favorite")),1)]),_:1}),t(f,{variant:"white",class:L({"text-primary-600 bg-primary-100":l(k)}),onClick:_(O,["prevent"])},{default:p(()=>[t(u,{name:"ui:compare"}),l(k)?(o(),d("span",ve,c(e.$t("product.remove-comparison")),1)):(o(),d("span",ye,c(e.$t("product.add-comparison")),1))]),_:1},8,["class"])])]),a[2]||(a[2]=s("div",{class:"flex border-t border-dashed border-gray-200 w-full my-4"},null,-1)),s("div",xe,[t(u,{name:"lucide:banknote",width:"22px",class:"text-gray-500"}),s("div",_e,[s("span",he,c(e.$t("product.payment-title")),1),(o(!0),d($,null,Y(e.product.paymentMethods,i=>{var q,B;return o(),h(R,{key:`product-footer-payment-${i.paymentMethodId}`,src:(B=(q=i==null?void 0:i.media)==null?void 0:q.logo)==null?void 0:B.src,class:"h-5 me-0.5",title:i.name},null,8,["src","title"])}),128))])]),s("div",ge,[t(u,{name:"lucide:truck",size:"20px",class:"text-gray-500"}),s("span",we,c(e.$t("product.free-delivery")),1)])],64))]),_:1})])}}});export{Ve as _};
