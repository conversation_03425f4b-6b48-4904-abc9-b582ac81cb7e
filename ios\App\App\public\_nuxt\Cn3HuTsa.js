import h from"./CuUhnQhf.js";import{Skeleton as $}from"./BaodNYLQ.js";import w from"./C4nYFGHw.js";import{_ as C}from"./DAA1LH-g.js";import{_ as L}from"./JNWsvxxg.js";import{b as B,_ as N,a as d}from"./CiqmSqHP.js";import{_ as S,a as V}from"./D3J2c5MI.js";import{i as M,c as t,w as o,o as e,e as v,a as s,d as m,p as n,t as i,h as I,q as r,a0 as _,$ as f}from"./C3gyeM1K.js";const T={class:"px-4 py-4 flex justify-between items-center"},j={key:0,class:"text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl"},K=M({__name:"horizantal-cards",props:{products:{default:()=>[]},loading:{type:Boolean,default:!1},title:{default:""},seeMoreLink:{default:null}},setup(q){return(a,z)=>{const p=h,c=$,u=w,g=L,y=C;return e(),t(y,{class:"col-span-3 flex flex-col max-md:col-span-3"},{default:o(()=>[v("div",T,[a.title?(e(),m("span",j,i(a.title),1)):n("",!0),a.seeMoreLink?(e(),t(p,{key:1,to:a.seeMoreLink,class:"text-primary-600 hidden max-sm:flex"},{default:o(()=>[I(i(a.$t("home.see-more")),1)]),_:1},8,["to"])):n("",!0)]),s(g,{class:"grid md:grid-cols-4 xs:grid-cols-1 gap-4 px-4 place-items-center"},{default:o(()=>[s(r(B),{opts:{align:"start",slidesToScroll:"auto"},class:"w-full col-span-4"},{default:o(({canScrollNext:k,canScrollPrev:x})=>[s(r(N),null,{default:o(()=>[a.loading?(e(!0),m(f,{key:0},_(Array(4),(l,b)=>(e(),t(r(d),{key:`product-skeleton-${b}`,class:"flex flex-col rounded h-full border p-2 border-gray-200 max-w-1/4 max-w-72 me-6 w-full"},{default:o(()=>[s(c,{class:"w-full h-32 bg-gray-200 mb-2"}),s(c,{class:"w-4/5 h-5 bg-gray-200 mb-2"}),s(c,{class:"w-1/3 h-5 bg-gray-200 mb-2"})]),_:2},1024))),128)):(e(!0),m(f,{key:1},_(a.products,l=>(e(),t(r(d),{key:l.productId,class:"basis-4/10"},{default:o(()=>[(e(),t(u,{key:`product-${l.productId}`,product:l,variant:"lg"},null,8,["product"]))]),_:2},1024))),128))]),_:1}),!a.loading&&x?(e(),t(r(S),{key:0})):n("",!0),!a.loading&&k?(e(),t(r(V),{key:1})):n("",!0)]),_:1})]),_:1})]),_:1})}}});export{K as _};
