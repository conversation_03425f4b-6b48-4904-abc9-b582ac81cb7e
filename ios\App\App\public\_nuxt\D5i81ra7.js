const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as N,u as R,ak as S,j as V,k as j,r as T,ac as A,c as B,w as r,o as D,e as s,a as t,h as _,t as m,q as n,f as M,Z as O,ag as J,a1 as U,g as Z,ah as G}from"./C3gyeM1K.js";import{_ as H}from"./BRCXRF_f.js";import{_ as K,a as Q,b as W}from"./BzSOFl6X.js";import{u as X,t as Y,o as b,s as d,F as oo}from"./ZoTeemxs.js";import{_ as eo}from"./CjR7N8Rt.js";import{_ as to}from"./CvMKnfiC.js";import{_ as so}from"./BoqtTPyX.js";import{c as ro,P as no}from"./DLHC-ANp.js";import{_ as ao}from"./C0H2RQFE.js";import"./D3IGe8e3.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./DYRsuAWe.js";import"./FUKBWQwh.js";import"./C2RgRyPS.js";import"./ClItKS3j.js";import"./DwXv0R8y.js";import"./jAU0Cazi.js";import"./DDFqsyHi.js";import"./BK9Yhb3N.js";import"./ytjl1pT4.js";import"./DSkQD3L1.js";import"./CY42fsWK.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./C00tNdvy.js";import"./BRIKaOVL.js";import"./C1DP8TeJ.js";import"./BYbD1TwY.js";import"./Ls81bkHL.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./DEEq2QX3.js";import"./B12VHTgT.js";import"./B9XwVa_7.js";const io=J(()=>G(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(f=>f.default||f)),lo={class:"flex flex-col w-full gap-4"},mo={class:"bg-gray-200 h-56 flex items-center justify-center rounded-t-lg"},po={class:"relative col-span-2 flex flex-col w-full gap-2 px-4"},co={class:"col-span-2 w-full px-4"},uo={class:"flex absolute top-7 end-4"},_o={class:"flex w-full flex-col gap-2 py-4"},fo={class:"flex justify-end"},ho={class:"flex flex-col gap-4"},go={class:"flex flex-col py-2 justify-center items-center relative w-full"},xo={class:"bg-white px-2 z-10 inline text-xs text-gray-500"},oe=N({__name:"login",setup(f){const w=R(),v=S(),p=V(),{t:a}=j(),c=T(!1),i=X({validationSchema:Y(b({password:d().min(7,a("error.required")),phone:b({number:d().min(5,a("error.required")),iso:d(),code:d()}).transform(o=>{const e=ro.find(u=>u.dial_code===o.code);return{...o,iso:(e==null?void 0:e.code)||o.iso}}).refine(o=>{if(!o.iso||!o.number)return!1;const e=no[o.iso];return e?new RegExp(`^${e[2]}$`).test(o.number)?!0:(i.setErrors({"phone.number":a("error.phone-number-invalid")}),!1):(i.setErrors({"phone.number":a("error.phone-number-invalid")}),!1)},{message:a("error.phone-number-invalid"),path:["number"]})})),initialValues:A({password:"",phone:{code:"962",iso:"JO",number:""}})}),$=()=>{const o={...p.query};return delete o.auth,v.push({path:p.path,query:o})},C=i.handleSubmit(async o=>{await w.login({phone:o.phone,password:o.password})});return(o,e)=>{const u=io,F=H,k=eo,P=to,q=Q,z=W,E=Z,I=K,L=oo,h=M,g=so;return D(),B(ao,{dismissible:!1,size:"!p-0","hide-close":!0,onClose:$},{body:r(()=>{var x,y;return[s("div",lo,[s("div",mo,[t(u,{src:"/images/logo.png",alt:"Action Mobile",class:"h-28"})]),s("div",po,[t(F,{error:(y=(x=n(i).errors)==null?void 0:x.value)==null?void 0:y.phone,onUpdate:e[0]||(e[0]=l=>{n(i).setFieldValue("phone",{number:l.nationalNumber,code:l.countryCallingCode,iso:l.countryCode})})},null,8,["error"])]),s("div",co,[t(L,{name:"password"},{default:r(({componentField:l})=>[t(I,{class:"relative"},{default:r(()=>[t(k,{class:"font-semibold"},{default:r(()=>[_(m(o.$t("form.password"))+"* ",1)]),_:1}),t(q,null,{default:r(()=>[t(P,U({type:n(c)?"text":"password",placeholder:o.$t("form.password")},l),null,16,["type","placeholder"])]),_:2},1024),t(z),s("div",uo,[s("button",{class:"p-1",onClick:e[1]||(e[1]=()=>c.value=!n(c))},[t(E,{name:n(c)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1})])])]}),footer:r(()=>[s("div",_o,[s("div",fo,[t(h,{to:`${n(p).path}?auth=forgot-password`,class:"hover:underline hover:text-primary-500 text-base text-gray-500"},{default:r(()=>[_(m(o.$t("auth.forget-password-question")),1)]),_:1},8,["to"])]),s("div",ho,[t(g,{class:"w-full",onClick:O(n(C),["prevent"])},{default:r(()=>[_(m(o.$t("auth.log-in")),1)]),_:1},8,["onClick"]),s("div",go,[s("span",xo,m(o.$t("auth.new-gust-hint")),1),e[2]||(e[2]=s("div",{class:"flex w-full bg-gray-200 absolute my-4 left-0 h-px z-0"},null,-1))]),t(g,{variant:"outline",class:"w-full"},{default:r(()=>[t(h,{to:`${n(p).path}?auth=signup`},{default:r(()=>[_(m(o.$t("auth.sign-up")),1)]),_:1},8,["to"])]),_:1})])])]),_:1})}}});export{oe as default};
