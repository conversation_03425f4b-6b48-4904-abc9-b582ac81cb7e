import{_ as i,u as c}from"./BD9sJOps.js";import{i as p,k as _,l,d as u,e,a as t,q as f,$ as d,o as x}from"./C3gyeM1K.js";import{_ as k}from"./B63kR5ej.js";import{_ as b}from"./CpoynfcS.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";import"./DAA1LH-g.js";import"./JNWsvxxg.js";import"./BsHCAE3W.js";import"./Bh9jP6R-.js";import"./BGs9QzNZ.js";import"./BCJZ1_ur.js";import"./CRC5xmnh.js";import"./BoqtTPyX.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";const g={class:"flex flex-col"},B={class:"flex gap-6 w-full my-6 max-sm:flex-col"},H=p({__name:"cart",setup(C){const{t:r}=_(),s=l(()=>{const{buildSinglePage:o}=c();return o(r("cart.page-title"))});return(o,$)=>{const a=i,m=k,n=b;return x(),u(d,null,[e("div",g,[t(a,{links:f(s)},null,8,["links"])]),e("div",B,[t(m),t(n)])],64)}}});export{H as default};
