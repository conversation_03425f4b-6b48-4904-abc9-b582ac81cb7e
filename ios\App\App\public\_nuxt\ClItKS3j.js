import{$ as c,i as d,aD as g,a1 as C,aE as h,A as p}from"./C3gyeM1K.js";function m(r){return r?r.flatMap(e=>e.type===c?m(e.children):[e]):[]}const S=d({name:"PrimitiveSlot",inheritAttrs:!1,setup(r,{attrs:e,slots:a}){return()=>{var l,u;if(!a.default)return null;const t=m(a.default()),o=t.findIndex(i=>i.type!==g);if(o===-1)return t;const n=t[o];(l=n.props)==null||delete l.ref;const f=n.props?C(e,n.props):e;e.class&&((u=n.props)!=null&&u.class)&&delete n.props.class;const s=h(n,f);for(const i in f)i.startsWith("on")&&(s.props||(s.props={}),s.props[i]=f[i]);return t.length===1?s:(t[o]=s,t)}}}),P=["area","img","input"],v=d({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(r,{attrs:e,slots:a}){const t=r.asChild?"template":r.as;return typeof t=="string"&&P.includes(t)?()=>p(t,e):t!=="template"?()=>p(r.as,e,{default:a.default}):()=>p(S,e,{default:a.default})}});export{v as P,S,m as r};
