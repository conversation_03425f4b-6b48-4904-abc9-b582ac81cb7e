import{_ as l}from"./Cn3HuTsa.js";import{i as u,Y as p,l as s,c as _,q as e,o as m}from"./C3gyeM1K.js";const h=u({__name:"suggestions",setup(d){const{data:r,error:o,status:t}=p("new-arrival",{query:{limit:8}});o.value&&console.error("Error fetching new arrival:",o.value);const a=s(()=>r.value),n=s(()=>t.value!=="success");return(c,f)=>{const i=l;return m(),_(i,{class:"my-8",title:c.$t("product.might-like"),products:e(a),loading:e(n)},null,8,["title","products","loading"])}}});export{h as _};
