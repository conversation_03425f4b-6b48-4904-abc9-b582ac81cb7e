import{i as n,a4 as u,a5 as d,q as o,d as i,o as m,W as p,K as f}from"./C3gyeM1K.js";import{a as c}from"./BBuxlZld.js";import{c as x}from"./jAU0Cazi.js";const k=n({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(a,{emit:s}){const e=a,l=c(e,"modelValue",s,{passive:!0,defaultValue:e.defaultValue});return(b,t)=>u((m(),i("input",{"onUpdate:modelValue":t[0]||(t[0]=r=>f(l)?l.value=r:null),class:p(o(x)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-foreground file:text-sm file:font-medium placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 outline-none",e.class))},null,2)),[[d,o(l)]])}});export{k as _};
