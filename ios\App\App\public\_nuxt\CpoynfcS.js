import{Skeleton as N}from"./BaodNYLQ.js";import{_ as L}from"./DAA1LH-g.js";import{_ as F}from"./JNWsvxxg.js";import{i as A,ai as I,r as b,l as m,ak as P,n as T,c as w,w as i,o,a as s,d as c,q as r,t as l,e,Z as V,$ as q,a0 as z,g as D,a2 as E,am as H,ab as K}from"./C3gyeM1K.js";import{_ as M}from"./BsHCAE3W.js";import{_ as O}from"./BoqtTPyX.js";import R from"./CuUhnQhf.js";import{u as Z}from"./BCJZ1_ur.js";const G={key:1,class:"text-xl font-bold text-gray-600"},J={key:0,class:"flex flex-col px-6 gap-2"},Q={key:1,class:"flex w-full flex-col gap-4 px-6"},U={class:"flex w-full justify-between gap-1 border border-gray-200 rounded-md"},W=["placeholder"],X={key:2,class:"flex flex-col px-6 gap-2"},Y={key:3,class:"flex w-full flex-col gap-4 px-6"},tt={class:"flex justify-between"},et={class:"text-base font-normal text-gray-600"},st={class:"text-base font-normal text-gray-600"},at={class:"flex justify-between"},nt={class:"text-base font-normal text-gray-600"},lt={key:4,class:"flex flex-col px-6 gap-2"},ot={key:5,class:"flex w-full flex-col gap-4 px-6"},ct={class:"flex justify-between"},rt={class:"text-base font-bold text-gray-600"},it={class:"text-base font-bold text-gray-700"},ut={class:"flex mb-2"},dt={class:"text-base font-normal text-gray-600"},pt={class:"flex rounded-full p-2 w-10 h-10 bg-primary-300 justify-center items-center"},ft={class:"flex flex-col flex-grow"},mt={class:"text-sm text-primary-600 font-semibold"},_t={class:"text-sm font-normal text-gray-500"},Ct=A({__name:"index",setup(xt){const{priceFormat:g}=Z(),_=I(),p=b([]),x=b(null),y=m(()=>{var t;return(t=p.value)==null?void 0:t.length}),u=m(()=>{var t;return _.fetching&&!((t=p.value)!=null&&t.length)}),h=m(()=>g(x.value.value));P(),T(()=>_,t=>{p.value=[...t.list],x.value=t.total},{deep:!0,immediate:!0});const v=[{title:"cart.privacy-payment-title",text:"cart.privacy-payment-text",icon:"ui:privacy-payment"},{title:"cart.insurance-payment-title",text:"cart.insurance-payment-text",icon:"ui:insurance-payment"},{title:"cart.secure-payment-title",text:"cart.secure-payment-text",icon:"ui:secure-payment"}],$=async()=>{const{$api:t}=E();return t("/orders/store",{method:"POST"}).then(a=>H(`checkout/${a.orderId}/1`)).catch(a=>{console.error(a),K.error((a==null?void 0:a.message)||"Something went wrong")})};return(t,a)=>{const n=N,k=M,f=O,C=R,S=D,j=F,B=L;return o(),w(B,{class:"w-2/6 max-sm:w-full"},{default:i(()=>[s(k,null,{default:i(()=>[r(u)?(o(),w(n,{key:0,class:"w-full h-10"})):(o(),c("span",G,l(t.$t("cart.details-title")),1))]),_:1}),s(j,{class:"px-0"},{default:i(()=>[r(u)?(o(),c("div",J,[s(n,{class:"w-full h-5"}),s(n,{class:"w-full h-5"})])):(o(),c("div",Q,[e("div",U,[e("input",{type:"number",placeholder:t.$t("cart.coupon-placeholder"),class:"border-none w-full bg-white no-spinner outline-none p-2 rounded-lg text-base"},null,8,W),s(f,{variant:"text",class:"sm"},{default:i(()=>[e("span",null,l(t.$t("cart.coupon-submit-title")),1)]),_:1})])])),a[1]||(a[1]=e("div",{class:"flex w-full border-dashed border border-gray-100 my-4"},null,-1)),r(u)?(o(),c("div",X,[s(n,{class:"w-full h-5"}),s(n,{class:"w-full h-5"})])):(o(),c("div",Y,[e("div",tt,[e("span",et,l(t.$t("cart.payment-sub-amount-title",{item:t.$t("cart.item",{count:r(y)}),number:r(y)})),1),e("span",st,l(r(h)),1)]),e("div",at,[e("span",nt,l(t.$t("cart.payment-tax-inclusive")),1),a[0]||(a[0]=e("span",null,null,-1))])])),a[2]||(a[2]=e("div",{class:"flex w-full border-dashed border border-gray-100 my-4"},null,-1)),r(u)?(o(),c("div",lt,[s(n,{class:"w-full h-6"}),s(n,{class:"w-full h-6 mb-4"}),s(n,{class:"w-full h-7"}),s(n,{class:"w-full h-7 mb-4"}),s(n,{class:"w-full h-9"}),s(n,{class:"w-full h-9"}),s(n,{class:"w-full h-9"})])):(o(),c("div",ot,[e("div",ct,[e("span",rt,l(t.$t("cart.total-title")),1),e("span",it,l(r(h)),1)]),e("div",ut,[e("span",dt,l(t.$t("cart.payment-delivery-note")),1)]),s(f,{onClick:V($,["prevent"])},{default:i(()=>[e("span",null,l(t.$t("cart-list.confirm-continue-pay")),1)]),_:1}),s(f,{"as-child":"",variant:"outline"},{default:i(()=>[s(C,{to:"/"},{default:i(()=>[e("span",null,l(t.$t("cart-list.confirm-continue")),1)]),_:1})]),_:1}),(o(),c(q,null,z(v,d=>e("div",{key:d.icon,class:"flex w-full border rounded border-gray-200 py-3 px-4 items-center gap-4 shadow"},[e("div",pt,[s(S,{name:`${d.icon}`,size:"20"},null,8,["name"])]),e("div",ft,[e("span",mt,l(t.$t(d.title)),1),e("span",_t,l(t.$t(d.text)),1)])])),64))]))]),_:1})]),_:1})}}});export{Ct as _};
