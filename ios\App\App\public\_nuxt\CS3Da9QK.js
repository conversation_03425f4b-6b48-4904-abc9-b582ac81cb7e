import{Skeleton as j}from"./BaodNYLQ.js";import{i as B,j as $,l as u,c as f,w as _,o,a as r,e as t,d as c,a0 as x,$ as h,W as d,q as l,g as z,t as p}from"./C3gyeM1K.js";import{_ as C}from"./DAA1LH-g.js";import{_ as L}from"./BsHCAE3W.js";const I={class:"relative grid grid-cols-4 gap-2 z-0"},N={class:"flex flex-col gap-2 flex-grow"},S={class:"flex p-2 gap-2 items-center justify-center bg-white max-md:flex-col max-md:justify-start"},V={class:"flex flex-col gap-1 justify-center max-w-28 max-sm:hidden"},q={class:"text-sm font-semibold text-gray-600"},A={class:"text-xs font-medium text-gray-500"},W=B({__name:"metro",props:{loading:{type:Boolean},isLocked:{type:Boolean}},setup(D){const g=$(),y=[{title:"checkout.contact-title",text:"checkout.contact-text",step:1},{title:"checkout.shipping-title",text:"checkout.shipping-text",step:2},{title:"checkout.pay-title",text:"checkout.pay-text",step:3},{title:"checkout.review-title",text:"checkout.review-text",step:4}],k=u(()=>{var e;return(e=g.params)==null?void 0:e.stepId}),a=u(()=>Number(k.value)-1);return(e,m)=>{const n=j,b=z,w=L,v=C;return o(),f(v,null,{default:_(()=>[r(w,null,{default:_(()=>[t("div",I,[e.loading?(o(!0),c(h,{key:0},x(Array(4),(i,s)=>(o(),c("div",{key:s,class:"flex col gap-2 items-center z-10 bg-white"},[r(n,{class:"w-8 h-8 rounded-full"}),t("div",N,[r(n,{class:"w-[60%] h-4"}),r(n,{class:"w-[60%] h-4"})])]))),128)):(o(),c(h,{key:1},x(y,(i,s)=>t("button",{key:s,class:"flex z-10 md:text-start max-sm:justify-center md:justify-normal"},[t("div",S,[t("div",{class:d(["flex rounded-full border-2 border-transparent p-1",{"!border-primary-600":s===l(a)}])},[t("div",{class:d(["flex p-2 bg-white border-2 border-gray-300 justify-center items-center rounded-full w-7 h-7",{"!bg-primary-600 border-none":s<=l(a)||e.isLocked}])},[s<l(a)||e.isLocked?(o(),f(b,{key:0,name:"lucide:check",size:"16px",class:"text-white"})):(o(),c("span",{key:1,class:d(["text-sm",{"text-white":s===l(a)}])},p(1+s),3))],2)],2),t("div",V,[t("span",q,p(e.$t(i.title)),1),t("span",A,p(e.$t(i.text)),1)])])])),64)),m[0]||(m[0]=t("div",{class:"flex border-t-2 border-gray-200 flex-grow absolute top-1/2 bottom-1/2 left-[10%] right-[10%]"},null,-1))])]),_:1})]),_:1})}}});export{W as _};
