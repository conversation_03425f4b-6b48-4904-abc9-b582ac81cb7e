import{ay as P,aZ as C,B as x,aB as z,a_ as H,a$ as I,b0 as D,a2 as O,ax as F,l as v,i as J,a3 as V,r as j,m as X,d as Z,s as Q,o as Y,a1 as M,q,v as K}from"./C3gyeM1K.js";async function ee(e,t){return await te(t).catch(r=>(console.error("Failed to get image meta for "+t,r+""),{width:0,height:0,ratio:0}))}async function te(e){if(typeof Image>"u")throw new TypeError("Image not supported");return new Promise((t,i)=>{const r=new Image;r.onload=()=>{const s={width:r.width,height:r.height,ratio:r.width/r.height};t(s)},r.onerror=s=>i(s),r.src=e})}function W(e){return t=>t?e[t]||t:e.missingValue}function E({formatter:e,keyMap:t,joinWith:i="/",valueMap:r}={}){e||(e=(o,n)=>`${o}=${n}`),t&&typeof t!="function"&&(t=W(t));const s=r||{};return Object.keys(s).forEach(o=>{typeof s[o]!="function"&&(s[o]=W(s[o]))}),(o={})=>Object.entries(o).filter(([c,d])=>typeof d<"u").map(([c,d])=>{const l=s[c];return typeof l=="function"&&(d=l(o[c])),c=typeof t=="function"?t(c):c,e(c,d)}).join(i)}function y(e=""){if(typeof e=="number")return e;if(typeof e=="string"&&e.replace("px","").match(/^\d+$/g))return Number.parseInt(e,10)}function re(e=""){if(e===void 0||!e.length)return[];const t=new Set;for(const i of e.split(" ")){const r=Number.parseInt(i.replace("x",""));r&&t.add(r)}return Array.from(t)}function ie(e){if(e.length===0)throw new Error("`densities` must not be empty, configure to `1` to render regular size only (DPR 1.0)")}function se(e){const t={};if(typeof e=="string")for(const i of e.split(/[\s,]+/).filter(r=>r)){const r=i.split(":");r.length!==2?t["1px"]=r[0].trim():t[r[0].trim()]=r[1].trim()}else Object.assign(t,e);return t}function oe(e){const t={options:e},i=(s,o={})=>k(t,s,o),r=(s,o={},n={})=>i(s,{...n,modifiers:P(o,n.modifiers||{})}).url;for(const s in e.presets)r[s]=(o,n,c)=>r(o,n,{...e.presets[s],...c});return r.options=e,r.getImage=i,r.getMeta=(s,o)=>ne(t,s,o),r.getSizes=(s,o)=>de(t,s,o),t.$img=r,r}async function ne(e,t,i){const r=k(e,t,{...i});return typeof r.getMeta=="function"?await r.getMeta():await ee(e,r.url)}function k(e,t,i){var l,u;if(t&&typeof t!="string")throw new TypeError(`input must be a string (received ${typeof t}: ${JSON.stringify(t)})`);if(!t||t.startsWith("data:"))return{url:t};const{provider:r,defaults:s}=ae(e,i.provider||e.options.provider),o=ce(e,i.preset);if(t=x(t)?t:C(t),!r.supportsAlias){for(const h in e.options.alias)if(t.startsWith(h)){const m=e.options.alias[h];m&&(t=z(m,t.slice(h.length)))}}if(r.validateDomains&&x(t)){const h=H(t).host;if(!e.options.domains.find(m=>m===h))return{url:t}}const n=P(i,o,s);n.modifiers={...n.modifiers};const c=n.modifiers.format;(l=n.modifiers)!=null&&l.width&&(n.modifiers.width=y(n.modifiers.width)),(u=n.modifiers)!=null&&u.height&&(n.modifiers.height=y(n.modifiers.height));const d=r.getImage(t,n,e);return d.format=d.format||c||"",d}function ae(e,t){const i=e.options.providers[t];if(!i)throw new Error("Unknown provider: "+t);return i}function ce(e,t){if(!t)return{};if(!e.options.presets[t])throw new Error("Unknown preset: "+t);return e.options.presets[t]}function de(e,t,i){var b,$,S,a,f;const r=y((b=i.modifiers)==null?void 0:b.width),s=y(($=i.modifiers)==null?void 0:$.height),o=se(i.sizes),n=(S=i.densities)!=null&&S.trim()?re(i.densities.trim()):e.options.densities;ie(n);const c=r&&s?s/r:0,d=[],l=[];if(Object.keys(o).length>=1){for(const g in o){const p=A(g,String(o[g]),s,c,e);if(p!==void 0){d.push({size:p.size,screenMaxWidth:p.screenMaxWidth,media:`(max-width: ${p.screenMaxWidth}px)`});for(const w of n)l.push({width:p._cWidth*w,src:N(e,t,i,p,w)})}}le(d)}else for(const g of n){const p=Object.keys(o)[0];let w=p?A(p,String(o[p]),s,c,e):void 0;w===void 0&&(w={size:"",screenMaxWidth:0,_cWidth:(a=i.modifiers)==null?void 0:a.width,_cHeight:(f=i.modifiers)==null?void 0:f.height}),l.push({width:g,src:N(e,t,i,w,g)})}ue(l);const u=l[l.length-1],h=d.length?d.map(g=>`${g.media?g.media+" ":""}${g.size}`).join(", "):void 0,m=h?"w":"x",_=l.map(g=>`${g.src} ${g.width}${m}`).join(", ");return{sizes:h,srcset:_,src:u==null?void 0:u.src}}function A(e,t,i,r,s){const o=s.options.screens&&s.options.screens[e]||Number.parseInt(e),n=t.endsWith("vw");if(!n&&/^\d+$/.test(t)&&(t=t+"px"),!n&&!t.endsWith("px"))return;let c=Number.parseInt(t);if(!o||!c)return;n&&(c=Math.round(c/100*o));const d=r?Math.round(c*r):i;return{size:t,screenMaxWidth:o,_cWidth:c,_cHeight:d}}function N(e,t,i,r,s){return e.$img(t,{...i.modifiers,width:r._cWidth?r._cWidth*s:void 0,height:r._cHeight?r._cHeight*s:void 0},i)}function le(e){var i;e.sort((r,s)=>r.screenMaxWidth-s.screenMaxWidth);let t=null;for(let r=e.length-1;r>=0;r--){const s=e[r];s.media===t&&e.splice(r,1),t=s.media}for(let r=0;r<e.length;r++)e[r].media=((i=e[r+1])==null?void 0:i.media)||""}function ue(e){e.sort((i,r)=>i.width-r.width);let t=null;for(let i=e.length-1;i>=0;i--){const r=e[i];r.width===t&&e.splice(i,1),t=r.width}}const fe=E({keyMap:{format:"f",fit:"fit",width:"w",height:"h",resize:"s",quality:"q",background:"b"},joinWith:"&",formatter:(e,t)=>I(e)+"_"+I(t)}),B=(e,{modifiers:t={},baseURL:i}={},r)=>{t.width&&t.height&&(t.resize=`${t.width}x${t.height}`,delete t.width,delete t.height);const s=fe(t)||"_";return i||(i=z(r.options.nuxt.baseURL,"/_ipx")),{url:z(i,s,D(e))}},G=!0,L=!0,ge=Object.freeze(Object.defineProperty({__proto__:null,getImage:B,supportsAlias:L,validateDomains:G},Symbol.toStringTag,{value:"Module"})),R=E({keyMap:{width:"width",height:"height",aspectRatio:"aspect_ratio",quality:"quality",sharpen:"sharpen",blur:"blur",crop:"crop",cropGravity:"crop_gravity",flip:"flip",flop:"flop",brightness:"brightness",saturation:"saturation",hue:"hue",contrast:"contrast",autoOptimize:"auto_optimize",sepia:"sepia"},joinWith:"&",formatter:(e,t)=>`${e}=${t}`}),he=(e,{modifiers:t={},baseURL:i}={})=>{const r=R(t);return{url:z(i,e+(r?"?"+r:""))}},me=Object.freeze(Object.defineProperty({__proto__:null,getImage:he,operationsGenerator:R},Symbol.toStringTag,{value:"Module"})),pe=Object.freeze(Object.defineProperty({__proto__:null,getImage:B,supportsAlias:L,validateDomains:G},Symbol.toStringTag,{value:"Module"})),U={screens:{xs:110,sm:600,md:992,lg:1300,xl:1700,xxl:1536,"2xl":1536},presets:{},provider:"ipxStatic",domains:[],alias:{},densities:[1,2],format:["webp","webp"],quality:90};U.providers={ipx:{provider:ge,defaults:{}},backend:{provider:me,defaults:{}},ipxStatic:{provider:pe,defaults:{}}};const T=()=>{const e=F(),t=O();return t.$img||t._img||(t._img=oe({...U,nuxt:{baseURL:e.app.baseURL},runtimeConfig:e}))};function ye(e){var t;(t=performance==null?void 0:performance.mark)==null||t.call(performance,"mark_feature_usage",{detail:{feature:e}})}const ve={src:{type:String,required:!1},format:{type:String,required:!1},quality:{type:[Number,String],required:!1},background:{type:String,required:!1},fit:{type:String,required:!1},modifiers:{type:Object,required:!1},preset:{type:String,required:!1},provider:{type:String,required:!1},sizes:{type:[Object,String],required:!1},densities:{type:String,required:!1},preload:{type:[Boolean,Object],required:!1},width:{type:[String,Number],required:!1},height:{type:[String,Number],required:!1},alt:{type:String,required:!1},referrerpolicy:{type:String,required:!1},usemap:{type:String,required:!1},longdesc:{type:String,required:!1},ismap:{type:Boolean,required:!1},loading:{type:String,required:!1,validator:e=>["lazy","eager"].includes(e)},crossorigin:{type:[Boolean,String],required:!1,validator:e=>["anonymous","use-credentials","",!0,!1].includes(e)},decoding:{type:String,required:!1,validator:e=>["async","auto","sync"].includes(e)},nonce:{type:[String],required:!1}},we=e=>{const t=v(()=>({provider:e.provider,preset:e.preset})),i=v(()=>({width:y(e.width),height:y(e.height),alt:e.alt,referrerpolicy:e.referrerpolicy,usemap:e.usemap,longdesc:e.longdesc,ismap:e.ismap,crossorigin:e.crossorigin===!0?"anonymous":e.crossorigin||void 0,loading:e.loading,decoding:e.decoding,nonce:e.nonce})),r=T(),s=v(()=>({...e.modifiers,width:y(e.width),height:y(e.height),format:e.format,quality:e.quality||r.options.quality,background:e.background,fit:e.fit}));return{options:t,attrs:i,modifiers:s}},_e={...ve,placeholder:{type:[Boolean,String,Number,Array],required:!1},placeholderClass:{type:String,required:!1},custom:{type:Boolean,required:!1}},be=["src"],qe=J({__name:"NuxtImg",props:_e,emits:["load","error"],setup(e,{emit:t}){const i=e,r=V(),s=t,o=!1,n=T(),c=we(i),d=j(!1),l=j(),u=v(()=>n.getSizes(i.src,{...c.options.value,sizes:i.sizes,densities:i.densities,modifiers:{...c.modifiers.value,width:y(i.width),height:y(i.height)}})),h=v(()=>{const a={...c.attrs.value,"data-nuxt-img":""};return(!i.placeholder||d.value)&&(a.sizes=u.value.sizes,a.srcset=u.value.srcset),a}),m=v(()=>{let a=i.placeholder;if(a===""&&(a=!0),!a||d.value)return!1;if(typeof a=="string")return a;const f=Array.isArray(a)?a:typeof a=="number"?[a,a]:[10,10];return n(i.src,{...c.modifiers.value,width:f[0],height:f[1],quality:f[2]||50,blur:f[3]||3},c.options.value)}),_=v(()=>i.sizes?u.value.src:n(i.src,c.modifiers.value,c.options.value)),b=v(()=>m.value?m.value:_.value),S=O().isHydrating;return X(()=>{if(m.value||i.custom){const a=new Image;_.value&&(a.src=_.value),i.sizes&&(a.sizes=u.value.sizes||"",a.srcset=u.value.srcset),a.onload=f=>{d.value=!0,s("load",f)},a.onerror=f=>{s("error",f)},ye("nuxt-image");return}l.value&&(l.value.complete&&S&&(l.value.getAttribute("data-error")?s("error",new Event("error")):s("load",new Event("load"))),l.value.onload=a=>{s("load",a)},l.value.onerror=a=>{s("error",a)})}),(a,f)=>a.custom?Q(a.$slots,"default",K(M({key:1},{...q(o)?{onerror:"this.setAttribute('data-error', 1)"}:{},imgAttrs:{...h.value,...q(r)},isLoaded:d.value,src:b.value}))):(Y(),Z("img",M({key:0,ref_key:"imgEl",ref:l,class:m.value&&!d.value?a.placeholderClass:void 0},{...q(o)?{onerror:"this.setAttribute('data-error', 1)"}:{},...h.value,...q(r)},{src:b.value}),null,16,be))}});export{qe as default,_e as imgProps};
