const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as N,Y as A,l as p,c as u,w as a,o as t,e as f,a as o,t as g,h as E,d as c,q as x,a0 as y,$ as h,ag as V,ah as j}from"./C3gyeM1K.js";import q from"./CuUhnQhf.js";import{Skeleton as z}from"./BaodNYLQ.js";import{_ as O}from"./DAA1LH-g.js";import{_ as P}from"./JNWsvxxg.js";const S=V(()=>j(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(s=>s.default||s)),D={class:"px-4 py-4 justify-between items-center flex"},I={class:"text-lg text-gray-600 font-semibold"},J=N({__name:"wearable",setup(s){const{data:b,error:l,status:w}=A("/products",{query:{perPage:4,categories:"wearables",orderBy:"numberOfOrder,desc"}});l.value&&console.error("Error fetching most wearable section:",l.value);const k=p(()=>{var e;return(e=b.value)==null?void 0:e.items}),v=p(()=>w.value!=="success");return(e,T)=>{const i=q,$=z,C=S,L=P,B=O;return t(),u(B,{class:"col-span-1 flex flex-col max-md:col-span-3"},{default:a(()=>[f("div",D,[f("span",I,g(e.$t("home.most-popular-wearable")),1),o(i,{to:"/categories/wearables",class:"max-sm:text-xs text-primary-600 text-nowrap"},{default:a(()=>[E(g(e.$t("home.see-more")),1)]),_:1})]),o(L,{class:"grid grid-cols-2 grid-rows-2 gap-4 px-4"},{default:a(()=>[x(v)?(t(!0),c(h,{key:0},y(Array(4),(r,n)=>(t(),c("div",{key:`loading-${n}`,class:"flex rounded-lg border-gray-200 border bg-gray-100 justify-center flex-col min-h-52 px-2 max-sm:min-h-36"},[o($,{class:"max-h-52 aspect-video"})]))),128)):(t(!0),c(h,{key:1},y(x(k),(r,n)=>(t(),u(i,{key:`product-${n}`,to:`/categories/wearables/${r.slug}`,class:"flex rounded-lg border-gray-200 border bg-gray-100 justify-center flex-col min-h-52 px-2 max-sm:min-h-36"},{default:a(()=>{var m,_,d;return[o(C,{class:"w-full",src:(d=(_=(m=r.media)==null?void 0:m.cover)==null?void 0:_[0])==null?void 0:d.src,width:"179",height:"181",format:"webp",quality:"90",fit:"contain",loading:"lazy"},null,8,["src"])]}),_:2},1032,["to"]))),128))]),_:1})]),_:1})}}});export{J as _};
