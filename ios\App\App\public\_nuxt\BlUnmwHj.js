const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as A,u as U,ak as B,j as M,r as V,k as D,ac as O,L as Z,c as J,w as s,o as G,e as a,a as o,h as u,t as m,q as r,f as H,Z as K,ag as Q,a1 as $,g as W,ah as X}from"./C3gyeM1K.js";import{_ as Y,a as ee,b as oe}from"./BzSOFl6X.js";import{u as te,t as se,o as E,s as p,F as re}from"./ZoTeemxs.js";import{_ as ae}from"./CjR7N8Rt.js";import{_ as ne}from"./CvMKnfiC.js";import{_ as le}from"./BRCXRF_f.js";import{_ as ie}from"./BoqtTPyX.js";import{_ as me}from"./C0H2RQFE.js";import{c as ue,P as pe}from"./DLHC-ANp.js";import"./ClItKS3j.js";import"./jAU0Cazi.js";import"./DSkQD3L1.js";import"./BK9Yhb3N.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./D3IGe8e3.js";import"./DYRsuAWe.js";import"./FUKBWQwh.js";import"./DwXv0R8y.js";import"./DDFqsyHi.js";import"./ytjl1pT4.js";import"./CY42fsWK.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./C00tNdvy.js";import"./BRIKaOVL.js";import"./C1DP8TeJ.js";import"./BYbD1TwY.js";import"./Ls81bkHL.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./DEEq2QX3.js";import"./B12VHTgT.js";import"./B9XwVa_7.js";const de=Q(()=>X(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(N=>N.default||N)),fe={class:"bg-gray-200 h-52 flex items-center justify-center rounded-t-lg"},ce={class:"grid grid-cols-2 gap-4 w-full overflow-y-auto min-h-[300px] px-4 py-6"},_e={class:"col-span-2 w-full"},he={class:"flex absolute top-7 end-4"},we={class:"col-span-2 w-full"},ge={class:"flex absolute top-7 end-4"},ye={class:"flex w-full flex-col px-4 gap-4 col-span-2 p-4"},be={class:"flex justify-end px-4 col-span-2"},ve={class:"flex flex-col py-2 justify-center items-center relative w-full"},xe={class:"bg-white px-2 z-10 inline text-xs text-gray-500"},ro=A({__name:"signup",setup(N){const S=U(),I=B(),f=M(),P=V(!1),c=V(!1),_=V(!1),{t:i}=D(),n=te({validationSchema:se(E({firstName:p().min(1,i("error.required")),lastName:p().min(1,i("error.required")),password:p().min(7,i("error.required")).regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/,{message:i("error.password-strength")}),confirmPassword:p().min(7,i("error.required")),phone:E({number:p().min(5,i("error.required")),iso:p(),code:p()}).transform(e=>{const t=ue.find(h=>h.dial_code===e.code);return{...e,iso:(t==null?void 0:t.code)||e.iso}}).refine(e=>{if(!e.iso||!e.number)return!1;const t=pe[e.iso];return t?new RegExp(`^${t[2]}$`).test(e.number)?!0:(n.setErrors({"phone.number":i("error.phone-number-invalid")}),!1):(n.setErrors({"phone.number":i("error.phone-number-invalid")}),!1)},{message:i("error.phone-number-invalid"),path:["number"]})}).refine(e=>e.password===e.confirmPassword,{path:["passwordConfirmation"],message:i("error.passwords-not-match")})),initialValues:O({firstName:"",lastName:"",password:"",confirmPassword:"",phone:{code:"962",iso:"JO",number:""}})}),L=n.handleSubmit(async e=>(P.value=!0,await S.signup({password:e.password,phone:e.phone,firstName:e.firstName,lastName:e.lastName}),Z(()=>{P.value=!1}))),R=()=>{const e={...f.query};return delete e.auth,I.push({path:f.path,query:e})};return(e,t)=>{const h=de,w=ae,g=ne,y=ee,b=oe,v=Y,x=re,T=le,C=W,F=H,k=ie,j=me;return G(),J(j,{size:"!p-0",dismissible:!1,"hide-close":!0,onClose:R},{body:s(()=>{var q,z;return[a("div",fe,[o(h,{src:"/images/logo.png",alt:"Action Mobile",class:"h-24"})]),a("div",ce,[o(x,{name:"firstName"},{default:s(({componentField:l})=>[o(v,null,{default:s(()=>[o(w,{class:"font-semibold"},{default:s(()=>[u(m(e.$t("form.first-name"))+"* ",1)]),_:1}),o(y,null,{default:s(()=>[o(g,$({modelValue:r(n).values.firstName,"onUpdate:modelValue":t[0]||(t[0]=d=>r(n).values.firstName=d),type:"text",placeholder:e.$t("form.first-name")},l),null,16,["modelValue","placeholder"])]),_:2},1024),o(b)]),_:2},1024)]),_:1}),o(x,{name:"lastName"},{default:s(({componentField:l})=>[o(v,null,{default:s(()=>[o(w,{class:"font-semibold"},{default:s(()=>[u(m(e.$t("form.last-name"))+"* ",1)]),_:1}),o(y,null,{default:s(()=>[o(g,$({modelValue:r(n).values.lastName,"onUpdate:modelValue":t[1]||(t[1]=d=>r(n).values.lastName=d),type:"text",placeholder:e.$t("form.last-name")},l),null,16,["modelValue","placeholder"])]),_:2},1024),o(b)]),_:2},1024)]),_:1}),o(T,{error:(z=(q=r(n).errors)==null?void 0:q.value)==null?void 0:z.phone,onUpdate:t[2]||(t[2]=l=>{r(n).setFieldValue("phone",{number:l.nationalNumber,code:l.countryCallingCode,iso:l.countryCode})})},null,8,["error"]),a("div",_e,[o(x,{name:"password"},{default:s(({componentField:l})=>[o(v,{class:"relative"},{default:s(()=>[o(w,{class:"font-semibold"},{default:s(()=>[u(m(e.$t("form.password"))+"* ",1)]),_:1}),o(y,null,{default:s(()=>[o(g,$({type:r(c)?"text":"password",placeholder:e.$t("form.password")},l),null,16,["type","placeholder"])]),_:2},1024),o(b),a("div",he,[a("button",{class:"p-1",onClick:t[3]||(t[3]=()=>c.value=!r(c))},[o(C,{name:r(c)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1})]),a("div",we,[o(x,{name:"confirmPassword"},{default:s(({componentField:l})=>[o(v,{class:"w-full relative"},{default:s(()=>[o(w,{class:"font-semibold"},{default:s(()=>[u(m(e.$t("form.confirm-password"))+"* ",1)]),_:1}),o(y,null,{default:s(()=>[o(g,$({modelValue:r(n).values.confirmPassword,"onUpdate:modelValue":t[4]||(t[4]=d=>r(n).values.confirmPassword=d),type:r(_)?"text":"password",placeholder:e.$t("form.new-password")},l),null,16,["modelValue","type","placeholder"])]),_:2},1024),o(b),a("div",ge,[a("button",{class:"p-1",onClick:t[5]||(t[5]=()=>_.value=!r(_))},[o(C,{name:r(_)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1})])])]}),footer:s(()=>[a("div",ye,[a("div",be,[o(F,{to:`${r(f).path}?auth=forgot-password`,class:"hover:underline hover:text-primary-500 text-base text-gray-500"},{default:s(()=>[u(m(e.$t("auth.forget-password-question")),1)]),_:1},8,["to"])]),o(k,{class:"w-full",onClick:t[6]||(t[6]=K(()=>r(L)(),["prevent"]))},{default:s(()=>[u(m(e.$t("auth.sign-up")),1)]),_:1}),a("div",ve,[a("span",xe,m(e.$t("auth.new-gust-hint")),1),t[7]||(t[7]=a("div",{class:"flex w-full bg-gray-200 absolute my-4 left-0 h-px z-0"},null,-1))]),o(k,{variant:"outline",class:"w-full"},{default:s(()=>[o(F,{to:`${r(f).path}?auth=login`},{default:s(()=>[u(m(e.$t("auth.log-in")),1)]),_:1},8,["to"])]),_:1})])]),_:1})}}});export{ro as default};
