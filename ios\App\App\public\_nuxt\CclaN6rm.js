const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css","./CuUhnQhf.js"])))=>i.map(i=>d[i]);
import{i as b,Y as F,l as r,z as R,d as s,a as o,e as a,q as i,$ as p,a0 as l,o as e,c as T,w as M,ag as d,ah as f,_ as W}from"./C3gyeM1K.js";import{_ as Y}from"./CemPb5Vw.js";import{_ as j}from"./eQvoTSBy.js";import{_ as G}from"./BZhNrtWy.js";import{Skeleton as J}from"./BaodNYLQ.js";import{_ as K}from"./DMQvEO6t.js";import Q from"./CDajOx9j.js";import{_ as U}from"./Di2nr7z5.js";import X from"./DEucmY1t.js";import{_ as Z}from"./Dd-JsqA8.js";import oo from"./BnHZ6AaK.js";import{_ as to}from"./Cz72SlET.js";import{_ as mo}from"./CNsNSWH9.js";import{_ as eo}from"./BknFFv3H.js";import{_ as no}from"./DJYsuLfG.js";import{_ as so}from"./t8uN8OsQ.js";import{_ as _o}from"./GBv7s3T4.js";import{u as ro}from"./BQBaqVsz.js";import{_ as ao}from"./BEHaPyd5.js";import"./BoqtTPyX.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./DAA1LH-g.js";import"./JNWsvxxg.js";import"./CiqmSqHP.js";import"./CRC5xmnh.js";import"./CuUhnQhf.js";import"./C4nYFGHw.js";import"./lU3sMj3q.js";import"./BGs9QzNZ.js";import"./BCJZ1_ur.js";import"./C0H2RQFE.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";import"./BrsQk_-I.js";import"./D3J2c5MI.js";import"./BsHCAE3W.js";import"./Dk8jasUa.js";import"./BL991R2A.js";import"./CvhKjyCU.js";const io=d(()=>f(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(t=>t.default||t)),co=d(()=>f(()=>import("./CuUhnQhf.js"),__vite__mapDeps([3,1,2]),import.meta.url).then(t=>t.default||t)),po={class:"content grid grid-cols-3 gap-6 py-6"},lo={class:"col-span-3 grid grid-cols-2 gap-6 max-md:grid-cols-1"},fo={class:"col-span-3 grid grid-cols-4 gap-2 max-md"},uo={class:"col-span-1 flex flex-col gap-4 max-sm:col-span-4"},ho=b({__name:"index",setup(t){const{data:_,status:u}=F("banners",{query:{limit:2}}),h=r(()=>u.value==="pending"),g=r(()=>(_==null?void 0:_.value)||[]),x=r(()=>[...g.value].splice(0,2)),v=R(),{setSeoData:H}=ro({pageName:"home",fullPath:v("/")});return H(),(go,xo)=>{const $=Y,y=j,w=G,k=J,A=io,B=co,L=K,D=Q,E=U,N=X,P=Z,z=oo,I=to,S=mo,C=eo,V=no,q=so,O=_o;return e(),s("div",po,[o($),o(i(ao)),o(y),o(w),a("div",lo,[i(h)?(e(!0),s(p,{key:0},l(Array(2),(m,n)=>(e(),s("div",{key:`loading-brand-${n}`,class:"w-full max-h-72 h-full rounded-lg shadow overflow-hidden"},[o(k,{class:"w-full min-h-32"})]))),128)):(e(!0),s(p,{key:1},l(i(x),m=>(e(),T(B,{key:m.bannerId,to:m.url,class:"w-full max-h-72 rounded-lg shadow overflow-hidden"},{default:M(()=>{var n,c;return[o(A,{src:(c=(n=m==null?void 0:m.media)==null?void 0:n.image)==null?void 0:c.preview,class:"w-full h-full"},null,8,["src"])]}),_:2},1032,["to"]))),128))]),o(L),o(D),a("div",fo,[o(E),a("div",uo,[o(N),o(P)])]),o(z),o(I),o(S),o(C),o(V),o(q),o(O)])}}}),gt=W(ho,[["__scopeId","data-v-e624ffbc"]]);export{gt as default};
