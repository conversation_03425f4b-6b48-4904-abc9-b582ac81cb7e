import{_ as h}from"./DAA1LH-g.js";import{_ as $}from"./JNWsvxxg.js";import{_ as v}from"./BsHCAE3W.js";import{Skeleton as C}from"./BaodNYLQ.js";import{_ as B}from"./Dk8jasUa.js";import{i as w,Y as A,l as c,c as l,w as t,o as e,a as r,e as S,t as q,d as o,q as m,a0 as _,$ as d}from"./C3gyeM1K.js";const E={class:"text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl"},Y=w({__name:"best-brands",setup(N){const{data:i,error:n,status:p}=A("brands",{query:{perPage:4,labels:"best-brands",orderBy:"createdAt,desc"},server:!1});n.value&&console.error("Error fetching categories:",n.value);const f=c(()=>{var s;return(s=i.value)==null?void 0:s.items}),u=c(()=>p.value!=="success");return(s,V)=>{const g=v,x=C,b=$,y=h;return e(),l(y,{class:"col-span-3 max-sm:col-span-4"},{default:t(()=>[r(g,{class:"px-4 py-4"},{default:t(()=>[S("span",E,q(s.$t("home.brand-title")),1)]),_:1}),r(b,{class:"grid gap-4 px-4 xs:grid-cols-1 lg:grid-cols-2"},{default:t(()=>[m(u)?(e(!0),o(d,{key:0},_(Array(4),(a,k)=>(e(),o("div",{key:`brand-loading-${k}`,class:"col-span-1 flex sm:min-h-64 xs:min-h-52 min-w-full gap-4"},[r(x,{class:"w-full h-full"})]))),128)):(e(!0),o(d,{key:1},_(m(f),a=>(e(),l(B,{key:a.brandId,brand:a},null,8,["brand"]))),128))]),_:1})]),_:1})}}});export{Y as _};
