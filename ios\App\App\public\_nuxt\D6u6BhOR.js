import{_ as v,a as S,b as B}from"./BzSOFl6X.js";import{u as T,t as V,o as q,s as p,F as I}from"./ZoTeemxs.js";import{_ as x}from"./CjR7N8Rt.js";import{_ as M}from"./CvMKnfiC.js";import{_ as j}from"./BoqtTPyX.js";import{_ as D}from"./C0H2RQFE.js";import{i as L,k as P,c as z,w as a,o as A,e as y,a as e,h as s,t as l,a1 as $,q as E}from"./C3gyeM1K.js";const G={class:"flex flex-col px-4 gap-4"},H={class:"flex w-full items-center gap-2"},Y=L({__name:"user-info",props:{user:{}},emits:["close","update"],setup(m,{emit:g}){var h,F,N;const{t:n}=P(),r=g,w=T({validationSchema:V(q({email:p().email(n("error.email")),firstName:p().min(1,n("error.requited")),lastName:p().min(1,n("error.requited"))})),initialValues:{email:((h=m.user)==null?void 0:h.email)??"",firstName:((F=m.user)==null?void 0:F.firstName)??"",lastName:((N=m.user)==null?void 0:N.lastName)??""}}).handleSubmit(t=>{r("update",t),r("close")});return(t,b)=>{const i=x,f=M,c=S,_=B,u=v,d=I,C=j,k=D;return A(),z(k,{dismissible:!1,"hide-close":"",title:t.$t("form.user-info"),onClose:b[0]||(b[0]=o=>r("close"))},{body:a(()=>[y("div",G,[e(d,{name:"email"},{default:a(({componentField:o})=>[e(u,{class:"w-full"},{default:a(()=>[e(i,{class:"font-bold"},{default:a(()=>[s(l(t.$t("form.email"))+"* ",1)]),_:1}),e(c,null,{default:a(()=>[e(f,$({type:"email",placeholder:t.$t("form.email")},o),null,16,["placeholder"])]),_:2},1024),e(_)]),_:2},1024)]),_:1}),y("div",H,[e(d,{name:"firstName"},{default:a(({componentField:o})=>[e(u,{class:"w-full"},{default:a(()=>[e(i,{class:"font-bold"},{default:a(()=>[s(l(t.$t("form.first-name"))+"* ",1)]),_:1}),e(c,null,{default:a(()=>[e(f,$({type:"text",placeholder:t.$t("form.first-name")},o),null,16,["placeholder"])]),_:2},1024),e(_)]),_:2},1024)]),_:1}),e(d,{name:"lastName"},{default:a(({componentField:o})=>[e(u,{class:"w-full"},{default:a(()=>[e(i,{class:"font-bold"},{default:a(()=>[s(l(t.$t("form.last-name"))+"* ",1)]),_:1}),e(c,null,{default:a(()=>[e(f,$({type:"text",placeholder:t.$t("form.last-name")},o),null,16,["placeholder"])]),_:2},1024),e(_)]),_:2},1024)]),_:1})]),e(C,{onClick:E(w)},{default:a(()=>[s(l(t.$t("form.save")),1)]),_:1},8,["onClick"])])]),_:1},8,["title"])}}});export{Y as _};
