import{_ as J,a as Q,b as W}from"./BzSOFl6X.js";import{i as ee,k as se,X as ae,r as F,ac as le,l as oe,a2 as te,ab as re,d as g,e as o,a as s,t as r,w as a,a4 as k,ae as R,q as i,Z as ne,$ as j,Y as de,o as w,h as f,ad as ie,a1 as b,a0 as me}from"./C3gyeM1K.js";import{u as ue,t as fe,o as L,a as K,s as u,n as P,b as O,F as ce}from"./ZoTeemxs.js";import{_ as pe}from"./CjR7N8Rt.js";import{_ as _e}from"./CvMKnfiC.js";import{_ as he}from"./BRCXRF_f.js";import{_ as be}from"./BoqtTPyX.js";import{c as ye,P as ve}from"./DLHC-ANp.js";const xe={class:"flex gap-2 px-4 w-full min-w-3xl mb-2 max-sm:flex-col"},ge={class:"grid grid-cols-2 gap-6 col-span-1 sm:w-1/2 xs:w-full"},we={class:"text-lg col-span-2 font-bold"},$e={value:null},Fe=["value"],Te={class:"flex flex-col col-span-2 gap-2"},ke={class:"flex gap-2 items-center"},Pe={class:"text-sm font-semibold"},Ne={class:"text-gray-500 text-sm"},Se={class:"flex gap-4 items-center cursor-pointer mt-2"},Ce={class:"flex gap-2 items-center"},qe={for:"house",class:"text-sm"},Ie={class:"flex gap-2 items-center"},De={for:"work",class:"text-sm"},Ve={class:"grid grid-cols-2 gap-6 col-span-1 sm:w-1/2 xs:w-full"},Ae={class:"text-lg font-bold text-nowrap"},Ee={class:"flex justify-end gap-4 w-full my-2 max-sm:my-8 max-sm:justify-between items-center py-4"},Ue={key:0},Be={key:1},Ze=ee({__name:"form",props:{address:{default:null}},emits:["fetch:address","close:address"],async setup(y,{emit:X}){var I,D,V,A,E;let $,N;const S=X,{t:d}=se(),{data:Y}=([$,N]=ae(()=>de("lookups-website/cities")),$=await $,N(),$),C=F({...y.address}),Z=F(((I=C.value)==null?void 0:I.default)===1),z=F((D=C.value)==null?void 0:D.isProfileDataShared),T=F(!1),n=ue({validationSchema:fe(L({cityId:P().min(1,d("error.required")).nullish().refine(e=>e!=null,{message:d("error.required")}),district:u().min(1,d("error.required")),firstName:u().min(1,d("error.required")),lastName:u().min(1,d("error.required")),recipientName:u().min(1,d("error.required")),fullAddress:u().optional(),buildingType:u().min(1,d("error.required")),default:K([O(),P()]).optional(),isProfileDataShared:K([O(),P()]).optional(),phone:L({number:u().min(5,d("error.required")),iso:u(),code:u()}).transform(e=>{const l=ye.find(m=>m.dial_code===e.code);return{...e,iso:(l==null?void 0:l.code)||e.iso}}).refine(e=>{if(!e.iso||!e.number)return!1;const l=ve[e.iso];return l?new RegExp(`^${l[2]}$`).test(e.number)?!0:(n.setErrors({phone:d("error.phone-number-invalid")}),!1):(n.setErrors({phone:d("error.phone-number-invalid")}),!1)},{message:d("error.phone-number-invalid"),path:["number"]})})),initialValues:le({...y.address,phone:{...(V=y.address)==null?void 0:V.phone,number:`${((E=(A=y.address)==null?void 0:A.phone)==null?void 0:E.number)??""}`}})}),q=oe(()=>{var e;return!!((e=y.address)!=null&&e.addressId)}),G=n.handleSubmit(async()=>{T.value=!0;const e={...n.values,default:Z.value?1:0,isProfileDataShared:z.value,phone:{...n.values.phone}},{$api:l}=te();return l("addresses",{method:q.value?"PUT":"POST",body:e}).then(()=>{re.success(q.value?d("form.address-updated-success"):d("form.address-added-success")),S("fetch:address",!1),T.value=!1,n.resetForm()})});return(e,l)=>{var B,M;const m=pe,c=Q,p=W,_=J,h=ce,v=_e,H=he,U=be;return w(),g(j,null,[o("div",xe,[o("div",ge,[o("span",we,r(e.$t("form.address-modal-title")),1),s(h,{name:"cityId",class:"col-span-1"},{default:a(({componentField:t})=>[s(_,{class:"w-full flex flex-col"},{default:a(()=>[s(m,{class:"font-semibold"},{default:a(()=>[f(r(e.$t("form.city"))+"* ",1)]),_:1}),s(c,null,{default:a(()=>[k(o("select",b({id:"city","onUpdate:modelValue":l[0]||(l[0]=x=>i(n).values.cityId=x),name:"city"},t,{class:"text-sm !outline-none border border-gray-200 rounded px-2 py-1 h-11"}),[o("option",$e,r(e.$t("form.select-city")),1),(w(!0),g(j,null,me(i(Y),x=>(w(),g("option",{key:`city_id_${x.value}`,value:x.value},r(x.text),9,Fe))),128))],16),[[ie,i(n).values.cityId,void 0,{number:!0}]])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"district",class:"col-span-1"},{default:a(({componentField:t})=>[s(_,{class:"w-full flex flex-col"},{default:a(()=>[s(m,{class:"font-semibold"},{default:a(()=>[f(r(e.$t("form.area"))+"* ",1)]),_:1}),s(c,null,{default:a(()=>[s(v,b({class:"h-11",type:"text",placeholder:e.$t("form.area")},t),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"fullAddress"},{default:a(({componentField:t})=>[s(_,{class:"w-full flex flex-col col-span-2"},{default:a(()=>[s(m,{class:"font-semibold"},{default:a(()=>[f(r(e.$t("form.full-address"))+"* ",1)]),_:1}),s(c,null,{default:a(()=>[s(v,b({class:"h-11",type:"text",placeholder:e.$t("form.full-address")},t),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),o("div",Te,[o("div",ke,[o("span",Pe,r(e.$t("form.receipt-details")),1),o("span",Ne,"("+r(e.$t("form.optional"))+")",1)]),o("div",Se,[o("div",Ce,[k(o("input",{id:"house","onUpdate:modelValue":l[1]||(l[1]=t=>i(n).values.buildingType=t),type:"radio",name:"location",value:"home",class:"w-4 h-4"},null,512),[[R,i(n).values.buildingType]]),o("label",qe,r(e.$t("form.house")),1)]),o("div",Ie,[k(o("input",{id:"work","onUpdate:modelValue":l[2]||(l[2]=t=>i(n).values.buildingType=t),type:"radio",name:"location",value:"work",class:"w-4 h-4"},null,512),[[R,i(n).values.buildingType]]),o("label",De,r(e.$t("form.work")),1)])])])]),l[6]||(l[6]=o("div",{class:"flex w-px bg-gray-100 flex-grow mx-2 min-h-full p-px shadow-sm max-sm:min-w-full max-sm:min-h-auto max-sm:my-4"},null,-1)),o("div",Ve,[o("span",Ae,r(e.$t("form.receipt-details")),1),s(H,{error:(M=(B=i(n).errors)==null?void 0:B.value)==null?void 0:M.phone,onUpdate:l[3]||(l[3]=t=>{i(n).setFieldValue("phone",{number:t.nationalNumber,code:t.countryCallingCode,iso:t.countryCode})})},null,8,["error"]),s(h,{name:"firstName"},{default:a(({componentField:t})=>[s(_,{class:"w-full flex flex-col col-span-1"},{default:a(()=>[s(m,{class:"font-semibold"},{default:a(()=>[f(r(e.$t("form.first-name"))+"* ",1)]),_:1}),s(c,null,{default:a(()=>[s(v,b({class:"h-11",type:"text",placeholder:e.$t("form.first-name")},t),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"lastName"},{default:a(({componentField:t})=>[s(_,{class:"w-full flex flex-col col-span-1"},{default:a(()=>[s(m,{class:"font-semibold"},{default:a(()=>[f(r(e.$t("form.last-name"))+"* ",1)]),_:1}),s(c,null,{default:a(()=>[s(v,b({class:"h-11",type:"text",placeholder:e.$t("form.last-name")},t),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1}),s(h,{name:"recipientName"},{default:a(({componentField:t})=>[s(_,{class:"w-full flex flex-col col-span-2"},{default:a(()=>[s(m,{class:"font-semibold"},{default:a(()=>[f(r(e.$t("form.recipient-name"))+"* ",1)]),_:1}),s(c,null,{default:a(()=>[s(v,b({class:"h-11",type:"text",placeholder:e.$t("form.recipient-name")},t),null,16,["placeholder"])]),_:2},1024),s(p)]),_:2},1024)]),_:1})])]),o("div",Ee,[s(U,{variant:"outline",class:"max-sm:w-full",onClick:l[4]||(l[4]=ne(()=>S("close:address"),["prevent"]))},{default:a(()=>[f(r(e.$t("form.cancel")),1)]),_:1}),s(U,{class:"max-sm:w-full",disabled:i(T),onClick:l[5]||(l[5]=()=>i(G)())},{default:a(()=>[e.address.addressId?(w(),g("span",Ue,r(e.$t("form.edit-address")),1)):(w(),g("span",Be,r(e.$t("form.save-address")),1))]),_:1},8,["disabled"])])],64)}}});export{Ze as _};
