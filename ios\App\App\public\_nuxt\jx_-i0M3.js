const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as z,l as y,r as A,c as B,w as m,o,e as t,d as c,q as l,a as r,g as F,t as a,h as g,a0 as w,W as E,$ as k,ag as N,Z as P,z as T,am as V,ah as Y,_ as M}from"./C3gyeM1K.js";import{_ as O}from"./BoqtTPyX.js";import S from"./D-ucjfQv.js";import{_ as q}from"./JNWsvxxg.js";import{u as R}from"./BCJZ1_ur.js";import{u as W}from"./CRC5xmnh.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./BispfOyS.js";const Z=N(()=>Y(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(i=>i.default||i)),G={class:"w-full h-full"},H={key:0,class:"flex flex-col w-full h-full items-center gap-6 py-14"},J={class:"text-lg max-w-sm text-center font-semibold"},K=["onClick"],Q={class:"header flex w-full justify-between items-center gap-4 text-base font-semibold p-4 max-sm:items-start max-sm:bg-sky-50 max-sm:flex-col-reverse"},U={class:"flex sm:items-center sm:justify-between gap-6 max-sm:flex-col"},X={class:"flex items-center gap-2"},tt={class:"text-gray-600"},et={class:"text-primary-600"},st={class:"flex items-center gap-2"},at={class:"text-gray-600"},ot={class:"text-primary-600"},nt={class:"flex items-center gap-2"},ct={class:"text-gray-600"},lt={class:"text-primary-600"},rt={class:"children flex flex-col gap-4"},it={class:"flex w-20 h-14 items-center justify-center bg-gray-100 p-2 rounded shadow"},mt={class:"flex w-full gap-2"},_t={class:"text-base font-normal"},dt={class:"flex w-full justify-end"},pt=z({__name:"active-list",props:{orders:{}},setup(i){const{priceFormat:b}=R(),_=y(()=>i.orders.filter(e=>e.status)),I=y(()=>{var e;return!((e=_.value)!=null&&e.length)}),d=A(null),$=e=>{const p=T();V(p(`/my/orders/${e}/tracking`))};return(e,p)=>{const C=F,u=O,D=S,L=Z,j=q;return o(),B(j,null,{default:m(()=>[t("div",G,[l(I)?(o(),c("div",H,[r(C,{name:"ui:empty-orders",class:"w-full h-60"}),t("span",J,a(e.$t("orders.empty-text")),1),r(u,null,{default:m(()=>[g(a(e.$t("orders.empty-text-btn")),1)]),_:1})])):(o(!0),c(k,{key:1},w(l(_),s=>(o(),c("div",{key:s.orderId,class:E(["flex w-full rounded-lg flex-col border cursor-pointer my-4",{active:l(d)===s.orderId}]),onClick:n=>d.value=s.orderId},[t("div",Q,[t("div",U,[t("div",X,[t("span",tt,a(e.$t("orders.number"))+": ",1),t("span",et,a(s==null?void 0:s.orderId),1)]),t("div",st,[t("span",at,a(e.$t("orders.date"))+": ",1),t("span",ot,a(("useDateFormat"in e?e.useDateFormat:l(W))(s==null?void 0:s.createdAt,"DD-MM-YYYY hh:mm A")),1)]),t("div",nt,[t("span",ct,a(e.$t("orders.total-amount"))+": ",1),t("span",lt,a(l(b)(s.total.value)),1)])]),r(D,{status:s.status},null,8,["status"])]),t("div",rt,[(o(!0),c(k,null,w(s.orderItems,n=>{var f,h,x,v;return o(),c("div",{key:n.orderId,class:"flex w-full gap-4 items-center"},[t("div",it,[r(L,{src:(v=(x=(h=(f=n.product)==null?void 0:f.media)==null?void 0:h.cover)==null?void 0:x[0])==null?void 0:v.preview,class:"object-contain max-h-14 p-2"},null,8,["src"])]),t("div",mt,[t("span",_t,a(n.product.name),1)])])}),128)),t("div",dt,[r(u,{size:"sm",onClick:P(n=>$(s.orderId),["prevent"])},{default:m(()=>[g(a(e.$t("orders.track")),1)]),_:2},1032,["onClick"])])])],10,K))),128))])]),_:1})}}}),$t=M(pt,[["__scopeId","data-v-63c6eaf3"]]);export{$t as default};
