import{_ as k}from"./BoqtTPyX.js";import{_ as F}from"./C0H2RQFE.js";import{i as R,ak as B,j as q,r as h,k as A,c as x,w as n,q as r,o as w,e as i,a as N,h as P,t as S,Z as V,al as $}from"./C3gyeM1K.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";const z={class:"relative col-span-2 flex flex-col w-full gap-2 px-4"},D={class:"flex w-full flex-col gap-2 py-4"},M={class:"flex flex-col gap-4"},nt=R({__name:"index",setup(j){const g=B(),a=q(),o=h(1),p=h(null),{t:e}=A(),l=()=>{const t={...a.query};return delete t.auth,g.push({path:a.path,query:t})},s=[{},{component:"AuthForgotPasswordForm",title:e("form.reset-password-title"),text:e("form.reset-password-text"),btn:e("form.continue")},{component:"AuthForgotPasswordOtp",btn:e("form.verify")},{component:"AuthForgotPasswordReset",btn:e("form.change-password")}],v=async()=>{var t;await((t=p.value)==null?void 0:t.submitForm())};return(t,m)=>{var c,f,u,d;const y=k,b=F;return w(),x(b,{dismissible:!1,size:(c=s[r(o)])!=null&&c.title?"":"!pt-0",title:(f=s[r(o)])==null?void 0:f.title,description:(u=s[r(o)])==null?void 0:u.text,"hide-close":!((d=s[r(o)])!=null&&d.title),onClose:l},{body:n(()=>{var _;return[i("div",z,[(w(),x($((_=s[r(o)])==null?void 0:_.component),{ref_key:"formRef",ref:p,"onSet:step":m[0]||(m[0]=C=>o.value=C),"onClose:modal":l},null,544))])]}),footer:n(()=>[i("div",D,[i("div",M,[N(y,{class:"w-full",onClick:V(v,["prevent"])},{default:n(()=>[P(S(t.$t("form.continue")),1)]),_:1})])])]),_:1},8,["size","title","description","hide-close"])}}});export{nt as default};
