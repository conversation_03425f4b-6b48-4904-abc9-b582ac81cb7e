import{_ as V}from"./BoqtTPyX.js";import{i as B,r as g,j as I,u as T,k as A,l as h,n as F,d as c,e as o,c as v,p as u,t as i,q as t,w,a4 as S,$ as j,a0 as q,a5 as z,W as x,o as n,h as y,Z as D,a as L,g as M,L as R,a2 as E,ab as K}from"./C3gyeM1K.js";const O={id:"rating-form",class:"flex flex-col w-full gap-3"},P={class:"flex font-semibold text-base"},U={class:"flex font-semibold text-sm text-gray-500"},W={key:1,class:"flex flex-col w-full gap-4"},Z={class:"text-sm text-gray-500"},G={class:"flex w-full gap-2"},H=["onClick"],J=["value","placeholder"],Q=["placeholder"],X={class:"flex w-full justify-end items-center"},te=B({__name:"rate-form",props:{productId:{}},setup(b){const l=g(!1),m=I(),{user:a}=T(),{t:$}=A(),k=h(()=>`${a==null?void 0:a.firstName} ${a==null?void 0:a.lastName}`),s=g({rating:0,description:""});F(m,()=>{R(()=>{var e;return l.value=((e=m.query)==null?void 0:e.form)==="rating"})},{immediate:!0});const p=h(()=>s.value.rating>0&&s.value.description.length>2),C=async()=>{const{$api:e}=E();e(`products/${b.productId}/rating`,{method:"POST",body:{rating:s.value.rating,review:s.value.description}}).then(()=>{K.success($("form.submit-rate-success")),s.value={rating:0,description:""}}).catch(r=>{throw r})};return(e,r)=>{const f=V,N=M;return n(),c("div",O,[o("div",P,i(e.$t("product.write-review-title")),1),o("div",U,i(e.$t("product.share-review-title")),1),t(l)?u("",!0):(n(),v(f,{key:0,class:"max-w-40 mt-2",onClick:r[0]||(r[0]=()=>l.value=!0)},{default:w(()=>[y(i(e.$t("product.write-review-btn-title")),1)]),_:1})),t(l)?(n(),c("div",W,[o("span",Z,i(e.$t("product.rate")),1),o("div",G,[(n(!0),c(j,null,q(Array(5),(_,d)=>(n(),c("button",{key:`form-rating-${d}`,class:x(t(s).rating>=1+d?"text-rating-200":"text-rating-100"),onClick:D(()=>t(s).rating=1+d,["prevent"])},[L(N,{name:"ui:rate-star"})],10,H))),128))]),o("input",{value:t(k),disabled:!0,type:"text",name:"username",placeholder:e.$t("form.full-name-placeholder"),class:"border border-gray-300 rounded-md text-sm font-medium text-gray-500 h-10 p-3 outline-none"},null,8,J),S(o("textarea",{"onUpdate:modelValue":r[1]||(r[1]=_=>t(s).description=_),name:"description",placeholder:e.$t("form.rate-description-placeholder"),class:"border border-gray-300 rounded-md text-sm font-medium text-gray-500 p-3 outline-none resize-none",rows:"5"},null,8,Q),[[z,t(s).description]]),o("div",X,[t(a).userId?(n(),v(f,{key:0,class:x(["w-36",{"opacity-50 cursor-not-allowed":!t(p)}]),disabled:!t(p),onClick:C},{default:w(()=>[y(i(e.$t("form.submit-rate-title")),1)]),_:1},8,["disabled","class"])):u("",!0)])])):u("",!0)])}}});export{te as _};
