const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Hyyn1Q8Z.js","./DEEq2QX3.js","./D3IGe8e3.js","./C3gyeM1K.js","./entry.CGfGZREn.css","./jAU0Cazi.js","./DDFqsyHi.js","./BBuxlZld.js","./CRC5xmnh.js","./C2RgRyPS.js","./ClItKS3j.js","./FUKBWQwh.js","./BK9Yhb3N.js","./CY42fsWK.js","./B12VHTgT.js","./DSkQD3L1.js","./ytjl1pT4.js","./B9XwVa_7.js","./Dq4ukG8h.js","./BoqtTPyX.js","./D8XAHdeJ.js","./D1vxl8R8.js","./695lZGB_.js","./CuUhnQhf.js","./BGs9QzNZ.js","./BCJZ1_ur.js","./index.CD1rGYUI.css","./DIdjh0X0.js","./40aoOQ_a.js","./lU3sMj3q.js","./C0H2RQFE.js","./Bs_HQ-gR.js","./L0mwwrGq.js","./ZidGD314.js"])))=>i.map(i=>d[i]);
import{i as A,ag as v,A as C,a1 as P,aP as V,X as g,u as k,aQ as R,j as T,ak as q,l as B,an as D,d as S,e as w,a as e,s as H,w as c,$ as b,Y as M,o as N,q as h,ah as m,_ as F}from"./C3gyeM1K.js";import Q from"./aD373m6i.js";import $ from"./oTfqGtV6.js";import{_ as j}from"./L0mwwrGq.js";import{u as W}from"./BBuxlZld.js";import{u as X}from"./CvhKjyCU.js";import"./DQHUSZRG.js";import"./CuUhnQhf.js";import"./Bjqrwoww.js";import"./BQWT7oJn.js";import"./CUNFy6WN.js";import"./D3IGe8e3.js";import"./jAU0Cazi.js";import"./D_nR533G.js";import"./DYRsuAWe.js";import"./CRC5xmnh.js";import"./FUKBWQwh.js";import"./C2RgRyPS.js";import"./ClItKS3j.js";import"./DwXv0R8y.js";import"./B12VHTgT.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./DDFqsyHi.js";import"./BK9Yhb3N.js";import"./BRIKaOVL.js";import"./CB0C_qKx.js";import"./C00tNdvy.js";import"./B5rxJs06.js";import"./ytjl1pT4.js";import"./CY42fsWK.js";import"./DSkQD3L1.js";import"./CoTDbsFa.js";import"./BaodNYLQ.js";import"./BispfOyS.js";import"./D1vxl8R8.js";import"./BoqtTPyX.js";import"./D8XAHdeJ.js";import"./BrsQk_-I.js";import"./BrbpxSLN.js";import"./BuJZm6eP.js";import"./UTzmdM4z.js";import"./Tzk92Vbf.js";import"./BMPHpF8N.js";import"./B-49JoAv.js";import"./WtLCqwhA.js";function Y(t,n){return(_,d)=>A({inheritAttrs:!1,props:t,emits:["hydrated"],setup(o,s){const i=v({loader:d}),a=v({hydrate:n(o),loader:()=>Promise.resolve(i)}),l=()=>{s.emit("hydrated")};return()=>C(a,P(s.attrs,{onVnodeMounted:l}))}})}const p=Y({hydrateOnIdle:{type:[Number,Boolean],required:!0}},t=>t.hydrateOnIdle===0?void 0:V(t.hydrateOnIdle===!0?void 0:t.hydrateOnIdle)),G=p("components/drawer/wish-list/index.vue",()=>m(()=>import("./Hyyn1Q8Z.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]),import.meta.url).then(t=>t.default||t)),J=p("components/drawer/cart-list/index.vue",()=>m(()=>import("./DIdjh0X0.js"),__vite__mapDeps([27,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,28,19,20,21,29,23,24,25,30,26]),import.meta.url).then(t=>t.default||t)),K=p("components/auth/index.vue",()=>m(()=>import("./Bs_HQ-gR.js"),__vite__mapDeps([31,32,3,4]),import.meta.url).then(t=>t.default||t)),U=p("components/ui/sonner/index.ts",()=>m(()=>import("./ZidGD314.js"),__vite__mapDeps([33,3,4]),import.meta.url).then(t=>t.Toaster||t)),Z={class:"layout"},tt={class:"container overflow-hidden"},et=A({__name:"default",async setup(t){let n,_;const d=W("(min-width: 600px)"),{data:o}=([n,_]=g(()=>M("/my/profile")),n=await n,_(),n),s=k(),i=R(),a=T(),l=q(),y=B(()=>{var r;return(r=a.query)==null?void 0:r.drawer});X(()=>({htmlAttrs:{lang:i.value.htmlAttrs.lang,dir:i.value.htmlAttrs.dir},link:[...i.value.link||[]],meta:[...i.value.meta||[]]}));const f=()=>{const r={...a.query};delete r.drawer,l.push({path:a.path,query:r})};return D(async()=>{o!=null&&o.value&&(s.user=o==null?void 0:o.value)}),(r,ot)=>{const x=Q,I=$,L=G,z=J,u=j,O=K,E=U;return N(),S(b,null,[w("div",Z,[e(x),w("div",tt,[H(r.$slots,"default",{},void 0,!0)]),e(I)]),e(u,null,{default:c(()=>[e(L,{"is-open":h(y)==="wishlist","hydrate-on-idle":"","onSet:wishList":f},null,8,["is-open"]),e(z,{"is-open":h(y)==="cart","hydrate-on-idle":"","onSet:cartList":f},null,8,["is-open"])]),_:1}),e(u,null,{default:c(()=>[e(O,{"hydrate-on-idle":""})]),_:1}),e(u,null,{default:c(()=>[e(E,{"rich-colors":"","toast-options":{duration:8e3},position:h(d)?"bottom-right":"top-center","hydrate-on-idle":""},null,8,["position"])]),_:1})],64)}}}),Gt=F(et,[["__scopeId","data-v-b1ed0046"]]);export{Gt as default};
