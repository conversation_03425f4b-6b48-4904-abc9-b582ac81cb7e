<script setup lang="ts">
// @ts-nocheck
import { SliderRange, SliderRoot, SliderThumb, SliderTrack } from 'reka-ui'
import { useDebounceFn } from '@vueuse/core'
import { useCurrency } from '~/composables/useCurrency'

const { price, update } = defineProps<{
	price: string | number | unknown
	update: Function
}>()
const route = useRoute()
const { priceFormat } = useCurrency()

const { locale } = useI18n()
const appDir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')

/** default min value (From the route or from the filter price )**/
const defaultMinValue = computed(() => {
	return Number((route.query?.price as string)?.split(':')?.[0] || price?.config?.min || 0)
})

/** default max value (From the route or from the filter price )**/
const defaultMaxValue = computed(() => {
	return Number((route.query?.price as string)?.split(':')?.[1] || price?.config?.max || 0)
})

/** Price Range **/
const priceRange = ref([defaultMinValue.value, defaultMaxValue.value])

/** update the filter debouncing */
const debouncedFn = useDebounceFn(() => {
	let value = null

	// make sure the value is not default
	if (priceRange.value[0] !== price?.config?.min || priceRange.value[1] !== price?.config?.max) {
		value = `${priceRange.value[0]}:${priceRange.value[1]}`
	}

	if (value || Boolean(route.query?.price)) {
		update(value)
	}
}, 500)

/** Handle on slide the price slider **/
const handleOnSlide = (value) => {
	priceRange.value = value
	debouncedFn()
}

/** Format the min price range value **/
const minRangeFormater = (value: number) => {
	if (value < priceRange.value[1]) {
		return value
	}

	return priceRange.value[1] - 5
}

/** Format the max price range value **/
const maxRangeFormater = (value: number) => {
	if (value > priceRange.value[0]) {
		return value
	}

	return priceRange.value[0] + 5
}

/** handle on update min price range **/
const onUpdateMinRange = (value: number) => {
	if (value >= priceRange.value[1]) {
		priceRange.value[0] = priceRange.value[1] - 5
		debouncedFn()
		return
	}
	priceRange.value[0] = value
	debouncedFn()
}

/** handle on update max price range **/
const onUpdateMaxRange = (value: number) => {
	if (value <= priceRange.value[0]) {
		priceRange.value[1] = priceRange.value[0] + 5
		debouncedFn()
		return
	}
	priceRange.value[1] = value
	debouncedFn()
}
</script>

<template>
	<div
		class="flex flex-col w-full pb-6 gap-1 border-b border-gray-200"
	>
		<div class="flex w-full mb-2 text-sm font-bold">
			{{ $t('filters.price-title') }}
		</div>
		<div
			class="flex w-full justify-between text-xs mb-1"
		>
			<span>{{ priceFormat(price.config.min) }}</span>
			<span>{{ priceFormat(price.config.max) }}</span>
		</div>
		<SliderRoot
			v-model="priceRange"
			:max="price.config.max"
			:min="price.config.min"
			:dir="appDir"
			:step="1"
			direction="horizontal-reverce"
			class="relative flex items-center select-none touch-none w-full h-5"
			:min-steps-between-thumbs="1"
			@update:model-value="handleOnSlide"
		>
			<SliderTrack class="bg-gray-300 relative grow rounded-full h-1 cursor-pointer">
				<SliderRange class="absolute bg-grass8 rounded-full h-full bg-primary-600" />
				<SliderRange class="absolute bg-grass8 rounded-full h-full bg-primary-600" />
			</SliderTrack>

			<SliderThumb
				class="block w-4 h-4 cursor-pointer bg-white rounded-full border border-primary-600 hover:bg-stone-50 shadow-sm focus:outline-none focus:shadow-[white] focus:shadow-grass9"
				aria-label="Volume"
			/>

			<SliderThumb
				class="block w-4 h-4 cursor-pointer bg-white rounded-full border border-primary-600 hover:bg-stone-50 shadow-sm focus:outline-none focus:shadow-[white] focus:shadow-grass9"
				aria-label="Volume"
			/>
		</SliderRoot>
		<div class="flex justify-between gap-4 text-xs items-center">
			<PriceInput
				v-model="priceRange[0]"
				type="number"
				min="0"
				max="100"
				:format-value="minRangeFormater"
				@update:price="onUpdateMinRange"
			/>
			<span>-</span>

			<PriceInput
				v-model="priceRange[1]"
				type="number"
				min="0"
				max="100"
				:format-value="maxRangeFormater"
				@update:price="onUpdateMaxRange"
			/>
		</div>
	</div>
</template>

<style scoped lang="scss">

</style>
