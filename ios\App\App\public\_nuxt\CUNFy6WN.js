import{b as v,a as R}from"./D3IGe8e3.js";import{i as y,c,o as f,w as u,s as m,a1 as w,Z as E,q as e,v as _,r as D,T as A,K as I,m as K,a as P,aK as z,L as T,x as L,l as U}from"./C3gyeM1K.js";import{c as x}from"./jAU0Cazi.js";import{i as b,a as k,b as W,c as j,d as V,e as N}from"./D_nR533G.js";import{u as B,c as q}from"./C2RgRyPS.js";import{u as H}from"./ytjl1pT4.js";import{P as Z}from"./CY42fsWK.js";import{u as $}from"./DSkQD3L1.js";import{a as G}from"./BBuxlZld.js";import{u as J}from"./BRIKaOVL.js";import{P as Q}from"./ClItKS3j.js";const X=y({__name:"MenuRootContentModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(i,{emit:r}){const t=i,d=r,a=v(t,d),o=b(),{forwardRef:s,currentElement:n}=B();return H(n),(l,p)=>(f(),c(k,w(e(a),{ref:e(s),"trap-focus":e(o).open.value,"disable-outside-pointer-events":e(o).open.value,"disable-outside-scroll":!0,onDismiss:p[0]||(p[0]=g=>e(o).onOpenChange(!1)),onFocusOutside:p[1]||(p[1]=E(g=>d("focusOutside",g),["prevent"]))}),{default:u(()=>[m(l.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),Y=y({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(i,{emit:r}){const a=v(i,r),o=b();return(s,n)=>(f(),c(k,w(e(a),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:n[0]||(n[0]=l=>e(o).onOpenChange(!1))}),{default:u(()=>[m(s.$slots,"default")]),_:3},16))}}),ee=y({__name:"MenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(i,{emit:r}){const a=v(i,r),o=b(),s=W();return(n,l)=>(f(),c(e(Z),{present:n.forceMount||e(o).open.value},{default:u(()=>[e(s).modal.value?(f(),c(X,_(w({key:0},{...n.$attrs,...e(a)})),{default:u(()=>[m(n.$slots,"default")]),_:3},16)):(f(),c(Y,_(w({key:1},{...n.$attrs,...e(a)})),{default:u(()=>[m(n.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),[M,oe]=q("DropdownMenuRoot"),te=y({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean},open:{type:Boolean,default:void 0},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(i,{emit:r}){const t=i,d=r;B();const a=G(t,"open",d,{defaultValue:t.defaultOpen,passive:t.open===void 0}),o=D(),{modal:s,dir:n}=A(t),l=J(n);return oe({open:a,onOpenChange:p=>{a.value=p},onOpenToggle:()=>{a.value=!a.value},triggerId:"",triggerElement:o,contentId:"",modal:s,dir:l}),(p,g)=>(f(),c(e(j),{open:e(a),"onUpdate:open":g[0]||(g[0]=h=>I(a)?a.value=h:null),dir:e(l),modal:e(s)},{default:u(()=>[m(p.$slots,"default",{open:e(a)})]),_:3},8,["open","dir","modal"]))}}),ne=y({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(i){const r=i,t=M(),{forwardRef:d,currentElement:a}=B();return K(()=>{t.triggerElement=a}),t.triggerId||(t.triggerId=$(void 0,"reka-dropdown-menu-trigger")),(o,s)=>(f(),c(e(V),{"as-child":""},{default:u(()=>[P(e(Q),{id:e(t).triggerId,ref:e(d),type:o.as==="button"?"button":void 0,"as-child":r.asChild,as:o.as,"aria-haspopup":"menu","aria-expanded":e(t).open.value,"aria-controls":e(t).open.value?e(t).contentId:void 0,"data-disabled":o.disabled?"":void 0,disabled:o.disabled,"data-state":e(t).open.value?"open":"closed",onClick:s[0]||(s[0]=async n=>{var l;!o.disabled&&n.button===0&&n.ctrlKey===!1&&((l=e(t))==null||l.onOpenToggle(),await T(),e(t).open.value&&n.preventDefault())}),onKeydown:s[1]||(s[1]=z(n=>{o.disabled||(["Enter"," "].includes(n.key)&&e(t).onOpenToggle(),n.key==="ArrowDown"&&e(t).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(n.key)&&n.preventDefault())},["enter","space","arrow-down"]))},{default:u(()=>[m(o.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}}),ae=y({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(i,{emit:r}){const a=v(i,r);B();const o=M(),s=D(!1);function n(l){l.defaultPrevented||(s.value||setTimeout(()=>{var p;(p=o.triggerElement.value)==null||p.focus()},0),s.value=!1,l.preventDefault())}return o.contentId||(o.contentId=$(void 0,"reka-dropdown-menu-content")),(l,p)=>{var g;return f(),c(e(ee),w(e(a),{id:e(o).contentId,"aria-labelledby":(g=e(o))==null?void 0:g.triggerId,style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"},onCloseAutoFocus:n,onInteractOutside:p[0]||(p[0]=h=>{var C;if(h.defaultPrevented)return;const O=h.detail.originalEvent,F=O.button===0&&O.ctrlKey===!0,S=O.button===2||F;(!e(o).modal.value||S)&&(s.value=!0),(C=e(o).triggerElement.value)!=null&&C.contains(h.target)&&h.preventDefault()})}),{default:u(()=>[m(l.$slots,"default")]),_:3},16,["id","aria-labelledby"])}}}),ge=y({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(i,{emit:r}){const a=v(i,r);return(o,s)=>(f(),c(e(te),_(L(e(a))),{default:u(()=>[m(o.$slots,"default")]),_:3},16))}}),we=y({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(i,{emit:r}){const t=i,d=r,a=U(()=>{const{class:s,...n}=t;return n}),o=v(a,d);return(s,n)=>(f(),c(e(N),null,{default:u(()=>[P(e(ae),w(e(o),{class:e(x)("z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t.class)}),{default:u(()=>[m(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),he=y({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(i){const t=R(i);return(d,a)=>(f(),c(e(ne),w({class:"outline-none"},e(t)),{default:u(()=>[m(d.$slots,"default")]),_:3},16))}});export{he as _,we as a,ge as b};
