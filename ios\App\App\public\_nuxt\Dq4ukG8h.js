import{i as g,l as a,ai as L,aj as k,d as o,q as c,a as r,e as C,t as m,w as $,$ as B,a0 as F,o as s,g as N,h as S,c as T}from"./C3gyeM1K.js";import{_ as V}from"./BoqtTPyX.js";import{_ as b}from"./695lZGB_.js";const A={class:"flex flex-col w-full h-full text-gray-700 overflow-y-auto max-h-full"},D={key:0,class:"flex flex-col w-full h-full items-center gap-6"},E={class:"flex text-lg max-w-xs text-center font-semibold"},I={key:1,class:"flex flex-col max-w-full gap-6 p-4"},z=g({__name:"items",emits:["add:wish-list"],setup(R,{emit:d}){const u=d,_=a(()=>{var t;return(t=n.value)==null?void 0:t.length}),f=a(()=>!_.value),p=L(),e=k(),n=a(()=>(e==null?void 0:e.list)||[]),h=async t=>{await e.removeFromList(t)},w=async t=>{await p.addToList(t)};return(t,l)=>{const x=N,y=V,v=b;return s(),o("div",A,[c(f)?(s(),o("div",D,[r(x,{name:"ui:empty-wish-list",class:"w-full h-60 mt-20"}),C("div",E,m(t.$t("wish-list.empty-text")),1),r(y,{variant:"default",class:"w-1/2",onClick:l[0]||(l[0]=()=>u("add:wish-list"))},{default:$(()=>[S(m(t.$t("wish-list.empty-button")),1)]),_:1})])):(s(),o("div",I,[(s(!0),o(B,null,F(c(n),i=>(s(),T(v,{key:`wish-list-item-${i.productId}`,product:i,"onRemove:wishList":h,"onAdd:cart":w},null,8,["product"]))),128))]))])}}});export{z as _};
