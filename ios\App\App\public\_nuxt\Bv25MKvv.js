import{_ as z,u as U}from"./BD9sJOps.js";import{i as H,k as K,r as v,l as M,a2 as O,ab as G,d as _,a as e,q as i,w as t,$ as C,o as h,e as r,t as n,h as b,a1 as g,a0 as J,aq as V,g as Q}from"./C3gyeM1K.js";import{_ as R}from"./DAA1LH-g.js";import{_ as W}from"./JNWsvxxg.js";import{_ as X}from"./BsHCAE3W.js";import{_ as Y,a as Z,b as ee}from"./BzSOFl6X.js";import{u as oe,t as te,o as T,s,b as ae,F as le}from"./ZoTeemxs.js";import{_ as re}from"./CjR7N8Rt.js";import{_ as ne}from"./CvMKnfiC.js";import{_ as se}from"./BRCXRF_f.js";import{_ as me}from"./BoqtTPyX.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./DSkQD3L1.js";import"./BK9Yhb3N.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./D3IGe8e3.js";import"./DYRsuAWe.js";import"./FUKBWQwh.js";import"./DwXv0R8y.js";import"./DDFqsyHi.js";import"./ytjl1pT4.js";import"./CY42fsWK.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./C00tNdvy.js";import"./BRIKaOVL.js";import"./C1DP8TeJ.js";import"./BYbD1TwY.js";import"./Ls81bkHL.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";const ie={class:"font-bold text-2xl"},ce={class:"text-lg text-gray-600"},pe={class:"flex flex-col gap-4 w-full rounded-lg border shadow p-4 justify-center"},ue={class:"whitespace-pre-wrap text-center"},de=["placeholder"],fe={class:"flex col-span-1"},_e={class:"flex justify-between items-start gap-4 w-full max-sm:flex-col"},he=["href"],eo=H({__name:"contact-us",setup(be){const{t:a}=K(),$=v(1),j=M(()=>U().buildSinglePage(a("contact.title"))),q=v([{icon:"ui:email",name:a("contact.email"),text:a("contact.email-note"),value:"<EMAIL>",bg:"#DBEAFE",color:"#2563EB",link:"mailto:<EMAIL>"},{icon:"ui:phone",name:a("contact.phone"),text:a("contact.phone-note"),value:"+962-791009595",bg:"#F3F9FC",color:"#893477",link:"tel:+962-791009595"},{icon:"ui:whatsapp",name:a("contact.whatsapp"),text:a("contact.whatsapp-note"),value:"+962-791009595",bg:"#D4EDD1",color:"#29A71A",link:"https://wa.me/962791009595"}]),m=oe({validationSchema:te(T({fullName:s().min(1,a("error.required")),email:s().email(a("error.email")),problemType:s().min(1,a("error.required")),message:s().min(1,a("error.required")),phone:T({number:s().min(5,a("error.required")),iso:s().min(1,a("error.required")),code:s().min(1,a("error.required")),isValid:ae()}).refine(o=>o.isValid?!0:m.setErrors({phone:a("error.phone-number-invalid")}),{message:a("error.phone-number-invalid")})})),initialValues:{fullName:"",email:"",problemType:"",phone:{number:"",code:"",iso:"",isValid:!1},message:""}}),B=o=>{m.setFieldValue("phone",{number:o.nationalNumber,code:o.countryCallingCode,iso:o.countryCode,isValid:o.isValid})},N=m.handleSubmit(o=>{const{$api:w}=O();return w("/contact-us",{method:"POST",body:o}).then(()=>{G.success(a("form.contact-saved-successfully")),m.resetForm(),$.value+=1}).catch(y=>{console.log(y)})});return(o,w)=>{const y=z,S=X,c=re,x=ne,p=Z,u=ee,d=Y,f=le,E=se,D=me,P=Q,A=W,I=R;return h(),_(C,null,[e(y,{links:i(j),class:"!border-0 !shadow-none"},null,8,["links"]),e(I,{class:"flex flex-col w-full h-full gap-2 my-6"},{default:t(()=>[e(S,{class:"text-center justify-center gap-4 rounded-lg"},{default:t(()=>[r("h1",ie,n(o.$t("contact.title")),1),r("h2",ce,n(o.$t("contact.sub-title")),1)]),_:1}),e(A,{class:"grid grid-cols-2 gap-6 py-12 max-md:grid-cols-1"},{default:t(()=>{var F,k;return[(h(),_("form",{key:`form-${i($)}`,class:"flex col-span-1"},[r("div",pe,[r("span",ue,n(o.$t("contact.form-title")),1),e(f,{name:"fullName"},{default:t(({componentField:l})=>[e(d,{class:"w-full relative"},{default:t(()=>[e(c,{class:"font-bold"},{default:t(()=>[b(n(o.$t("form.full-name"))+"* ",1)]),_:1}),e(p,null,{default:t(()=>[e(x,g({type:"text",placeholder:o.$t("form.full-name")},l),null,16,["placeholder"])]),_:2},1024),e(u)]),_:2},1024)]),_:1}),e(f,{name:"email"},{default:t(({componentField:l})=>[e(d,{class:"w-full relative"},{default:t(()=>[e(c,{class:"font-bold"},{default:t(()=>[b(n(o.$t("form.email"))+"* ",1)]),_:1}),e(p,null,{default:t(()=>[e(x,g({type:"email",placeholder:o.$t("form.email")},l),null,16,["placeholder"])]),_:2},1024),e(u)]),_:2},1024)]),_:1}),e(E,{error:(k=(F=i(m).errors)==null?void 0:F.value)==null?void 0:k.phone,onUpdate:B},null,8,["error"]),e(f,{name:"problemType"},{default:t(({componentField:l})=>[e(d,{class:"w-full relative"},{default:t(()=>[e(c,{class:"font-bold"},{default:t(()=>[b(n(o.$t("form.problem-type"))+"* ",1)]),_:1}),e(p,null,{default:t(()=>[e(x,g({type:"text",placeholder:o.$t("form.problem-type")},l),null,16,["placeholder"])]),_:2},1024),e(u)]),_:2},1024)]),_:1}),e(f,{name:"message"},{default:t(({componentField:l})=>[e(d,{class:"w-full relative"},{default:t(()=>[e(c,{class:"font-bold"},{default:t(()=>[b(n(o.$t("form.message"))+"* ",1)]),_:1}),e(p,null,{default:t(()=>[r("textarea",g({class:"flex border w-full min-h-20 p-2 rounded outline-none text-sm",placeholder:o.$t("form.message"),rows:"7"},l),null,16,de)]),_:2},1024),e(u)]),_:2},1024)]),_:1}),e(D,{onClick:i(N)},{default:t(()=>[r("span",null,n(o.$t("form.send-message")),1)]),_:1},8,["onClick"])])])),r("div",fe,[r("div",_e,[(h(!0),_(C,null,J(i(q),(l,L)=>(h(),_("div",{key:`info-${L}`,class:"flex flex-col gap-2 border rounded-lg shadow w-1/3 text-xs justify-center items-center py-4 max-sm:w-full"},[r("div",{class:"flex flex-col p-2 w-10 h-10 items-center justify-center rounded-full",style:V({background:l.bg})},[e(P,{name:l.icon,size:"20px"},null,8,["name"])],4),r("span",null,n(l.name),1),r("span",null,n(l.text),1),r("a",{dir:"ltr",style:V({color:l.color}),href:l.link},n(l.value),13,he)]))),128))])])]}),_:1})]),_:1})],64)}}});export{eo as default};
