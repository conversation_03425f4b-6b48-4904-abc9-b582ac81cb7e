const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as b,k as v,l as k,r as C,d as i,a as e,q as m,w as c,$ as _,o as l,e as n,ag as $,t as o,a0 as B,g as j,ah as z}from"./C3gyeM1K.js";import{_ as I,u as L}from"./BD9sJOps.js";import{_ as E}from"./DAA1LH-g.js";import{_ as N}from"./JNWsvxxg.js";import{_ as P}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const V=$(()=>z(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(s=>s.default||s)),q={class:"absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0"},A={class:"font-bold text-2xl max-sm:text-sm"},D={class:"text-xl font-semibold max-sm:text-xs"},S={class:"flex justify-between items-center"},F={class:"text-base font-semibold"},H={class:"text-sm text-gray-600"},W=b({__name:"why-action",setup(s){const{t}=v(),d=k(()=>L().buildSinglePage(t("why-action.title"))),p=C([{title:t("why-action.title-1"),text:t("why-action.text-1"),icon:"ui:truck"},{title:t("why-action.title-2"),text:t("why-action.text-2"),icon:"ui:insurance"},{title:t("why-action.title-3"),text:t("why-action.text-3"),icon:"ui:retune"},{title:t("why-action.title-4"),text:t("why-action.text-4"),icon:"ui:best-price"},{title:t("why-action.title-5"),text:t("why-action.text-5"),icon:"ui:online-pay"},{title:t("why-action.title-6"),text:t("why-action.text-6"),icon:"ui:options"},{title:t("why-action.title-7"),text:t("why-action.text-7"),icon:"ui:easy-use"}]);return(r,O)=>{const u=I,x=V,h=P,w=j,y=N,f=E;return l(),i(_,null,[e(u,{links:m(d),class:"!border-0 !shadow-none"},null,8,["links"]),e(f,{class:"flex flex-col w-full h-full gap-2 my-6"},{default:c(()=>[e(h,{class:"relative text-center justify-center gap-4 text-white rounded-lg p-0"},{default:c(()=>[e(x,{src:"/images/why-action/lg.png",sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw",srcset:"/images/why-action/sm.png 640w,/images/why-action/md.png 1024w, /images/why-action/lg.png 1920w",width:"1920",height:"1080",format:"webp",quality:"80",class:"rounded-lg object-cover"}),n("div",q,[n("h1",A,o(r.$t("why-action.title")),1),n("p",D,o(r.$t("why-action.sub-title")),1)])]),_:1}),e(y,{class:"grid grid-cols-3 max-sm:grid-cols-1 gap-6 py-12"},{default:c(()=>[(l(!0),i(_,null,B(m(p),(a,g)=>(l(),i("div",{key:g,class:"flex flex-col p-4 rounded-lg shadow gap-2 border"},[n("div",S,[n("span",F,o(a.title),1),e(w,{name:a.icon,size:"25px"},null,8,["name"])]),n("span",H,o(a.text),1)]))),128))]),_:1})]),_:1})],64)}}});export{W as default};
