import{Skeleton as z}from"./BaodNYLQ.js";import{_ as A}from"./BsHCAE3W.js";import{i as D,j as E,ak as H,l as u,d as f,a as d,p as $,w as _,q as g,$ as y,a0 as C,o as c,e as x,h as L,t as b,c as T,g as G}from"./C3gyeM1K.js";import{_ as J}from"./BispfOyS.js";import{u as K}from"./BCJZ1_ur.js";const M={key:0,class:"flex justify-between items-center w-full"},O={class:"text-sm text-gray-600"},P={key:0},U={key:0,class:"flex max-w-full flex-wrap gap-2 border-b border-gray-200 pb-4 mb-2 mx-4"},W=["onClick"],te=D({__name:"selected",props:{filter:{},loading:{type:Boolean}},emits:["clear"],setup(i,{emit:j}){const I=j,{priceFormat:V}=K(),k=E(),B=H(),e=u(()=>k.query),N=u(()=>{var a,r,s,l,o;return!(e!=null&&e.value)||!((a=e.value)!=null&&a.ram)||!((r=i.filter)!=null&&r.ram)?[]:(o=(l=(s=e==null?void 0:e.value)==null?void 0:s.ram)==null?void 0:l.split(","))==null?void 0:o.map(n=>({...i.filter.ram.options.find(t=>(t==null?void 0:t.value)==n),type:"ram"})).map(n=>{var t,p,m;return{...n,text:`${n.type}  (${n.text} ${(m=(p=(t=i.filter)==null?void 0:t.ram)==null?void 0:p.config)==null?void 0:m.suffix})`}})}),q=u(()=>{var a,r,s,l,o;return!(e!=null&&e.value)||!((a=e.value)!=null&&a.storage)||!((r=i.filter)!=null&&r.storage)?[]:(o=(l=(s=e==null?void 0:e.value)==null?void 0:s.storage)==null?void 0:l.split(","))==null?void 0:o.map(n=>({...i.filter.storage.options.find(t=>(t==null?void 0:t.value)==n),type:"storage"})).map(n=>{var t,p,m,h,w;return{...n,text:`${((p=(t=i.filter)==null?void 0:t.storage)==null?void 0:p.name)||n.type} (${n.text} ${(w=(h=(m=i.filter)==null?void 0:m.storage)==null?void 0:h.config)==null?void 0:w.suffix}) `}})}),S=u(()=>{var a,r,s,l,o;return!(e!=null&&e.value)||!((a=e.value)!=null&&a.brandId)||!((r=i.filter)!=null&&r.brandId)?[]:(o=(l=(s=e==null?void 0:e.value)==null?void 0:s.brandId)==null?void 0:l.split(","))==null?void 0:o.map(n=>({...i.filter.brandId.options.find(t=>(t==null?void 0:t.value)==n),type:"brand"}))}),F=u(()=>{var a,r,s;return!((a=e.value)!=null&&a.price)||!((r=i.filter)!=null&&r.price)?[]:[{text:((s=e==null?void 0:e.value)==null?void 0:s.price).split(":").map(l=>V(Number(l))).join(" - "),type:"price"}]}),v=u(()=>[...F.value,...N.value,...q.value,...S.value]),R=a=>{const r={...k.query},s=r[a.type];if(a.type=="price")delete r[a.type];else if(s){const l=s.split(",").filter(o=>o!==String(a.value)).join(",");l?r[a.type]=l:delete r[a.type]}B.push({path:k.path,query:r})};return(a,r)=>{const s=z,l=A,o=G;return c(),f(y,null,[d(l,{class:"flex flex-row w-full justify-between items-center py-4 ps-6 border-b mb-2"},{default:_(()=>{var n,t;return[a.loading?(c(),f("div",M,[d(s,{class:"h-8 w-1/3 border-gray-200"}),d(s,{class:"h-8 w-1/3 border-gray-200"})])):(c(),f(y,{key:1},[x("span",O,[L(b(a.$t("filters.selected-title"))+" ",1),(n=g(v))!=null&&n.length?(c(),f("span",P," ("+b((t=g(v))==null?void 0:t.length)+") ",1)):$("",!0)]),x("button",{class:"text-sm text-primary-600 hover:opacity-50",onClick:r[0]||(r[0]=p=>I("clear"))},b(a.$t("filters.clear-title")),1)],64))]}),_:1}),g(v).length?(c(),f("div",U,[a.loading?(c(!0),f(y,{key:0},C(Array(5),(n,t)=>(c(),T(s,{key:`loading-${t}`,class:"h-5 w-1/4 bg-sky-50"}))),128)):(c(!0),f(y,{key:1},C(g(v),(n,t)=>(c(),f("div",{key:t},[d(g(J),{variant:"selected"},{default:_(()=>[x("div",{class:"inline-flex gap-2 justify-center items-center text-nowrap cursor-pointer",onClick:p=>R(n)},[x("span",null,b((n==null?void 0:n.text)||n),1),d(o,{name:"lucide:circle-x",size:"12px"})],8,W)]),_:2},1024)]))),128))])):$("",!0)],64)}}});export{te as _};
