<script setup lang="ts">
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Brand, BrandList, Pagination } from '~/interfaces/brands/brand'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const page = computed<number>(() => Number(route.query?.page ?? 1))

/** bread crumb links **/
const breadCrumbLinks = computed(() => {
	return useBreadcrumbs().buildSinglePage(t('brands.title'))
})

const { data, error, status } = await useApi<BrandList>('/brands', {
	params: {
		perPage: 24,
		page,
	},
	watch: [page],
})

if (error.value) {
	console.log(`Error on fetching brands: ${error.value}`)
}

const brands = computed(() => (data.value as BrandList)?.items as Brand[])
const pagination = computed(() => (data.value as BrandList)?.pagination as Pagination)
const loading = computed(() => status.value !== 'success')
const pageNumber = computed(() => page.value > 1 ? `- ${t(`form.page-number`, { page: page.value })}` : '')

const onPageChanged = (page: number) => {
	const localePath = useLocalePath()
	router.push(localePath(`/brands-list?page=${page}`))
}

if (!!pagination.value.lastPage && pagination.value.lastPage < page.value) {
	throw createError({
		statusCode: 404,
	})
}

useSeoMeta({
	title: () => `${t('brands.title')} ${pageNumber.value}| ${t('header.meta-site-name')}`,
	description: () => `${t('brands.meta-description')} ${pageNumber.value}`,
	ogTitle: () => `${t('brands.title')} ${pageNumber.value}| ${t('header.meta-site-name')}`,
	ogDescription: () => `${t('brands.meta-description')} ${pageNumber.value}`,
	twitterTitle: () => `${t('brands.title')} ${pageNumber.value}| ${t('header.meta-site-name')}`,
	twitterDescription: () => `${t('brands.meta-description')} ${pageNumber.value}`,
})

const url = useRequestURL()
useHead({
	link: [
		{
			rel: 'canonical',
			href: url.href,
		},
		{
			rel: 'twitter:card',
			href: url.href,
		},
		{
			rel: 'og:url',
			href: url.href,
		},
	],
	script: [
		{
			type: 'application/ld+json',
			innerHTML: JSON.stringify({
				'@context': 'https://schema.org',
				'@type': 'LocalBusiness',
				'name': t('app.action-page-title'),
				'image': 'https://action.jo/images/logo.png',
				'@id': url.href,
				'url': url.href,
				'telephone': '962791009595',
				'priceRange': '10',
				'address': {
					'@type': 'PostalAddress',
					'streetAddress': '329 K. Abdullah I St.',
					'addressLocality': 'Amman',
					'postalCode': '11185',
					'addressCountry': 'JO',
				},
				'geo': {
					'@type': 'GeoCoordinates',
					'latitude': 31.9785307,
					'longitude': 35.9728592,
				},
				'openingHoursSpecification': {
					'@type': 'OpeningHoursSpecification',
					'dayOfWeek': [
						'Monday',
						'Tuesday',
						'Wednesday',
						'Thursday',
						'Friday',
						'Saturday',
						'Sunday',
					],
					'opens': '10:00',
					'closes': '23:00',
				},
				'sameAs': [
					'https://www.facebook.com/Actionmobile11',
					'https://www.instagram.com/actionwebsite/',
					url.href,
				],
			}),
			tagPosition: 'head', // Ensure it's in the <head>
		},
	],
	// @ts-ignore
	__dangerouslyDisableSanitizersByTagID: {
		// Disable sanitization for this script
		'ld-json-schema': ['innerHTML'],
	},
})
</script>

<template>
	<Breadcrumb
		:links="breadCrumbLinks"
		class="!border-0 !shadow-none"
	/>
	<Card class="flex flex-col w-full h-full gap-2 my-6">
		<CardHeader class="text-center justify-center gap-4 rounded-lg">
			<h1 class="font-bold text-2xl">
				{{ $t('brands.title') }}
			</h1>
			<p class="text-xl">
				{{ $t('brands.sub-title') }}
			</p>
		</CardHeader>
		<CardContent class="grid grid-cols-5 max-sm:grid-cols-2 gap-6 py-12">
			<template v-if="loading">
				<div
					v-for="(_, index) in Array(25)"
					:key="`loading-brand-${index}`"
					class="flex flex-col p-4 rounded-lg shadow gap-2 border"
				>
					<div class="flex flex-col gap-2 h-full items-center justify-between">
						<div class="flex flex-grow justify-center items-center">
							<Skeleton class="h-16 w-28" />
						</div>
						<Skeleton class="h-4 w-28" />
					</div>
				</div>
			</template>
			<template v-else>
				<NuxtLinkLocale
					v-for="(item, index) in brands"
					:key="index"
					:to="`/brands/${item.slug}`"
					class="flex flex-col p-4 rounded-lg shadow gap-2 border"
				>
					<div class="flex flex-col gap-2 h-full items-center justify-between">
						<div class="flex flex-grow justify-center items-center">
							<NuxtImg
								:src="item?.media?.logoName?.preview"
								:alt="`${item.name} brand logo`"
								object-fit="cover"
								object-position="center"
								height="56"
								width="100"
								format="webp"
								loading="eager"
								provider="backend"
							/>
						</div>
						<h2 class="text-base font-semibold">
							<div class="flex flex-grow justify-center items-center">
								{{ item.name }}
							</div>
						</h2>
					</div>
				</NuxtLinkLocale>
			</template>
		</CardContent>
		<CardFooter v-if="!!brands?.length">
			<div
				class="flex justify-center items-center w-full"
			>
				<Paginate
					:items-per-page="pagination?.perPage"
					:total="pagination?.lastPage"
					:sibling-count="1"
					:show-edges="true"
					:default-page="pagination.page"
					@update:page="onPageChanged"
				/>
			</div>
		</CardFooter>
	</Card>
</template>
