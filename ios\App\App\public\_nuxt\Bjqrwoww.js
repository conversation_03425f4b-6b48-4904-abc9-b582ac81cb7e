const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as A,ak as B,aj as I,ai as j,u as N,z as $,l,m as E,L as V,d as D,a as e,e as p,w as o,q as t,W as s,o as M,ag as R,g as T,t as h,ah as q}from"./C3gyeM1K.js";import F from"./CuUhnQhf.js";import{_ as H}from"./BQWT7oJn.js";import{_ as O}from"./BispfOyS.js";import{_ as n}from"./BoqtTPyX.js";import{u as U}from"./BrsQk_-I.js";const W=R(()=>q(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(m=>m.default||m)),G={class:"max-sm:hidden container flex items-center w-full justify-between h-20 my-2 py-2 gap-x-4 will-change-transform"},J={class:"grid grid-cols-5 gap-4"},K={class:"text-2xs font-semibold text-white leading-none"},Q={class:"text-2xs font-semibold text-white leading-none"},X={class:"text-2xs font-semibold text-white leading-none"},ae=A({__name:"actions",props:{hasScroll:{type:Boolean}},emits:["open:drawer"],setup(m,{emit:y}){const g=y,v=B(),b=I(),w=j(),C=N(),u=U(),S=$(),d=l(()=>b.count||0),_=l(()=>w.count||0),i=l(()=>u.count||0),L=l(()=>!!C.isLoggedIn),z=()=>{if(i.value<=1)return!1;const r=u.products.join("/");v.push(S(`/compare/${r}`))};return E(()=>{V(()=>{u.fetchProducts()})}),(r,c)=>{const k=W,x=F,P=H,a=T,f=O;return M(),D("div",G,[e(x,{to:"/"},{default:o(()=>[e(k,{class:"max-h-16 app-logo mx-auto",alt:"Action Mobile",src:"/images/logo.png",width:r.hasScroll?70:100,height:r.hasScroll?35:53,format:"webp",loading:"eager"},null,8,["width","height"])]),_:1}),e(P),p("div",J,[e(t(n),{variant:"icon"},{default:o(()=>[e(a,{name:"lucide:bell",size:"20px"})]),_:1}),e(t(n),{variant:"icon",class:s(["relative",{"bg-primary-300 !text-primary-600":!!t(i)}]),onClick:z},{default:o(()=>[e(a,{name:"ui:compare",size:"20px"}),e(f,{class:s(["absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none",{hidden:!t(i)}])},{default:o(()=>[p("span",K,h(t(i)),1)]),_:1},8,["class"])]),_:1},8,["class"]),e(t(n),{variant:"icon","as-child":!0,class:s({"bg-primary-300 !text-primary-600":t(L)})},{default:o(()=>[e(x,{to:"/my/profile",class:"flex justify-center items-center"},{default:o(()=>[e(a,{name:"lucide:circle-user-round",size:"20px"})]),_:1})]),_:1},8,["class"]),e(t(n),{variant:"icon",class:s(["relative",{"bg-primary-300 !text-primary-600":!!t(d)}]),onClick:c[0]||(c[0]=()=>g("open:drawer","wishlist"))},{default:o(()=>[e(a,{name:"lucide:heart",size:"20px"}),e(f,{class:s(["absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none",{hidden:!t(d)}])},{default:o(()=>[p("span",Q,h(t(d)),1)]),_:1},8,["class"])]),_:1},8,["class"]),e(t(n),{variant:"icon",class:s(["relative",{"bg-primary-300 !text-primary-600":t(_)}]),onClick:c[1]||(c[1]=()=>g("open:drawer","cart"))},{default:o(()=>[e(a,{name:"lucide:shopping-cart",size:"20px"}),e(f,{class:s(["absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none",{hidden:!t(_)}])},{default:o(()=>[p("span",X,h(t(_)),1)]),_:1},8,["class"])]),_:1},8,["class"])])])}}});export{ae as _};
