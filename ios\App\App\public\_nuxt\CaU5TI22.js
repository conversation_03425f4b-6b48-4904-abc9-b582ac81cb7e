import{c as k}from"./jAU0Cazi.js";import{i as b,T as V,l as h,c as m,o as p,w as f,s as v,p as B,q as e,a1 as w,aK as _,Z as q,k as $,a as S,W as R}from"./C3gyeM1K.js";import{b as P}from"./D3IGe8e3.js";import{a as F}from"./BBuxlZld.js";import{c as K,u as y}from"./C2RgRyPS.js";import{u as E}from"./C1DP8TeJ.js";import{P as g}from"./ClItKS3j.js";import{_ as T}from"./BYbD1TwY.js";const[M,N]=K("SwitchRoot"),j=b({__name:"SwitchRoot",props:{defaultValue:{type:Boolean},modelValue:{type:[Boolean,null],default:void 0},disabled:{type:Boolean},id:{},value:{default:"on"},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:modelValue"],setup(i,{emit:l}){const t=i,n=l,{disabled:o}=V(t),s=F(t,"modelValue",n,{defaultValue:t.defaultValue,passive:t.modelValue===void 0});function r(){o.value||(s.value=!s.value)}const{forwardRef:u,currentElement:d}=y(),c=E(d),C=h(()=>{var a;return t.id&&d.value?(a=document.querySelector(`[for="${t.id}"]`))==null?void 0:a.innerText:void 0});return N({modelValue:s,toggleCheck:r,disabled:o}),(a,I)=>(p(),m(e(g),w(a.$attrs,{id:a.id,ref:e(u),role:"switch",type:a.as==="button"?"button":void 0,value:a.value,"aria-label":a.$attrs["aria-label"]||C.value,"aria-checked":e(s),"aria-required":a.required,"data-state":e(s)?"checked":"unchecked","data-disabled":e(o)?"":void 0,"as-child":a.asChild,as:a.as,disabled:e(o),onClick:r,onKeydown:_(q(r,["prevent"]),["enter"])}),{default:f(()=>[v(a.$slots,"default",{modelValue:e(s)}),e(c)&&a.name?(p(),m(e(T),{key:0,type:"checkbox",name:a.name,disabled:e(o),required:a.required,value:a.value,checked:!!e(s)},null,8,["name","disabled","required","value","checked"])):B("",!0)]),_:3},16,["id","type","value","aria-label","aria-checked","aria-required","data-state","data-disabled","as-child","as","disabled","onKeydown"]))}}),z=b({__name:"SwitchThumb",props:{asChild:{type:Boolean},as:{default:"span"}},setup(i){const l=M();return y(),(t,n)=>{var o;return p(),m(e(g),{"data-state":(o=e(l).modelValue)!=null&&o.value?"checked":"unchecked","data-disabled":e(l).disabled.value?"":void 0,"as-child":t.asChild,as:t.as},{default:f(()=>[v(t.$slots,"default")]),_:3},8,["data-state","data-disabled","as-child","as"])}}}),O=b({__name:"Switch",props:{defaultValue:{type:Boolean},modelValue:{type:[Boolean,null]},disabled:{type:Boolean},id:{},value:{},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},emits:["update:modelValue"],setup(i,{emit:l}){const t=i,n=l,o=h(()=>{const{class:d,...c}=t;return c}),s=P(o,n),{locale:r}=$(),u=h(()=>r.value==="ar");return(d,c)=>(p(),m(e(j),w(e(s),{direction:u.value?"rtl":"ltr",class:e(k)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-600 data-[state=unchecked]:bg-gray-300 shadow",t.class)}),{default:f(()=>[S(e(z),{class:R(e(k)(`pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg transition-transform ${u.value?"end-0 data-[state=checked]:-translate-x-5":"right-0 data-[state=checked]:translate-x-5"}`))},{default:f(()=>[v(d.$slots,"thumb")]),_:3},8,["class"])]),_:3},16,["direction","class"]))}});export{O as _};
