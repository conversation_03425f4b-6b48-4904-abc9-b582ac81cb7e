const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as P,u as S,l as i,j as F,r as O,k as h,n as R,a2 as V,ab as q,c as g,w as d,o as p,a as c,e as a,q as l,d as x,ag as T,t as u,g as H,$ as K,a0 as M,W as b,Z as U,ah as W,_ as Z}from"./C3gyeM1K.js";import{_ as G}from"./DAA1LH-g.js";import{_ as J}from"./JNWsvxxg.js";import{_ as Q}from"./BsHCAE3W.js";import X from"./CuUhnQhf.js";import{g as Y}from"./BBuxlZld.js";import"./jAU0Cazi.js";import"./CRC5xmnh.js";const ee=T(()=>W(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(s=>s.default||s)),te={class:"flex flex-col gap-1 max-h-96 h-full justify-center items-center"},ae={class:"relative flex flex-col"},oe={key:1,class:"text-xl font-bold"},se={class:"absolute bottom-2 justify-center items-center z-10 bg-white flex rounded-full w-4 h-4 border-primary-600 border overflow-hidden"},ne={class:"text-lg font-bold truncate-1-line leading-tight"},re={class:"text-base font-normal truncate-1-line leading-tight"},le={dir:"ltr",class:"text-base font-normal truncate-1-line leading-tight"},ie={class:"flex flex-col"},ce={class:"flex gap-4 items-center"},ue={class:"text-base font-semibold"},de={class:"flex gap-4 items-center"},pe={class:"text-base font-semibold text-gray-500"},me=P({__name:"navigation",props:{user:{}},setup(s){const m=S(),w=i(()=>{var e,t;return((e=s.user)==null?void 0:e.firstName)+" "+((t=s.user)==null?void 0:t.lastName)}),v=i(()=>{var e,t,o;return(((t=(e=s.user)==null?void 0:e.firstName)==null?void 0:t.charAt(0))+"."+((o=s.user.lastName)==null?void 0:o.charAt(0))).toUpperCase()}),y=i(()=>{var e,t,o,n;return`+${(t=(e=s.user)==null?void 0:e.phone)==null?void 0:t.code}${(n=(o=s.user)==null?void 0:o.phone)==null?void 0:n.number}`}),f=i(()=>{var e,t,o;return(o=(t=(e=s.user)==null?void 0:e.media)==null?void 0:t.avatar)==null?void 0:o.preview}),_=F(),k=O("profile"),{t:C}=h(),{open:N,onChange:$}=Y({accept:"image/*"}),j=[{title:"profile.link-profile-title",icon:"ui:profile",page:"profile"},{title:"profile.link-wish-title",icon:"ui:heart",page:"wishlist"},{title:"profile.link-address-title",icon:"ui:address",page:"address"},{title:"profile.link-reviews-title",icon:"ui:reviews",page:"reviews"},{title:"profile.link-orders-title",icon:"ui:orders",page:"orders"},{title:"profile.link-wallet-title",icon:"ui:wallets",page:"wallet"}];R(()=>_.query,e=>{k.value=e.page||"profile"},{immediate:!0});const A=i(()=>h().locale.value==="ar"),z=async()=>{await m.logout()},D=i(()=>{var e;return(e=_.meta)==null?void 0:e.parent});return $(async e=>{if(!(e!=null&&e[0]))return;const t=new FormData;t.append("file",e==null?void 0:e[0]);const{$api:o}=V();return o("/upload-media",{method:"POST",body:t}).then(async n=>{await m.uploadAvatar(n),q.success(C("form.profile-image-updated"))}).catch(n=>{console.log("Error on updating user image",n)})}),(e,t)=>{const o=ee,n=H,I=Q,L=X,B=J,E=G;return p(),g(E,null,{default:d(()=>[c(I,{class:"profile-head"},{default:d(()=>{var r;return[a("div",te,[a("div",ae,[a("div",{role:"button",class:"flex flex-col rounded-full w-20 h-20 bg-white border-2 border-primary-600 justify-center items-center",onClick:t[0]||(t[0]=()=>l(N)())},[l(f)?(p(),g(o,{key:0,src:l(f),class:"rounded-full w-full h-full object-cover"},null,8,["src"])):(p(),x("span",oe,u(l(v)),1))]),a("div",se,[c(n,{name:"lucide:pencil",size:"8px",class:"text-primary-600"})])]),a("span",ne,u(l(w)),1),a("span",re,u((r=e.user)==null?void 0:r.email),1),a("span",le,u(l(y)),1)])]}),_:1}),c(B,{class:"p-0"},{default:d(()=>[a("div",ie,[(p(),x(K,null,M(j,r=>c(L,{key:r.title,class:b(["flex w-full justify-between items-center gap-4 border-b border-gray-200 p-4",{"bg-primary-600 text-white":l(D)===r.page}]),to:`/my/${r.page}`,"active-class":"bg-primary-600 text-white"},{default:d(()=>[a("div",ce,[c(n,{name:r.icon,class:"h-6 w-7"},null,8,["name"]),a("span",ue,u(e.$t(r.title)),1)]),c(n,{name:"lucide:chevron-right",class:b({"rotate-180":l(A)})},null,8,["class"])]),_:2},1032,["to","class"])),64)),a("div",{role:"button",class:"flex w-full justify-between items-center gap-4 border-b border-gray-200 p-4",onClickOnce:U(z,["prevent"])},[a("div",de,[a("span",pe,u(e.$t("profile.sign-out-title")),1)])],32)])]),_:1})]),_:1})}}}),ye=Z(me,[["__scopeId","data-v-bdce6e18"]]);export{ye as default};
