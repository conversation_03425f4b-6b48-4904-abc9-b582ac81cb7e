<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { onMounted } from 'vue'
import { Skeleton } from '~/components/ui/skeleton'
import type { LockupCategories } from '~/interfaces/lockup/categories'

const expandedList = ref<LockupCategories[]>([])
const { t, locale } = useI18n()
const { data, error, status } = await useApi<LockupCategories[]>('lookups-website/categories', {
	watch: [locale],
})

const response = computed<LockupCategories[] | []>(() => data.value as LockupCategories[] ?? [])
const loading = computed(() => status.value !== 'success')
const { width: screenWidth } = useWindowSize()

let BREAK_POINT: number = 0
if (error.value) {
	console.error('API Error: fetching lockup category', error.value)
}

/** init menu list **/
const initList = (): void => {
	if (screenWidth.value >= 1200) {
		BREAK_POINT = 7
	} else if (screenWidth.value < 1200 && screenWidth.value >= 600) {
		BREAK_POINT = 3
	} else if (screenWidth.value < 600) {
		BREAK_POINT = 1
	}
	if (BREAK_POINT) {
		expandedList.value = response.value?.slice(0, BREAK_POINT) || []
		expandedList.value.push({
			text: t('header.other-sections'),
			children: response.value?.slice(BREAK_POINT, response.value.length),
			value: 'OTHER_SECTION',
		})
	}
}

const dir = computed(() => locale.value === 'ar' ? 'rtl' : 'ltr')
const localePath = useLocalePath()
onMounted(() => {
	nextTick(() => {
		initList()
	})

	watch([() => screenWidth, () => data], () => {
		initList()
	}, { deep: true })
})
</script>

<template>
	<div
		v-if="BREAK_POINT"
		class="container w-full flex gap-4 text-gray-500 flex-wrap will-change-auto transition-all max-md:!px-2"
	>
		<template v-if="loading">
			<Skeleton
				v-for="(_, index) in Array(BREAK_POINT)"
				:key="`home-link-${index}`"
				as="div"
				class="w-32 h-10"
			/>
		</template>
		<template v-else>
			<NavigationMenu
				v-for="(item, index) in expandedList"
				:key="`${index}-${JSON.stringify(item.text)}`"
				:disable-hover-trigger="false"
				:dir="dir"
				:href="item?.meta?localePath(`/category/${item.meta?.slug}`):'#'"
			>
				<AppHeaderLinksList :item="item" />
			</NavigationMenu>
		</template>
	</div>
</template>
