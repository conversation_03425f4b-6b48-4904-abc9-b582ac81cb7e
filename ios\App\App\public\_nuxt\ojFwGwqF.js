import{c as F}from"./jAU0Cazi.js";import{b as O,a as z}from"./D3IGe8e3.js";import{i as w,T as G,l as d,r as L,n as N,c as B,o as C,w as $,s as M,a as U,q as o,a1 as k,m as H,ar as J,aK as q,L as Q}from"./C3gyeM1K.js";import{P as K}from"./ClItKS3j.js";import{a as W}from"./BBuxlZld.js";import{_ as X}from"./BYbD1TwY.js";import{c as Y,u as Z}from"./C2RgRyPS.js";import{u as ee}from"./BRIKaOVL.js";import{u as te}from"./C0-TGi1L.js";import{u as ae}from"./B5rxJs06.js";import{g as le}from"./FUKBWQwh.js";const[oe,ne]=Y("PinInputRoot"),se=w({inheritAttrs:!1,__name:"PinInputRoot",props:{modelValue:{},defaultValue:{},placeholder:{default:""},mask:{type:Boolean},otp:{type:Boolean},type:{default:"text"},dir:{},disabled:{type:Boolean},id:{},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean}},emits:["update:modelValue","complete"],setup(c,{emit:l}){const t=c,n=l,{mask:r,otp:u,placeholder:h,type:p,disabled:b,dir:D}=G(t),{forwardRef:_}=Z(),E=ee(D),m=W(t,"modelValue",n,{defaultValue:t.defaultValue??[],passive:t.modelValue===void 0}),x=d(()=>Array.isArray(m.value)?[...m.value]:[]),g=L(new Set);function A(i){g.value.add(i)}const V=d(()=>x.value.filter(y=>!!y).length===g.value.size);return N(m,()=>{V.value&&n("complete",m.value)},{deep:!0}),ne({modelValue:m,currentModelValue:x,mask:r,otp:u,placeholder:h,type:p,dir:E,disabled:b,isCompleted:V,inputElements:g,onInputElementChange:A}),(i,y)=>(C(),B(o(K),k(i.$attrs,{ref:o(_),dir:o(E),"data-complete":V.value?"":void 0,"data-disabled":o(b)?"":void 0}),{default:$(()=>[M(i.$slots,"default",{modelValue:o(m)}),U(X,{id:i.id,as:"input",feature:"focusable",tabindex:"-1",value:x.value.join(""),name:i.name??"",disabled:o(b),required:i.required,onFocus:y[0]||(y[0]=R=>{var I,v;return(v=(I=Array.from(g.value))==null?void 0:I[0])==null?void 0:v.focus()})},null,8,["id","value","name","disabled","required"])]),_:3},16,["dir","data-complete","data-disabled"]))}}),re=w({__name:"PinInputInput",props:{index:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"input"}},setup(c){const l=c,t=oe(),n=d(()=>Array.from(t.inputElements.value)),r=d(()=>t.currentModelValue.value[l.index]),u=d(()=>l.disabled||t.disabled.value),h=d(()=>t.otp.value),p=d(()=>t.type.value==="number"),b=d(()=>t.mask.value),{primitiveElement:D,currentElement:_}=te();function E(e){var f;const a=e.target;if((((f=e.data)==null?void 0:f.length)??0)>1){R(a.value);return}if(p.value&&!/^\d*$/.test(a.value)){a.value=a.value.replace(/\D/g,"");return}a.value=a.value.slice(-1),v(l.index,a.value);const s=n.value[l.index+1];s&&s.focus()}function m(){const e=_.value;Q(()=>{e&&!e.value&&(e.placeholder=t.placeholder.value)})}function x(e){ae(e,le(),void 0,{itemsArray:n.value,focus:!0,loop:!1,arrowKeyOptions:"horizontal",dir:t.dir.value})}function g(e){if(e.preventDefault(),e.target.value)v(l.index,"");else{const f=n.value[l.index-1];f&&(f.focus(),v(l.index-1,""))}}function A(e){e.key==="Delete"&&(e.preventDefault(),v(l.index,""))}function V(e){const a=e.target;a.setSelectionRange(1,1),a.value||(a.placeholder="")}function i(e){m()}function y(e){e.preventDefault();const a=e.clipboardData;if(!a)return;const s=a.getData("text");R(s)}function R(e){var S;const a=[...t.currentModelValue.value],s=e.length>=n.value.length?0:l.index,f=Math.min(s+e.length,n.value.length);for(let P=s;P<f;P++){const T=n.value[P],j=e[P-s];p.value&&!/^\d*$/.test(j)||(a[P]=j,T.focus())}t.modelValue.value=a,(S=n.value[f])==null||S.focus()}function I(e){let a=e.length-1;for(;a>=0&&e[a]==="";)e.pop(),a--;return e}function v(e,a){const s=[...t.currentModelValue.value];s[e]=a,t.modelValue.value=I(s)}return N(r,()=>{r.value||m()}),H(()=>{t.onInputElementChange(_.value)}),J(()=>{var e;(e=t.inputElements)==null||e.value.delete(_.value)}),(e,a)=>(C(),B(o(K),{ref_key:"primitiveElement",ref:D,autocapitalize:"none",as:e.as,"as-child":e.asChild,autocomplete:h.value?"one-time-code":"false",type:b.value?"password":"text",inputmode:p.value?"numeric":"text",pattern:p.value?"[0-9]*":void 0,placeholder:o(t).placeholder.value,value:r.value,disabled:u.value,"data-disabled":u.value?"":void 0,"data-complete":o(t).isCompleted.value?"":void 0,"aria-label":`pin input ${e.index+1} of ${n.value.length}`,onInput:a[0]||(a[0]=s=>E(s)),onKeydown:[q(x,["left","right","up","down","home","end"]),q(g,["backspace"]),q(A,["delete"])],onFocus:V,onBlur:i,onPaste:y},{default:$(()=>[M(e.$slots,"default")]),_:3},8,["as","as-child","autocomplete","type","inputmode","pattern","placeholder","value","disabled","data-disabled","data-complete","aria-label"]))}}),be=w({__name:"PinInput",props:{modelValue:{default:()=>[]},defaultValue:{},placeholder:{},mask:{type:Boolean},otp:{type:Boolean},type:{},dir:{},disabled:{type:Boolean},id:{},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean},class:{}},emits:["update:modelValue","complete"],setup(c,{emit:l}){const t=c,n=l,r=d(()=>{const{class:h,...p}=t;return p}),u=O(r,n);return(h,p)=>(C(),B(o(se),k(o(u),{class:o(F)("flex gap-2 items-center",t.class)}),{default:$(()=>[M(h.$slots,"default")]),_:3},16,["class"]))}}),_e=w({__name:"PinInputGroup",props:{asChild:{type:Boolean},as:{},class:{}},setup(c){const l=c,t=d(()=>{const{class:r,...u}=l;return u}),n=z(t);return(r,u)=>(C(),B(o(K),k(o(n),{class:o(F)("flex items-center",l.class)}),{default:$(()=>[M(r.$slots,"default")]),_:3},16,["class"]))}}),xe=w({__name:"PinInputInput",props:{index:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(c){const l=c,t=d(()=>{const{class:r,...u}=l;return u}),n=z(t);return(r,u)=>(C(),B(o(re),k(o(n),{class:o(F)("relative text-center flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md outline-none",l.class)}),null,16,["class"]))}});export{_e as _,xe as a,be as b};
