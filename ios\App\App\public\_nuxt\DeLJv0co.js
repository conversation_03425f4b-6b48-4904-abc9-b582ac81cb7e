import{u as J,_ as Q}from"./BD9sJOps.js";import{n as U,aL as W,i as X,j as Z,ak as oo,l as y,Y as to,r as ro,d as ao,a as n,c as j,p as no,w as m,q as t,$ as eo,o as $,e as p,t as A,L as so,a2 as io,am as mo,ab as co}from"./C3gyeM1K.js";import{_ as lo}from"./XQ2tvv6p.js";import uo from"./CZdelrXR.js";import{_ as po}from"./EyNo9aGE.js";import{_ as _o}from"./DFS1-ioY.js";import{_ as fo}from"./DAA1LH-g.js";import{_ as go}from"./JNWsvxxg.js";import{_ as vo}from"./CO3DBU_1.js";import{_ as yo}from"./DBTwKVcZ.js";import{_ as wo}from"./BoqtTPyX.js";import Po from"./CuUhnQhf.js";import{_ as $o}from"./lU3sMj3q.js";import{_ as Co}from"./C0H2RQFE.js";import{u as xo}from"./BrsQk_-I.js";import{u as ho}from"./BGs9QzNZ.js";import{u as bo}from"./BQBaqVsz.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";import"./Dr0cUkmi.js";import"./CiqmSqHP.js";import"./CRC5xmnh.js";import"./D3J2c5MI.js";import"./CCzc7fMa.js";import"./BBuxlZld.js";import"./BxRYGYnW.js";import"./BbzLkj_4.js";import"./L0mwwrGq.js";import"./idjCP6w_.js";import"./Cn3HuTsa.js";import"./C4nYFGHw.js";import"./BCJZ1_ur.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";import"./BsHCAE3W.js";import"./CdAhpRi0.js";import"./C5xuNBUQ.js";import"./KJBS0BDy.js";import"./KT5gHpzo.js";import"./CK-5carg.js";import"./CvhKjyCU.js";function ko(C=150){const e=W(),d=s=>{const i=document.getElementById(s);i&&window.scrollTo({top:(i==null?void 0:i.offsetTop)-C,behavior:"smooth"})};return U(()=>e,s=>{if(s!=null&&s.hash)return setTimeout(()=>{d(s.hash.replace("#",""))},100)},{immediate:!0,deep:!0}),{scrollToElement:d}}const So={class:"flex gap-4 py-2 max-sm:flex-col"},To={class:"flex gap-4 max-md:flex-col w-full max-md:w-1/2 max-sm:w-full"},No={class:"flex w-full justify-end items-center gap-4"},Bo={class:"flex w-full px-4 justify-center items-center mb-4"},Bt=X({__name:"[...slug]",setup(C){var k,S,T,N,B,D,L;const e=Z(),d=oo(),s=y(()=>{var o;return((o=e.params)==null?void 0:o.slug).join("/")}),{data:i,error:x,status:E}=to(`products/${s.value}`),r=y(()=>ho(i==null?void 0:i.value)),c=y(()=>E.value!=="success"),l=ro(null);x.value&&console.error("Error: in fetching product details",x.value);const V=y(()=>{var o;return J().buildSinglePage((o=r.value)==null?void 0:o.name)}),{setSeoData:M}=bo({pageName:"product",fullPath:e==null?void 0:e.fullPath,metaDescription:(k=r.value)==null?void 0:k.metaDescription,metaTitle:(S=r.value)==null?void 0:S.metaTitle,media:(L=(D=(B=(N=(T=r.value)==null?void 0:T.variance)==null?void 0:N.media)==null?void 0:B.gallery)==null?void 0:D[0])==null?void 0:L.preview});ko(),M();const R=async({varianceSlug:o})=>{const a=Array.isArray(e.params.slug)?e.params.slug[0]:e.params.slug;return so(()=>{d.push({name:e.name,params:{...e.params,slug:[a,o]}})})},h=async o=>{var _,f,g;const{$api:a}=io();return a("/orders/buy-now",{method:"POST",body:{quantity:o,productId:(_=r.value)==null?void 0:_.productId,varianceId:(g=(f=r.value)==null?void 0:f.variance)==null?void 0:g.varianceId}}).then(async()=>{await mo("/cart")}).catch(u=>{console.error(u),co.error((u==null?void 0:u.message)||"Something went wrong")})},w=xo(),b=async o=>w.products.includes(o)?w.removeProduct(o):w.setProduct(o);return(o,a)=>{const _=Q,f=lo,g=uo,u=po,O=_o,q=go,F=fo,G=vo,K=yo,I=wo,Y=Po,z=$o,H=Co;return $(),ao(eo,null,[n(F,{class:"-mt-2 pt-2"},{default:m(()=>{var v;return[n(_,{links:t(V),loading:t(c),class:"!border-0 !shadow-none"},null,8,["links","loading"]),($(),j(q,{key:(v=t(r))==null?void 0:v.productId},{default:m(()=>[p("div",So,[n(f,{product:t(r),loading:t(c)},null,8,["product","loading"]),p("div",To,[n(g,{product:t(r),loading:t(c),"onSelect:variant":R},null,8,["product","loading"]),n(u,{product:t(r),loading:t(c),"onConfirm:addToCart":a[0]||(a[0]=P=>l.value=P),"onProduct:payNow":h,"onProduct:compare":b},null,8,["product","loading"]),n(O,{product:t(r),loading:t(c),"onConfirm:addToCart":a[1]||(a[1]=P=>l.value=P),"onProduct:payNow":h,"onProduct:compare":b},null,8,["product","loading"])])])]),_:1}))]}),_:1}),n(G),n(K,{loading:t(c),product:t(r)},null,8,["loading","product"]),t(l)?($(),j(H,{key:0,title:o.$t("cart-list.add-item-title"),onClose:a[3]||(a[3]=v=>l.value=null)},{footer:m(()=>[p("div",No,[n(I,{variant:"outline",onClickOnce:a[2]||(a[2]=v=>l.value=null)},{default:m(()=>[p("span",null,A(o.$t("cart-list.confirm-continue")),1)]),_:1}),n(I,{"as-child":""},{default:m(()=>[n(Y,{to:"/cart"},{default:m(()=>[p("span",null,A(o.$t("cart-list.confirm-continue-pay")),1)]),_:1})]),_:1})])]),body:m(()=>[p("div",Bo,[n(z,{product:t(l),"view-only":!0},null,8,["product"])])]),_:1},8,["title"])):no("",!0)],64)}}});export{Bt as default};
