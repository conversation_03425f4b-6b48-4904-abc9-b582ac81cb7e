import{ap as e}from"./C3gyeM1K.js";import{c as s}from"./BBuxlZld.js";const c=e("compare",{state:()=>({products:s("CompareProducts",[])}),actions:{setProduct(t){return this.isDisableCompare||this.products.includes(t)?!1:(this.products.push(t),!0)},removeProduct(t){this.products=this.products.filter(r=>r!==t)},hasVariance(t){return this.products.indexOf(t)!==-1},clearAll(){this.products=[]},fetchProducts(){this.products=s("CompareProducts",[])}},getters:{isDisableCompare:t=>{var r;return((r=t.products)==null?void 0:r.length)>=3},isEmpty:t=>{var r;return!((r=t.products)!=null&&r.length)},count:t=>{var r;return(r=t.products)==null?void 0:r.length}}});export{c as u};
