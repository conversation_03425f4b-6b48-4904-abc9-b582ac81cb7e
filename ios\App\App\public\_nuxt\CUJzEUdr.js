import{av as D,r as v,l as O,m as ee,n as y,R as ae,d as re,o as te,a4 as oe,s as h,p as _,az as ie,e as W,v as ne,x as se,q as F,a as le,a1 as ce,aA as $,ay as w,ac as G}from"./C3gyeM1K.js";import{u as pe,o as ue,a as P,s as M,d as ge,l as A,e as de,c as me}from"./C0giEChS.js";import{u as fe}from"./Bzz9boR6.js";import ve from"./DiFCG9It.js";import"./BsYnu6hU.js";import"./CvhKjyCU.js";ue({apiKey:M(),libraries:P(de(M())),language:P(M()),region:P(M()),v:P(ge([A("weekly"),A("quarterly"),A("beta"),A("alpha"),M()]))});function ye(j){let b=Promise.resolve();return pe("googleMaps",n=>{const o=(n==null?void 0:n.libraries)||["places"],k=n!=null&&n.language?{language:n.language}:void 0,L=n!=null&&n.region?{region:n.region}:void 0,p=n!=null&&n.v?{v:n.v}:void 0;return{scriptInput:{src:D("https://maps.googleapis.com/maps/api/js",{libraries:o.join(","),key:n==null?void 0:n.apiKey,loading:"async",callback:"google.maps.__ib__",...k,...L,...p})},clientInit:()=>{window.google=window.google||{},window.google.maps=window.google.maps||{},b=new Promise(S=>{window.google.maps.__ib__=S})},schema:void 0,scriptOptions:{use(){return{maps:b.then(()=>window.google.maps)}}}}},j)}const Oe={__name:"ScriptGoogleMaps",props:{trigger:{type:[String,Array,Boolean],required:!1,default:["mouseenter","mouseover","mousedown"]},aboveTheFold:{type:Boolean,required:!1},apiKey:{type:String,required:!1},center:{type:null,required:!1},centerMarker:{type:Boolean,required:!1},mapOptions:{type:null,required:!1},region:{type:String,required:!1},language:{type:String,required:!1},version:{type:String,required:!1},width:{type:[Number,String],required:!1,default:640},height:{type:[Number,String],required:!1,default:400},placeholderOptions:{type:Object,required:!1},placeholderAttrs:{type:Object,required:!1},rootAttrs:{type:Object,required:!1},markers:{type:Array,required:!1}},emits:["ready","error"],setup(j,{expose:b,emit:n}){var H;const o=j,k=n,L=o.apiKey||((H=me("googleMaps"))==null?void 0:H.apiKey),p=v(),S=v(),q=v(),B=v(),E=fe({trigger:o.trigger,el:S}),{load:U,status:d,onLoaded:J}=ye({apiKey:o.apiKey,scriptOptions:{trigger:E},region:o.region,language:o.language,v:o.version}),u=O(()=>{var e;return w({center:B.value},o.mapOptions,{center:o.center,zoom:15,mapId:(e=o.mapOptions)!=null&&e.styles?void 0:"map"})}),m=v(!1),s=v(),c=v(new Map);function z(e){return typeof e=="string"&&(e.split(",").length>2||e.includes("+"))}function K(e){return new Promise(async a=>{const r=e instanceof Promise?await e:e;r&&r.setMap(null),a()})}function x(e){const a=typeof e=="string"?{position:{lat:Number.parseFloat(e.split(",")[0]||"0"),lng:Number.parseFloat(e.split(",")[1]||"0")}}:e;return a.position||(a.position={lat:0,lng:0}),a}async function T(e){if(!e)return;const a=x(e),r=$({position:a.position});if(c.value.has(r))return c.value.get(r);const t=new Promise(async i=>{const l=await I("marker"),g=w(G(a),{map:G(s.value),position:a.location});i(new l.AdvancedMarkerElement(g))});return c.value.set(r,t),t}const N=new Map;async function C(e){return e&&typeof e=="object"?Promise.resolve(e):N.has(e)?Promise.resolve(N.get(e)):new Promise(async(a,r)=>{p.value||(await U(),await new Promise(i=>{const l=y(p,()=>{l(),i()})})),new p.value.places.PlacesService(s.value).findPlaceFromQuery({query:e,fields:["name","geometry"]},(i,l)=>{var g,f;return l==="OK"&&((f=(g=i==null?void 0:i[0])==null?void 0:g.geometry)!=null&&f.location)?a(i[0].geometry.location):r(new Error(`No location found for ${e}`))})}).then(a=>(N.set(e,a),a))}const R=new Map;function I(e){var r;if(R.has(e))return R.get(e);const a=((r=p.value)==null?void 0:r.importLibrary(e))||new Promise(t=>{const i=y(p,l=>{if(l){const g=l.importLibrary(e);t(g),i()}},{immediate:!0})});return R.set(e,a),a}const Q={googleMaps:p,map:s,createAdvancedMapMarker:T,resolveQueryToLatLang:C,importLibrary:I};b(Q),ee(()=>{y(m,e=>{e&&k("ready",Q)}),y(d,e=>{e==="error"&&k("error")}),y(u,()=>{var e;(e=s.value)==null||e.setOptions(u.value)}),y([()=>o.markers,s],async()=>{if(!s.value)return;const e=new Map((o.markers||[]).map(i=>[$({position:x(i).position}),i])),a=new Set([...c.value.keys()].filter(i=>!e.has(i))),r=new Set([...e.keys()].filter(i=>!c.value.has(i))),t=$({position:u.value.center});for(const i of a){if(i===t)continue;const l=await c.value.get(i);l&&K(l).then(()=>{c.value.delete(i)})}for(const i of r)T(e.get(i))},{immediate:!0,deep:!0}),y([()=>u.value.center,m,s],async(e,a)=>{if(!s.value)return;let r=G(e[0]);if(r&&(z(r)&&m.value&&(r=await C(r)),s.value.setCenter(r),typeof o.centerMarker>"u"||o.centerMarker)){if(u.value.mapId)return;if(a[0]){const t=$({position:a[0]});c.value.has(t)&&K(c.value.get(t)).then(()=>{c.value.delete(t)})}T({position:r})}},{immediate:!0}),J(async e=>{var t;p.value=await e.maps;const a=u.value.center,r={...u.value,center:!a||z(a)?void 0:a};s.value=new p.value.Map(q.value,r),a&&z(a)&&(B.value=await C(a),(t=s.value)==null||t.setCenter(B.value)),m.value=!0})});function X(e){return e.map(a=>{const r=a.featureType?`feature:${a.featureType}`:"",t=a.elementType?`element:${a.elementType}`:"",i=(a.stylers||[]).map(l=>Object.entries(l).map(([g,f])=>(g==="color"&&typeof f=="string"&&(f=f.replace("#","0x")),`${g}:${f}`)).join("|")).filter(Boolean).join("|");return[r,t,i].filter(Boolean).join("|")}).filter(Boolean)}const V=O(()=>{var r;let e=u.value.center;e&&typeof e=="object"&&(e=`${e.lat},${e.lng}`);const a=w(o.placeholderOptions,{zoom:u.value.zoom,center:e},{size:`${o.width}x${o.height}`,key:L,scale:2,style:(r=o.mapOptions)!=null&&r.styles?X(o.mapOptions.styles):void 0,markers:[...o.markers||[],e].filter(Boolean).map(t=>(typeof t=="object"&&t.location&&(t=t.location),typeof t=="object"&&t.lat?`${t.lat},${t.lng}`:t)).join("|")});return D("https://maps.googleapis.com/maps/api/staticmap",a)}),Y=O(()=>w(o.placeholderAttrs,{src:V.value,alt:"Google Maps Static Map",loading:o.aboveTheFold?"eager":"lazy",style:{cursor:"pointer",width:"100%",objectFit:"cover",height:"100%"}})),Z=O(()=>w(o.rootAttrs,{"aria-busy":d.value==="loading","aria-label":d.value==="awaitingLoad"?"Google Maps Static Map":d.value==="loading"?"Google Maps Map Embed Loading":"Google Maps Embed","aria-live":"polite",role:"application",style:{cursor:"pointer",position:"relative",maxWidth:"100%",width:`${o.width}px`,height:"'auto'",aspectRatio:`${o.width}/${o.height}`},...E instanceof Promise?E.ssrAttrs||{}:{}}));return ae(async()=>{var e,a,r;await Promise.all([...c.value.entries()].map(([,t])=>K(t))),c.value.clear(),(e=s.value)==null||e.unbindAll(),s.value=void 0,(r=(a=q.value)==null?void 0:a.firstChild)==null||r.remove()}),(e,a)=>(te(),re("div",ce({ref_key:"rootEl",ref:S},Z.value),[oe(W("div",{ref_key:"mapEl",ref:q,style:{width:"100%",height:"100%",maxWidth:"100%"}},null,512),[[ie,m.value]]),m.value?_("",!0):h(e.$slots,"placeholder",{key:0,placeholder:V.value},()=>[W("img",ne(se(Y.value)),null,16)]),F(d)!=="awaitingLoad"&&!m.value?h(e.$slots,"loading",{key:1},()=>[le(ve)]):_("",!0),F(d)==="awaitingLoad"?h(e.$slots,"awaitingLoad",{key:2}):F(d)==="error"?h(e.$slots,"error",{key:3}):_("",!0),h(e.$slots,"default")],16))}};export{Oe as default};
