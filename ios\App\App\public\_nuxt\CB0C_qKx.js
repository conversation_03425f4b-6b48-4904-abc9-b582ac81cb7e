import{a as x}from"./BBuxlZld.js";import{f as O,E as P,a as k}from"./C00tNdvy.js";import{u as w}from"./Bqx9KQ63.js";import{u as G}from"./BRIKaOVL.js";import{c as M}from"./C2RgRyPS.js";import{P as N}from"./ClItKS3j.js";import{i as V,T as _,r as d,c as $,o as A,w as b,a as D,q as u,s as U}from"./C3gyeM1K.js";const[Q,j]=M("RovingFocusGroup"),W=V({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(F,{expose:I,emit:T}){const a=F,p=T,{loop:C,orientation:f,dir:S}=_(a),m=G(S),i=x(a,"currentTabStopId",p,{defaultValue:a.defaultCurrentTabStopId,passive:a.currentTabStopId===void 0}),n=d(!1),r=d(!1),l=d(0),{getItems:v,CollectionSlot:g}=w({isProvider:!0});function y(e){const o=!r.value;if(e.currentTarget&&e.target===e.currentTarget&&o&&!n.value){const s=new CustomEvent(P,k);if(e.currentTarget.dispatchEvent(s),p("entryFocus",s),!s.defaultPrevented){const c=v().map(t=>t.ref).filter(t=>t.dataset.disabled!==""),B=c.find(t=>t.getAttribute("data-active")===""),R=c.find(t=>t.id===i.value),h=[B,R,...c].filter(Boolean);O(h,a.preventScrollOnEntryFocus)}}r.value=!1}function E(){setTimeout(()=>{r.value=!1},1)}return I({getItems:v}),j({loop:C,dir:m,orientation:f,currentTabStopId:i,onItemFocus:e=>{i.value=e},onItemShiftTab:()=>{n.value=!0},onFocusableItemAdd:()=>{l.value++},onFocusableItemRemove:()=>{l.value--}}),(e,o)=>(A(),$(u(g),null,{default:b(()=>[D(u(N),{tabindex:n.value||l.value===0?-1:0,"data-orientation":u(f),as:e.as,"as-child":e.asChild,dir:u(m),style:{outline:"none"},onMousedown:o[0]||(o[0]=s=>r.value=!0),onMouseup:E,onFocus:y,onBlur:o[1]||(o[1]=s=>n.value=!1)},{default:b(()=>[U(e.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}});export{W as _,Q as i};
