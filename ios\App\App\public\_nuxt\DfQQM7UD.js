import{Skeleton as R}from"./BaodNYLQ.js";import{i as h,r as I,c as y,o as l,w as c,a as p,q as e,s as k,a1 as M,v as P,x as S,l as C,k as j,e as d,W as g,g as A,j as E,m as z,d as f,p as w,$,t as x,a0 as B}from"./C3gyeM1K.js";import{a as N,u as q}from"./BBuxlZld.js";import{_ as U,a as W,b as L}from"./CUNFy6WN.js";import{c as Q}from"./jAU0Cazi.js";import{u as G,b as H}from"./D3IGe8e3.js";import{g as D,i as V}from"./B12VHTgT.js";import{_ as J}from"./D_nR533G.js";import{c as K,u as F}from"./C2RgRyPS.js";import{P as O}from"./CY42fsWK.js";import{P as T}from"./ClItKS3j.js";const[X,Y]=K(["MenuCheckboxItem","MenuRadioItem"],"MenuItemIndicatorContext"),Z=h({__name:"MenuItemIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(i){const n=X({modelValue:I(!1)});return(s,r)=>(l(),y(e(O),{present:s.forceMount||e(V)(e(n).modelValue.value)||e(n).modelValue.value===!0},{default:c(()=>[p(e(T),{as:s.as,"as-child":s.asChild,"data-state":e(D)(e(n).modelValue.value)},{default:c(()=>[k(s.$slots,"default")]),_:3},8,["as","as-child","data-state"])]),_:3},8,["present"]))}}),ee=h({__name:"MenuCheckboxItem",props:{modelValue:{type:[Boolean,String],default:!1},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select","update:modelValue"],setup(i,{emit:n}){const s=i,r=n,o=N(s,"modelValue",r);return Y({modelValue:o}),(u,t)=>(l(),y(J,M({role:"menuitemcheckbox"},s,{"aria-checked":e(V)(e(o))?"mixed":e(o),"data-state":e(D)(e(o)),onSelect:t[0]||(t[0]=async _=>{r("select",_),e(V)(e(o))?o.value=!0:o.value=!e(o)})}),{default:c(()=>[k(u.$slots,"default",{modelValue:e(o)})]),_:3},16,["aria-checked","data-state"]))}}),te=h({__name:"DropdownMenuCheckboxItem",props:{modelValue:{type:[Boolean,String]},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select","update:modelValue"],setup(i,{emit:n}){const s=i,o=G(n);return F(),(u,t)=>(l(),y(e(ee),P(S({...s,...e(o)})),{default:c(()=>[k(u.$slots,"default")]),_:3},16))}}),se=h({__name:"DropdownMenuItemIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(i){const n=i;return F(),(s,r)=>(l(),y(e(Z),P(S(n)),{default:c(()=>[k(s.$slots,"default")]),_:3},16))}}),ae=h({__name:"DropdownMenuCheckboxItem",props:{modelValue:{type:[Boolean,String]},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},emits:["select","update:modelValue"],setup(i,{emit:n}){const s=i,r=n,o=C(()=>{const{class:m,...v}=s;return v}),u=H(o,r),{locale:t}=j(),_=C(()=>t.value==="ar"?"rtl":"ltr");return(m,v)=>{const a=A;return l(),y(e(te),M({dir:_.value},e(u),{class:e(Q)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pe-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s.class)}),{default:c(()=>[d("span",{class:g(["absolute left-2 flex h-3.5 w-3.5 items-center justify-center",{"!left-auto !right-2":_.value==="ltr"}])},[p(e(se),null,{default:c(()=>[p(a,{name:"lucide:check",class:"w-4 h-4 text-primary-600"})]),_:1})],2),k(m.$slots,"default")]),_:3},16,["dir","class"])}}}),oe={class:"flex flex-col w-full"},le={key:0,class:"flex gap-2 px-2 items-center xs:hidden sm:flex"},ne={class:"border border-gray-200 rounded-lg p-2"},re={class:"ps-1 text-primary-600"},ce={class:"flex items-center"},ie={class:"text-sm font-normal"},de={key:1,class:"flex flex-col px-2 items-center"},ue=["onClick"],pe={class:"flex items-center justify-between text-base font-medium"},Ce=h({__name:"sorting",props:{total:{default:0},update:{type:Function},loading:{type:Boolean,default:!1}},setup(i){const n=q("(min-width: 600px)",{ssrWidth:1e3}),s=E(),r=I([{key:"createdAt,desc",title:"filters.soring-desc-title",selected:!0},{key:"createdAt,asc",title:"filters.soring-asc-title",selected:!1},{key:"avgRate,desc",title:"filters.soring-most-rate-title",selected:!1},{key:"avgRate,asc",title:"filters.soring-less-rate-title",selected:!1},{key:"basePrice,desc",title:"filters.soring-most-price-title",selected:!1},{key:"basePrice,asc",title:"filters.soring-less-price-title",selected:!1}]),o=C(()=>r.value.find(t=>!!t.selected)),u=(t,_=!0)=>{t&&(r.value=r.value.map(m=>({...m,selected:t==m.key})),_&&i.update({orderBy:t}))};return z(()=>{var t;u((t=s.query)==null?void 0:t.orderBy,!1)}),(t,_)=>{const m=R,v=A;return l(),f("div",oe,[e(n)?(l(),f("div",le,[t.loading?(l(),f($,{key:0},[p(m,{class:"w-44 h-8"}),p(m,{class:"w-16 h-8"})],64)):(l(),f($,{key:1},[d("div",ne,[p(e(L),null,{default:c(()=>[p(e(U),{class:"flex gap-1 text-xs focus-within:border-primary-600 active:border-primary-600"},{default:c(()=>[p(v,{name:"lucide:list-filter",size:"17px"}),d("span",null,x(t.$t("filters.sorting-title")),1),d("span",re,x(t.$t(e(o).title)),1)]),_:1}),p(e(W),{class:"w-48"},{default:c(()=>[(l(!0),f($,null,B(e(r),a=>(l(),y(e(ae),{key:a.key,"model-value":a.selected,"onUpdate:modelValue":b=>a.selected=b,class:g(["text-start w-full text-xs text-gray-500 py-2 px-4 cursor-pointer hover:bg-primary-100 font-medium",{"pointer-events-none":a.selected}]),onCloseAutoFocus:b=>!0,onSelect:b=>u(a.key)},{default:c(()=>[d("div",ce,[d("span",{class:g({"text-primary-500":a.selected})},x(t.$t(a.title)),3)])]),_:2},1032,["model-value","onUpdate:modelValue","class","onSelect"]))),128))]),_:1})]),_:1})]),d("span",ie,x(t.total)+" "+x(t.$t("filters.result-title")),1)],64))])):e(n)?w("",!0):(l(),f("div",de,[(l(!0),f($,null,B(e(r),a=>(l(),f("div",{key:a.key,class:g(["text-start w-full text-xs text-gray-500 py-2 px-4 cursor-pointer hover:bg-primary-100 font-medium",{"pointer-events-none":a.selected}]),onClick:b=>u(a.key)},[d("div",pe,[d("span",{class:g({"text-primary-500":a.selected})},x(t.$t(a.title)),3),d("div",null,[a.selected?(l(),y(v,{key:0,name:"lucide:check",class:"w-6 h-6 text-primary-600"})):w("",!0)])])],10,ue))),128))]))])}}});export{Ce as _};
