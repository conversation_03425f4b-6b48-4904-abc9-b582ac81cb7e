<!--
This is just a very simple component that throws an example error.
Feel free to delete this file.
-->

<script setup>
import * as Sentry from '@sentry/nuxt'

const hasSentError = ref(false)

const throwError = () => {
	// Sentry.startSpan(
	// 	{
	// 		name: 'Example Frontend Span test',
	// 		op: 'test2',
	// 	},
	// 	() => {
	// 		hasSentError.value = true
	// 		throw new Error('Sentry prod test')
	// 	},
	// )
	try {
		hasSentError.value = true
		// Create a more specific error
		const error = new Error('Sentry Test Error')
		error.name = 'SentryTestError'
		throw error
	} catch (error) {
		// Explicitly capture the error with additional context
		Sentry.captureException(error, {
			tags: {
				component: 'SentryErrorButton',
				testCase: 'manual-error-throw',
			},
			extra: {
				timestamp: new Date().toISOString(),
			},
		})
		console.error('Error captured and sent to Sentry:', error)
	}
}
</script>

<template>
	<div
		v-if="hasSentError"
		class="success"
	>
		Sample error was sent to Sentry.
	</div>
	<button
		@click="throwError"
	>
		<span>Throw Sample Error</span>
	</button>
</template>

<style scoped>
  button {
    border-radius: 8px;
    color: white;
    cursor: pointer;
    background-color: #553DB8;
    border: none;
    padding: 0;
    margin-top: 4px;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;

    & > span {
      display: inline-block;
      padding: 12px 16px;
      border-radius: inherit;
      font-size: 20px;
      font-weight: bold;
      line-height: 1;
      background-color: #7553FF;
      border: 1px solid #553DB8;
      transform: translateY(-4px);
    }

    &:hover > span {
      transform: translateY(-8px);
    }

    &:active > span {
      transform: translateY(0);
    }
  }

  .success {
    width: max-content;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 20px;
    line-height: 1;
    background-color: #00F261;
    border: 1px solid #00BF4D;
    color: #181423;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  }
</style>
