const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as E,k as L,r as A,l as z,a2 as D,ab as O,d as F,a as e,q as i,w as t,$ as U,o as v,e as a,t as r,h as b,a1 as g,ag as H,ah as K}from"./C3gyeM1K.js";import{_ as M,u as R}from"./BD9sJOps.js";import{_ as G}from"./DAA1LH-g.js";import{_ as J}from"./JNWsvxxg.js";import{_ as Q}from"./BsHCAE3W.js";import{_ as W,a as X,b as Y}from"./BzSOFl6X.js";import{u as Z,t as ee,o as V,s as n,b as oe,F as te}from"./ZoTeemxs.js";import{_ as se}from"./CjR7N8Rt.js";import{_ as ae}from"./CvMKnfiC.js";import{_ as re}from"./BRCXRF_f.js";import{_ as ne}from"./BoqtTPyX.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";import"./ClItKS3j.js";import"./DSkQD3L1.js";import"./BK9Yhb3N.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./D3IGe8e3.js";import"./DYRsuAWe.js";import"./FUKBWQwh.js";import"./DwXv0R8y.js";import"./DDFqsyHi.js";import"./ytjl1pT4.js";import"./CY42fsWK.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./C00tNdvy.js";import"./BRIKaOVL.js";import"./C1DP8TeJ.js";import"./BYbD1TwY.js";import"./Ls81bkHL.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";const le=H(()=>K(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(c=>c.default||c)),me={class:"font-bold text-2xl"},ie={class:"text-lg text-gray-600"},ce={class:"flex flex-col gap-4 w-full rounded-lg border shadow p-4 justify-center"},pe={class:"whitespace-pre-wrap text-center"},ue=["placeholder"],de={class:"flex col-span-1"},_e={class:"flex justify-between items-center gap-4 w-full max-sm:flex-col"},Xe=E({__name:"complaints",setup(c){const{t:s}=L(),y=A(1),k=z(()=>R().buildSinglePage(s("complain.title"))),l=Z({validationSchema:ee(V({fullName:n().min(1,s("error.required")).nullish(),email:n().email(s("error.email")).nullish(),message:n().min(1,s("error.required")).nullish(),problemType:n(),phone:V({number:n().min(5,s("error.required")),iso:n().min(1,s("error.required")),code:n().min(1,s("error.required")),isValid:oe()}).refine(o=>o.isValid?!0:l.setErrors({"phone.number":s("error.phone-number-invalid")}),{message:s("error.phone-number-invalid"),path:["number"]})})),initialValues:{fullName:"",email:"",phone:{number:"",code:"",iso:"",isValid:!1},problemType:"complaint",message:""}}),N=o=>{l.setFieldValue("phone",{number:o.nationalNumber,code:o.countryCallingCode,iso:o.countryCode,isValid:o.isValid})},T=l.handleSubmit(o=>{const{$api:$}=D();return $("/contact-us",{method:"POST",body:o}).then(()=>{O.success(s("form.contact-saved-successfully")),l.resetForm(),y.value+=1}).catch(p=>{console.log(p)})});return(o,$)=>{const p=M,P=Q,u=se,x=ae,d=X,_=Y,f=W,h=te,S=re,j=ne,q=le,B=J,I=G;return v(),F(U,null,[e(p,{links:i(k),class:"!border-0 !shadow-none"},null,8,["links"]),e(I,{class:"flex flex-col w-full h-full gap-2 my-6"},{default:t(()=>[e(P,{class:"text-center justify-center gap-4 rounded-lg"},{default:t(()=>[a("h1",me,r(o.$t("complain.title")),1),a("h2",ie,r(o.$t("complain.sub-title")),1)]),_:1}),e(B,{class:"grid grid-cols-2 gap-6 py-12 max-md:grid-cols-1"},{default:t(()=>{var w,C;return[(v(),F("form",{key:`form-${i(y)}`,class:"flex col-span-1"},[a("div",ce,[a("span",pe,r(o.$t("complain.form-title")),1),e(h,{name:"fullName"},{default:t(({componentField:m})=>[e(f,{class:"w-full relative"},{default:t(()=>[e(u,{class:"font-bold"},{default:t(()=>[b(r(o.$t("form.full-name"))+"* ",1)]),_:1}),e(d,null,{default:t(()=>[e(x,g({type:"text",placeholder:o.$t("form.full-name")},m),null,16,["placeholder"])]),_:2},1024),e(_)]),_:2},1024)]),_:1}),e(h,{name:"email"},{default:t(({componentField:m})=>[e(f,{class:"w-full relative"},{default:t(()=>[e(u,{class:"font-bold"},{default:t(()=>[b(r(o.$t("form.email"))+"* ",1)]),_:1}),e(d,null,{default:t(()=>[e(x,g({type:"email",placeholder:o.$t("form.email")},m),null,16,["placeholder"])]),_:2},1024),e(_)]),_:2},1024)]),_:1}),e(S,{error:(C=(w=i(l).errors)==null?void 0:w.value)==null?void 0:C.phone,onUpdate:N},null,8,["error"]),e(h,{name:"message"},{default:t(({componentField:m})=>[e(f,{class:"w-full relative"},{default:t(()=>[e(u,{class:"font-bold"},{default:t(()=>[b(r(o.$t("form.message"))+"* ",1)]),_:1}),e(d,null,{default:t(()=>[a("textarea",g({class:"flex border w-full min-h-20 p-2 rounded outline-none text-sm",placeholder:o.$t("form.message"),rows:"7"},m),null,16,ue)]),_:2},1024),e(_)]),_:2},1024)]),_:1}),e(j,{onClick:i(T)},{default:t(()=>[a("span",null,r(o.$t("form.send-message")),1)]),_:1},8,["onClick"])])])),a("div",de,[a("div",_e,[e(q,{src:"images/complaints.png",class:"w-full object-contain object-center p-12"})])])]}),_:1})]),_:1})],64)}}});export{Xe as default};
