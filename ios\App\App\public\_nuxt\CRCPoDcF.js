import{i,r as m,n,d,a4 as p,e as o,a5 as f,q as c,a1 as v,K as x,t as V,o as g}from"./C3gyeM1K.js";const w={class:"flex w-full gap-1 border border-gray-200 rounded p-2 flex-grow"},y={class:"text-xs text-gray-500"},b=i({__name:"price-input",props:{modelValue:{},formatValue:{type:Function}},emits:["update:price"],setup(s,{emit:l}){const u=l,a=m();return n(()=>s.modelValue,e=>{a.value=Number((e==null?void 0:e.value)||e)},{immediate:!0}),n(a,e=>{const t=s.formatValue((e==null?void 0:e.value)||e);u("update:price",t),a.value=t},{immediate:!0,deep:!0}),(e,t)=>(g(),d("div",w,[p(o("input",v({"onUpdate:modelValue":t[0]||(t[0]=r=>x(a)?a.value=r:null),class:"no-spinner inline px-1 outline-none w-full",type:"number",name:"price"},e.$attrs),null,16),[[f,c(a)]]),o("span",y,V(e.$t("JDO")),1)]))}});export{b as _};
