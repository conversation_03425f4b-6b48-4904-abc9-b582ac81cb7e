import{b as v,a as k}from"./D3IGe8e3.js";import{i as M,e as S,f as q,g as I,h as K,j as Q,d as W,a as J,b as L,c as X,_ as Y}from"./DEEq2QX3.js";import{i as _,c as u,o as c,w as t,s as m,a1 as w,q as e,v as Z,x as ee,l as C,a as s,p as h,e as P,g as te,d as B,W as y,$ as se,h as b,t as D}from"./C3gyeM1K.js";import{c as $}from"./jAU0Cazi.js";import{u as oe}from"./C2RgRyPS.js";import{P as ae}from"./ClItKS3j.js";import{a as ne,_ as le}from"./B9XwVa_7.js";import{u as re,j as x}from"./BBuxlZld.js";const ie=_({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(l){const o=l;oe();const a=M();return(r,n)=>(c(),u(e(ae),w(o,{type:r.as==="button"?"button":void 0,onClick:n[0]||(n[0]=d=>e(a).onOpenChange(!1))}),{default:t(()=>[m(r.$slots,"default")]),_:3},16,["type"]))}}),de=_({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(l,{emit:o}){const n=v(l,o);return(d,f)=>(c(),u(e(S),Z(ee(e(n))),{default:t(()=>[m(d.$slots,"default")]),_:3},16))}}),ce=_({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},hideClose:{type:Boolean},class:{}},emits:["close","escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(l,{emit:o}){const a=l,r=o,n=C(()=>{const{class:f,...p}=a;return p}),d=v(n,r);return(f,p)=>{const i=te;return c(),u(e(q),null,{default:t(()=>[s(e(I),{class:"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"}),s(e(K),w(e(d),{"aria-describedby":"Dialog content","aria-description":"Dialog description",class:e($)("fixed left-1/2 top-1/2 z-50 grid w-full max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 border bg-background py-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a.class)}),{default:t(()=>[f.hideClose?h("",!0):(c(),u(e(ie),{key:0,class:"absolute end-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground !outline-none",onClick:p[0]||(p[0]=g=>r("close"))},{default:t(()=>[s(i,{name:"lucide:x-circle",class:"w-7 h-7 text-gray-600"}),p[1]||(p[1]=P("span",{class:"sr-only"},"Close",-1))]),_:1})),m(f.$slots,"default")]),_:3},16,["class"])]),_:3})}}}),pe=_({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const o=l,a=C(()=>{const{class:n,...d}=o;return d}),r=k(a);return(n,d)=>(c(),u(e(ne),w(e(r),{class:e($)("text-md text-gray-700 px-4",o.class)}),{default:t(()=>[m(n.$slots,"default")]),_:3},16,["class"]))}}),ue=_({__name:"DialogFooter",props:{class:{}},setup(l){const o=l;return(a,r)=>(c(),B("div",{class:y(e($)("flex flex-col-reverse sm:flex-row sm:justify-end sm:gap-x-2 px-4",o.class))},[m(a.$slots,"default")],2))}}),me=_({__name:"DialogHeader",props:{class:{}},setup(l){const o=l;return(a,r)=>(c(),B("div",{class:y(e($)("flex flex-col gap-y-1.5 border-b border-gray-200 px-6 pb-4",o.class))},[m(a.$slots,"default")],2))}}),_e=_({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const o=l,a=C(()=>{const{class:n,...d}=o;return d}),r=k(a);return(n,d)=>(c(),u(e(Q),w(e(r),{class:e($)("text-xl font-semibold leading-none tracking-tight",o.class)}),{default:t(()=>[m(n.$slots,"default")]),_:3},16,["class"]))}}),fe={class:"max-h-[65vh] min-h-24 h-full overflow-y-auto"},Be=_({__name:"modal",props:{title:{},description:{},dismissible:{type:Boolean},hideClose:{type:Boolean},size:{default:"max-w-lg"}},emits:["close"],setup(l,{emit:o}){const a=re("(min-width: 600px)"),[r,n]=x(),[d,f]=x(),p=o;return(i,g)=>{const F=_e,z=me,O=pe,j=ue,T=ce,E=de,N=L,V=J,H=le,R=X,U=Y,A=W;return c(),B(se,null,[s(e(r),null,{default:t(()=>[m(i.$slots,"body")]),_:3}),s(e(d),null,{default:t(()=>[m(i.$slots,"footer")]),_:3}),e(a)?(c(),u(E,{key:0,"default-open":"","onUpdate:open":g[0]||(g[0]=G=>p("close"))},{default:t(()=>[s(T,{class:y([i.size]),"hide-close":i.hideClose},{default:t(()=>[s(z,{class:y({hidden:!i.title})},{default:t(()=>[s(F,null,{default:t(()=>[b(D(i.title||"Dialog title"),1)]),_:1})]),_:1},8,["class"]),s(O,{class:y({hidden:!i.description})},{default:t(()=>[b(D(i.description||"Dialog description"),1)]),_:1},8,["class"]),P("div",fe,[s(e(n))]),s(j,null,{default:t(()=>[s(e(f))]),_:1})]),_:1},8,["class","hide-close"])]),_:1})):h("",!0),e(a)?h("",!0):(c(),u(A,{key:1,"default-open":!0,dismissible:i.dismissible},{default:t(()=>[s(U,{onClose:g[1]||(g[1]=G=>p("close"))},{default:t(()=>[i.title?(c(),u(V,{key:0,class:"border-b border-gray-200"},{default:t(()=>[s(N,null,{default:t(()=>[b(D(i.title),1)]),_:1})]),_:1})):h("",!0),s(H,{class:"p-4"},{default:t(()=>[b(D(i.description),1)]),_:1}),s(e(n)),s(R,{class:"pt-2"},{default:t(()=>[s(e(f))]),_:1})]),_:1})]),_:1},8,["dismissible"]))],64)}}});export{Be as _};
