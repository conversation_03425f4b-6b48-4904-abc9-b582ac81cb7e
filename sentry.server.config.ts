import * as Sentry from '@sentry/nuxt'
import 'dotenv/config'

Sentry.init({
	// dsn: useRuntimeConfig().public.sentry.dsn,
	dsn: 'https://<EMAIL>/4509337280839680',

	// Setting this option to true will print useful information to the console while you're setting up Sentry.
	// debug: true,
	enabled: true,
	// tracesSampleRate: 1.0,
	// environment: useRuntimeConfig().public.sentry.environment,
	// environment: 'staging',
})
