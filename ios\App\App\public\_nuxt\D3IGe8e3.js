import{H as p,aI as f,aJ as u,V as i,l as d}from"./C3gyeM1K.js";function l(a){const e=p(),s=e==null?void 0:e.type.emits,r={};return s!=null&&s.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),s==null||s.forEach(o=>{r[f(u(o))]=(...n)=>a(o,...n)}),r}function P(a){const e=p(),s=Object.keys((e==null?void 0:e.type.props)??{}).reduce((o,n)=>{const t=(e==null?void 0:e.type.props[n]).default;return t!==void 0&&(o[n]=t),o},{}),r=i(a);return d(()=>{const o={},n=(e==null?void 0:e.vnode.props)??{};return Object.keys(n).forEach(t=>{o[u(t)]=n[t]}),Object.keys({...s,...o}).reduce((t,c)=>(r.value[c]!==void 0&&(t[c]=r.value[c]),t),{})})}function b(a,e){const s=P(a),r=e?l(e):{};return d(()=>({...s.value,...r}))}export{P as a,b,l as u};
