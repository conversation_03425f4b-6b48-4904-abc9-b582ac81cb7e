import{_ as a}from"./BispfOyS.js";import{i as o,c as e,w as r,W as _,o as n,h as c,t as p,_ as m}from"./C3gyeM1K.js";import"./D1vxl8R8.js";import"./jAU0Cazi.js";const i=o({__name:"order",props:{status:{}},setup(d){return(s,u)=>{const t=a;return n(),e(t,{class:_(`order-status ${s.status}`)},{default:r(()=>[c(p(s.$t(`orders.${s.status}`)),1)]),_:1},8,["class"])}}}),x=m(i,[["__scopeId","data-v-7b3c085f"]]);export{x as default};
