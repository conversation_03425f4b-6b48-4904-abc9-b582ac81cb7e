const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as k,k as j,l as C,r as f,d as a,a as i,q as _,w as d,$ as x,o as l,e as s,ag as L,t,a0 as u,g as z,ah as B}from"./C3gyeM1K.js";import{_ as I,u as q}from"./BD9sJOps.js";import{_ as E}from"./DAA1LH-g.js";import{_ as N}from"./JNWsvxxg.js";import{_ as P}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const V=L(()=>B(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(c=>c.default||c)),A={class:"absolute inset-0 flex flex-col items-center justify-center text-white gap-4 px-4 max-sm:gap-0"},D={class:"font-bold text-2xl max-sm:text-sm"},S={class:"text-xl font-semibold max-sm:text-xs"},F={class:"flex justify-between items-center gap-2 py-4 px-10 max-sm:px-4"},H={class:"flex w-1/2 p-12 max-sm:w-full max-sm:hidden"},O={class:"flex flex-col w-1/2 gap-2 max-sm:w-full"},R={class:"text-xl font-semibold"},T={class:"text-base text-gray-500"},G={class:"flex w-full gap-4 my-2"},J={class:"flex justify-between items-center"},K={class:"text-primary-600 font-normal"},M={class:"font-normal text-gray-600"},Q={class:"flex justify-between items-center gap-2 bg-sky-50 py-4 px-10 max-sm:px-4"},U={class:"flex flex-col w-1/2 gap-2 max-sm:w-full"},W={class:"text-xl font-semibold"},X={class:"text-base text-gray-500"},Y={class:"flex w-full gap-4 my-2"},Z={class:"flex justify-between items-center"},ss={class:"text-primary-600 font-normal"},ts={class:"font-normal text-gray-600"},es={class:"flex w-1/2 p-12 justify-end max-sm:hidden"},ds=k({__name:"vision",setup(c){const{t:e}=j(),v=C(()=>q().buildSinglePage(e("vision.title"))),g=f([{title:e("vision.vision-1-title"),text:e("vision.vision-1-text"),icon:"ui:vision-idea"},{title:e("vision.vision-2-title"),text:e("vision.vision-2-text"),icon:"ui:vision-growth"}]),h=f([{title:e("vision.mission-1-title"),text:e("vision.mission-1-text"),icon:"ui:mission-quality"},{title:e("vision.mission-2-title"),text:e("vision.mission-2-text"),icon:"ui:mission-satisfy"}]);return(o,is)=>{const w=I,m=V,y=P,p=z,b=N,$=E;return l(),a(x,null,[i(w,{links:_(v),class:"!border-0 !shadow-none"},null,8,["links"]),i($,{class:"flex flex-col w-full h-full gap-2 my-6"},{default:d(()=>[i(y,{class:"relative text-center justify-center gap-4 text-white rounded-lg p-0"},{default:d(()=>[i(m,{src:"/images/services/lg.png",sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw",srcset:"/images/services/sm.png 640w,/images/services/md.png 1024w, /images/services/lg.png 1920w",width:"1920",height:"1080",format:"webp",quality:"80",class:"rounded-lg object-cover"}),s("div",A,[s("h1",D,t(o.$t("vision.title")),1),s("p",S,t(o.$t("vision.sub-title")),1)])]),_:1}),i(b,{class:"flex flex-col gap-6 p-0"},{default:d(()=>[s("div",F,[s("div",H,[i(m,{src:"images/vision.png",class:"max-w-md h-full w-full"})]),s("div",O,[s("span",R,t(o.$t("vision.vision-title")),1),s("p",T,t(o.$t("vision.vision-text")),1),s("div",G,[(l(!0),a(x,null,u(_(g),(n,r)=>(l(),a("div",{key:`vision-${r}`,class:"flex flex-col p-4 gap-2 rounded-lg bg-sky-50 max-sm:w-1/2"},[s("div",J,[s("span",K,t(n.title),1),i(p,{name:n.icon,size:"15px"},null,8,["name"])]),s("p",M,t(n.text),1)]))),128))])])]),s("div",Q,[s("div",U,[s("span",W,t(o.$t("vision.mission-title")),1),s("p",X,t(o.$t("vision.mission-text")),1),s("div",Y,[(l(!0),a(x,null,u(_(h),(n,r)=>(l(),a("div",{key:`mission-${r}`,class:"flex flex-col p-4 gap-2 rounded-lg bg-sky-100 max-sm:w-1/2"},[s("div",Z,[s("span",ss,t(n.title),1),i(p,{name:n.icon,size:"15px"},null,8,["name"])]),s("p",ts,t(n.text),1)]))),128))])]),s("div",es,[i(m,{src:"images/mission.png",class:"max-w-md h-full w-full"})])])]),_:1})]),_:1})],64)}}});export{ds as default};
