import{i as s,y as i,z as f,l as n,A as d,B as x,C as L}from"./C3gyeM1K.js";const a=i({...L,componentName:"NuxtLinkLocale"}),m=s({name:"NuxtLinkLocale",props:{...a.props,locale:{type:String,default:void 0,required:!1}},setup(t,{slots:o}){const l=f(),u=n(()=>{const e=t.to??t.href;return e!=null?l(e,t.locale):e}),r=n(()=>{if(t.external||t.target&&t.target!=="_self")return!0;const e=t.to??t.href;return typeof e=="object"?!1:e===""||e==null||x(e,{acceptRelative:!0})}),c=()=>{const e={...t};return r.value||(e.to=u.value),delete e.href,delete e.locale,e};return()=>d(a,c(),o.default)}});export{m as default};
