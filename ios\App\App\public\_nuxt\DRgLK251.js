import{_ as y,u as v}from"./BD9sJOps.js";import{i as b,X as C,l as a,d as k,a as t,q as n,w as l,$ as w,Y as $,o as B,e as s,t as r,g as H}from"./C3gyeM1K.js";import{_ as L}from"./DAA1LH-g.js";import{_ as A}from"./JNWsvxxg.js";import{_ as D}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const M={class:"font-bold text-2xl"},N={class:"text-lg text-gray-600"},S={class:"flex flex-col gap-4 bg-sky-50 p-4 rounded"},T={class:"flex items-center gap-2"},V={class:"flex rounded-full p-2 bg-sky-100"},j={class:"text-lg"},q=["innerHTML"],O=b({__name:"service-usage",async setup(E){let o,_;const{data:c}=([o,_]=C(()=>$("pages/terms-of-service")),o=await o,_(),o),m=a(()=>{var e;return(e=c.value)==null?void 0:e.body}),i=a(()=>{var e;return(e=c.value)==null?void 0:e.name}),d=a(()=>{var e;return(e=c.value)==null?void 0:e.metaDescription}),p=a(()=>v().buildSinglePage(i.value));return(e,F)=>{const u=y,f=D,x=H,g=A,h=L;return B(),k(w,null,[t(u,{links:n(p),class:"!border-0 !shadow-none"},null,8,["links"]),t(h,{class:"flex flex-col w-full h-full my-6"},{default:l(()=>[t(f,{class:"text-center justify-center gap-4 py-12"},{default:l(()=>[s("h1",M,r(n(i)),1),s("h2",N,r(n(d)),1)]),_:1}),t(g,{class:"flex flex-col gap-6"},{default:l(()=>[s("div",S,[s("div",T,[s("div",V,[t(x,{name:"ui:privacy"})]),s("span",j,r(e.$t("service-usage.section-1-title")),1)]),s("div",{class:"text-sm text-gray-600 ps-6",innerHTML:n(m)},null,8,q)])]),_:1})]),_:1})],64)}}});export{O as default};
