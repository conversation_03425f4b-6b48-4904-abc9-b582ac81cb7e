import{u as ge,b as Ie}from"./D3IGe8e3.js";import{i as A,r as D,T as Ne,s as N,q as l,m as we,c as I,o as E,w as k,a as fe,a1 as q,p as Me,v as He,x as ze,l as L,E as he,Z as ct,aq as vt,aT as pt,H as ft,n as ae,R as mt,ar as yt,L as Le,d as be,W as We}from"./C3gyeM1K.js";import{c as re}from"./jAU0Cazi.js";import{a as gt,b as wt,u as ht,_ as bt}from"./DDFqsyHi.js";import{c as je,u as G}from"./C2RgRyPS.js";import{P as Ue}from"./ClItKS3j.js";import{a as $t}from"./BBuxlZld.js";import{P as qe}from"./CY42fsWK.js";import{a as Ot}from"./B12VHTgT.js";import{u as Re}from"./DSkQD3L1.js";import{g as Ce}from"./FUKBWQwh.js";import{u as Dt}from"./ytjl1pT4.js";const[J,Bt]=je("DialogRoot"),Pt=A({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(t,{emit:a}){const n=t,e=$t(n,"open",a,{defaultValue:n.defaultOpen,passive:n.open===void 0}),v=D(),r=D(),{modal:s}=Ne(n);return Bt({open:e,modal:s,openModal:()=>{e.value=!0},onOpenChange:p=>{e.value=p},onOpenToggle:()=>{e.value=!e.value},contentId:"",titleId:"",descriptionId:"",triggerElement:v,contentElement:r}),(p,d)=>N(p.$slots,"default",{open:l(e)})}}),Ke=A({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:a}){const n=t,o=a,e=J(),{forwardRef:v,currentElement:r}=G();return e.titleId||(e.titleId=Re(void 0,"reka-dialog-title")),e.descriptionId||(e.descriptionId=Re(void 0,"reka-dialog-description")),we(()=>{e.contentElement=r,Ce()!==document.body&&(e.triggerElement.value=Ce())}),(s,p)=>(E(),I(l(wt),{"as-child":"",loop:"",trapped:n.trapFocus,onMountAutoFocus:p[5]||(p[5]=d=>o("openAutoFocus",d)),onUnmountAutoFocus:p[6]||(p[6]=d=>o("closeAutoFocus",d))},{default:k(()=>[fe(l(gt),q({id:l(e).contentId,ref:l(v),as:s.as,"as-child":s.asChild,"disable-outside-pointer-events":s.disableOutsidePointerEvents,role:"dialog","aria-describedby":l(e).descriptionId,"aria-labelledby":l(e).titleId,"data-state":l(Ot)(l(e).open.value)},s.$attrs,{onDismiss:p[0]||(p[0]=d=>l(e).onOpenChange(!1)),onEscapeKeyDown:p[1]||(p[1]=d=>o("escapeKeyDown",d)),onFocusOutside:p[2]||(p[2]=d=>o("focusOutside",d)),onInteractOutside:p[3]||(p[3]=d=>o("interactOutside",d)),onPointerDownOutside:p[4]||(p[4]=d=>o("pointerDownOutside",d))}),{default:k(()=>[N(s.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),_t=A({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:a}){const n=t,o=a,e=J(),v=ge(o),{forwardRef:r,currentElement:s}=G();return Dt(s),(p,d)=>(E(),I(Ke,q({...n,...l(v)},{ref:l(r),"trap-focus":l(e).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:d[0]||(d[0]=y=>{var f;y.defaultPrevented||(y.preventDefault(),(f=l(e).triggerElement.value)==null||f.focus())}),onPointerDownOutside:d[1]||(d[1]=y=>{const f=y.detail.originalEvent,i=f.button===0&&f.ctrlKey===!0;(f.button===2||i)&&y.preventDefault()}),onFocusOutside:d[2]||(d[2]=y=>{y.preventDefault()})}),{default:k(()=>[N(p.$slots,"default")]),_:3},16,["trap-focus"]))}}),Tt=A({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:a}){const n=t,e=ge(a);G();const v=J(),r=D(!1),s=D(!1);return(p,d)=>(E(),I(Ke,q({...n,...l(e)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:d[0]||(d[0]=y=>{var f;y.defaultPrevented||(r.value||(f=l(v).triggerElement.value)==null||f.focus(),y.preventDefault()),r.value=!1,s.value=!1}),onInteractOutside:d[1]||(d[1]=y=>{var $;y.defaultPrevented||(r.value=!0,y.detail.originalEvent.type==="pointerdown"&&(s.value=!0));const f=y.target;(($=l(v).triggerElement.value)==null?void 0:$.contains(f))&&y.preventDefault(),y.detail.originalEvent.type==="focusin"&&s.value&&y.preventDefault()})}),{default:k(()=>[N(p.$slots,"default")]),_:3},16))}}),St=A({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:a}){const n=t,o=a,e=J(),v=ge(o),{forwardRef:r}=G();return(s,p)=>(E(),I(l(qe),{present:s.forceMount||l(e).open.value},{default:k(()=>[l(e).modal.value?(E(),I(_t,q({key:0,ref:l(r)},{...n,...l(v),...s.$attrs}),{default:k(()=>[N(s.$slots,"default")]),_:3},16)):(E(),I(Tt,q({key:1,ref:l(r)},{...n,...l(v),...s.$attrs}),{default:k(()=>[N(s.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),xt=A({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(t){const a=J();return ht(!0),G(),(n,o)=>(E(),I(l(Ue),{as:n.as,"as-child":n.asChild,"data-state":l(a).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:k(()=>[N(n.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Et=A({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(t){const a=J(),{forwardRef:n}=G();return(o,e)=>{var v;return(v=l(a))!=null&&v.modal.value?(E(),I(l(qe),{key:0,present:o.forceMount||l(a).open.value},{default:k(()=>[fe(xt,q(o.$attrs,{ref:l(n),as:o.as,"as-child":o.asChild}),{default:k(()=>[N(o.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):Me("",!0)}}}),Rt=A({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(t){const a=t,n=J();return G(),(o,e)=>(E(),I(l(Ue),q(a,{id:l(n).titleId}),{default:k(()=>[N(o.$slots,"default")]),_:3},16,["id"]))}}),Ct=A({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const a=t;return(n,o)=>(E(),I(l(bt),He(ze(a)),{default:k(()=>[N(n.$slots,"default")]),_:3},16))}});(function(){var t;try{if(typeof document<"u"){var a=document.createElement("style");a.nonce=(t=document.head.querySelector("meta[property=csp-nonce]"))==null?void 0:t.content,a.appendChild(document.createTextNode('[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32,.72,0,1);animation-duration:.5s;animation-timing-function:cubic-bezier(.32,.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform, 100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform, 100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform, 100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform, 100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top],[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height, 0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left],[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height, 0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(.32,.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32,.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true]):after{content:"";position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]:after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]:after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]:after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]:after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not([data-state=closed]){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:hover,[data-vaul-handle]:active{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover: hover) and (pointer: fine){[data-vaul-drawer]{-webkit-user-select:none;user-select:none}}@media (pointer: fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{0%{transform:translate3d(0,var(--initial-transform, 100%),0)}to{transform:translateZ(0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform, 100%),0)}}@keyframes slideFromTop{0%{transform:translate3d(0,calc(var(--initial-transform, 100%) * -1),0)}to{transform:translateZ(0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform, 100%) * -1),0)}}@keyframes slideFromLeft{0%{transform:translate3d(calc(var(--initial-transform, 100%) * -1),0,0)}to{transform:translateZ(0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform, 100%) * -1),0,0)}}@keyframes slideFromRight{0%{transform:translate3d(var(--initial-transform, 100%),0,0)}to{transform:translateZ(0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform, 100%),0,0)}}')),document.head.appendChild(a)}}catch(n){console.error("vite-plugin-css-injected-by-js",n)}})();const kt=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const At=t=>typeof t<"u";function Ft(t){return JSON.parse(JSON.stringify(t))}function ke(t,a,n,o={}){var e,v,r;const{clone:s=!1,passive:p=!1,eventName:d,deep:y=!1,defaultValue:f,shouldEmit:i}=o,$=ft(),O=n||($==null?void 0:$.emit)||((e=$==null?void 0:$.$emit)==null?void 0:e.bind($))||((r=(v=$==null?void 0:$.proxy)==null?void 0:v.$emit)==null?void 0:r.bind($==null?void 0:$.proxy));let m=d;a||(a="modelValue"),m=m||`update:${a.toString()}`;const j=c=>s?typeof s=="function"?s(c):Ft(c):c,K=()=>At(t[a])?j(t[a]):f,w=c=>{i?i(c)&&O(m,c):O(m,c)};if(p){const c=K(),h=D(c);let g=!1;return ae(()=>t[a],P=>{g||(g=!0,h.value=j(P),Le(()=>g=!1))}),ae(h,P=>{!g&&(P!==t[a]||y)&&w(P)},{deep:y}),h}else return L({get(){return K()},set(c){w(c)}})}const[$e,It]=je("DrawerRoot"),Ve=new WeakMap;function C(t,a,n=!1){if(!t||!(t instanceof HTMLElement)||!a)return;const o={};Object.entries(a).forEach(([e,v])=>{if(e.startsWith("--")){t.style.setProperty(e,v);return}o[e]=t.style[e],t.style[e]=v}),!n&&Ve.set(t,o)}function Nt(t,a){if(!t||!(t instanceof HTMLElement))return;const n=Ve.get(t);n&&Object.entries(n).forEach(([o,e])=>{t.style[o]=e})}function ve(t,a){const n=window.getComputedStyle(t),o=n.transform||n.webkitTransform||n.mozTransform;let e=o.match(/^matrix3d\((.+)\)$/);return e?Number.parseFloat(e[1].split(", ")[x(a)?13:12]):(e=o.match(/^matrix\((.+)\)$/),e?Number.parseFloat(e[1].split(", ")[x(a)?5:4]):null)}function Mt(t){return 8*(Math.log(t+1)-2)}function x(t){switch(t){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return t}}function ye(t,a){if(!t)return()=>{};const n=t.style.cssText;return Object.assign(t.style,a),()=>{t.style.cssText=n}}function Ht(...t){return(...a)=>{for(const n of t)typeof n=="function"&&n(...a)}}const T={DURATION:.5,EASE:[.32,.72,0,1]},Ye=.4,zt=.25,Lt=100,Xe=8,pe=16,Ze=26,Ae="vaul-dragging";function Wt({activeSnapPoint:t,snapPoints:a,drawerRef:n,overlayRef:o,fadeFromIndex:e,onSnapPointChange:v,direction:r}){const s=D(typeof window<"u"?{innerWidth:window.innerWidth,innerHeight:window.innerHeight}:void 0);function p(){s.value={innerWidth:window.innerWidth,innerHeight:window.innerHeight}}we(()=>{typeof window<"u"&&window.addEventListener("resize",p)}),mt(()=>{typeof window<"u"&&window.removeEventListener("resize",p)});const d=L(()=>(a.value&&t.value===a.value[a.value.length-1])??null),y=L(()=>a.value&&a.value.length>0&&((e==null?void 0:e.value)||(e==null?void 0:e.value)===0)&&!Number.isNaN(e==null?void 0:e.value)&&a.value[(e==null?void 0:e.value)??-1]===t.value||!a.value),f=L(()=>{var w;return((w=a.value)==null?void 0:w.findIndex(c=>c===t.value))??null}),i=L(()=>{var w;return((w=a.value)==null?void 0:w.map(c=>{const h=typeof c=="string";let g=0;if(h&&(g=Number.parseInt(c,10)),x(r.value)){const _=h?g:s.value?c*s.value.innerHeight:0;return s.value?r.value==="bottom"?s.value.innerHeight-_:-s.value.innerHeight+_:_}const P=h?g:s.value?c*s.value.innerWidth:0;return s.value?r.value==="right"?s.value.innerWidth-P:-s.value.innerWidth+P:P}))??[]}),$=L(()=>{var w;return f.value!==null?(w=i.value)==null?void 0:w[f.value]:null}),O=w=>{var c,h,g,P;const _=((c=i.value)==null?void 0:c.findIndex(z=>z===w))??null;Le(()=>{var z;v(_,i.value),C((z=n.value)==null?void 0:z.$el,{transition:`transform ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`,transform:x(r.value)?`translate3d(0, ${w}px, 0)`:`translate3d(${w}px, 0, 0)`})}),i.value&&_!==i.value.length-1&&_!==(e==null?void 0:e.value)?C((h=o.value)==null?void 0:h.$el,{transition:`opacity ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`,opacity:"0"}):C((g=o.value)==null?void 0:g.$el,{transition:`opacity ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`,opacity:"1"}),t.value=_!==null?((P=a.value)==null?void 0:P[_])??null:null};ae([t,i,a],()=>{var w;if(t.value){const c=((w=a.value)==null?void 0:w.findIndex(h=>h===t.value))??-1;i.value&&c!==-1&&typeof i.value[c]=="number"&&O(i.value[c])}},{immediate:!0});function m({draggedDistance:w,closeDrawer:c,velocity:h,dismissible:g}){var P,_,z;if(e.value===void 0)return;const Y=r.value==="bottom"||r.value==="right"?($.value??0)-w:($.value??0)+w,se=f.value===e.value-1,Q=f.value===0,V=w>0;if(se&&C((P=o.value)==null?void 0:P.$el,{transition:`opacity ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`}),h>2&&!V){g?c():O(i.value[0]);return}if(h>2&&V&&i&&a.value){O(i.value[a.value.length-1]);return}const ne=(_=i.value)==null?void 0:_.reduce((U,b)=>typeof U!="number"||typeof b!="number"?U:Math.abs(b-Y)<Math.abs(U-Y)?b:U),ee=x(r.value)?window.innerHeight:window.innerWidth;if(h>Ye&&Math.abs(w)<ee*.4){const U=V?1:-1;if(U>0&&d){O(i.value[(((z=a.value)==null?void 0:z.length)??0)-1]);return}if(Q&&U<0&&g&&c(),f.value===null)return;O(i.value[f.value+U]);return}O(ne)}function j({draggedDistance:w}){var c;if($.value===null)return;const h=r.value==="bottom"||r.value==="right"?$.value-w:$.value+w;(r.value==="bottom"||r.value==="right")&&h<i.value[i.value.length-1]||(r.value==="top"||r.value==="left")&&h>i.value[i.value.length-1]||C((c=n.value)==null?void 0:c.$el,{transform:x(r.value)?`translate3d(0, ${h}px, 0)`:`translate3d(${h}px, 0, 0)`})}function K(w,c){if(!a.value||typeof f.value!="number"||!i.value||e.value===void 0)return null;const h=f.value===e.value-1;if(f.value>=e.value&&c)return 0;if(h&&!c)return 1;if(!y.value&&!h)return null;const g=h?f.value+1:f.value-1,P=h?i.value[g]-i.value[g-1]:i.value[g+1]-i.value[g],_=w/Math.abs(P);return h?1-_:_}return{isLastSnapPoint:d,shouldFade:y,getPercentageDragged:K,activeSnapPointIndex:f,onRelease:m,onDrag:j,snapPointsOffset:i}}function Fe(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}let le=null;function jt(t){const{isOpen:a,modal:n,nested:o,hasBeenOpened:e,preventScrollRestoration:v,noBodyStyles:r}=t,s=D(typeof window<"u"?window.location.href:""),p=D(0);function d(){if(Fe()&&le===null&&a.value&&!r.value){le={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height};const{scrollX:f,innerHeight:i}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:`${-p.value}px`,left:`${-f}px`,right:"0px",height:"auto"}),setTimeout(()=>{requestAnimationFrame(()=>{const $=i-window.innerHeight;$&&p.value>=i&&(document.body.style.top=`-${p.value+$}px`)})},300)}}function y(){if(Fe()&&le!==null&&!r.value){const f=-Number.parseInt(document.body.style.top,10),i=-Number.parseInt(document.body.style.left,10);Object.assign(document.body.style,le),window.requestAnimationFrame(()=>{if(v.value&&s.value!==window.location.href){s.value=window.location.href;return}window.scrollTo(i,f)}),le=null}}return we(()=>{function f(){p.value=window.scrollY}f(),window.addEventListener("scroll",f),yt(()=>{window.removeEventListener("scroll",f)})}),ae([a,e,s],()=>{o.value||!e.value||(a.value?(window.matchMedia("(display-mode: standalone)").matches||d(),n.value||setTimeout(()=>{y()},500)):y())}),{restorePositionSetting:y}}function Ut(t,a){return t&&t.value?t:a}function qt(t){const{emitDrag:a,emitRelease:n,emitClose:o,emitOpenChange:e,open:v,dismissible:r,nested:s,modal:p,shouldScaleBackground:d,setBackgroundColorOnScale:y,scrollLockTimeout:f,closeThreshold:i,activeSnapPoint:$,fadeFromIndex:O,direction:m,noBodyStyles:j,handleOnly:K,preventScrollRestoration:w}=t,c=D(v.value??!1),h=D(!1),g=D(!1),P=D(!1),_=D(null),z=D(null),Y=D(null),se=D(null),Q=D(null),V=D(!1),ne=D(null),ee=D(0),U=D(!1);D(0);const b=D(null);D(0);const Oe=L(()=>{var u;return((u=b.value)==null?void 0:u.$el.getBoundingClientRect().height)||0}),H=Ut(t.snapPoints,D(void 0)),Ge=L(()=>{var u;return H&&(((u=H.value)==null?void 0:u.length)??0)>0}),Je=D(null),{activeSnapPointIndex:De,onRelease:Qe,snapPointsOffset:et,onDrag:tt,shouldFade:Be,getPercentageDragged:at}=Wt({snapPoints:H,activeSnapPoint:$,drawerRef:b,fadeFromIndex:O,overlayRef:_,onSnapPointChange:nt,direction:m});function nt(u,S){H.value&&u===S.length-1&&(z.value=new Date)}jt({isOpen:c,modal:p,nested:s,hasBeenOpened:h,noBodyStyles:j,preventScrollRestoration:w});function ie(){return(window.innerWidth-Ze)/window.innerWidth}function Pe(u,S){var R;if(!u)return!1;let B=u;const F=(R=window.getSelection())==null?void 0:R.toString(),M=b.value?ve(b.value.$el,m.value):null,W=new Date;if(B.hasAttribute("data-vaul-no-drag")||B.closest("[data-vaul-no-drag]"))return!1;if(m.value==="right"||m.value==="left")return!0;if(z.value&&W.getTime()-z.value.getTime()<500)return!1;if(M!==null&&(m.value==="bottom"?M>0:M<0))return!0;if(F&&F.length>0)return!1;if(Q.value&&W.getTime()-Q.value.getTime()<f.value&&M===0||S)return Q.value=W,!1;for(;B;){if(B.scrollHeight>B.clientHeight){if(B.scrollTop!==0)return Q.value=new Date,!1;if(B.getAttribute("role")==="dialog")return!0}B=B.parentNode}return!0}function ot(u){!r.value&&!H.value||b.value&&!b.value.$el.contains(u.target)||(g.value=!0,Y.value=new Date,u.target.setPointerCapture(u.pointerId),ee.value=x(m.value)?u.clientY:u.clientX)}function lt(u){var S,R,B,F,M,W;if(b.value&&g.value){const te=m.value==="bottom"||m.value==="right"?1:-1,ue=(ee.value-(x(m.value)?u.clientY:u.clientX))*te,de=ue>0,Te=H.value&&!r.value&&!de;if(Te&&De.value===0)return;const me=Math.abs(ue),Se=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]");let X=me/Oe.value;const xe=at(me,de);if(xe!==null&&(X=xe),Te&&X>=1||!V.value&&!Pe(u.target,de))return;if((S=b==null?void 0:b.value)==null||S.$el.classList.add(Ae),V.value=!0,C((R=b.value)==null?void 0:R.$el,{transition:"none"}),C((B=_.value)==null?void 0:B.$el,{transition:"none"}),H.value&&tt({draggedDistance:ue}),de&&!H.value){const Z=Mt(ue),ce=Math.min(Z*-1,0)*te;C((F=b.value)==null?void 0:F.$el,{transform:x(m.value)?`translate3d(0, ${ce}px, 0)`:`translate3d(${ce}px, 0, 0)`});return}const dt=1-X;if((Be.value||O.value&&De.value===O.value-1)&&(a(X),C((M=_.value)==null?void 0:M.$el,{opacity:`${dt}`,transition:"none"},!0)),Se&&_.value&&d.value){const Z=Math.min(ie()+X*(1-ie()),1),ce=8-X*8,Ee=Math.max(0,14-X*14);C(Se,{borderRadius:`${ce}px`,transform:x(m.value)?`scale(${Z}) translate3d(0, ${Ee}px, 0)`:`scale(${Z}) translate3d(${Ee}px, 0, 0)`,transition:"none"},!0)}if(!H.value){const Z=me*te;C((W=b.value)==null?void 0:W.$el,{transform:x(m.value)?`translate3d(0, ${Z}px, 0)`:`translate3d(${Z}px, 0, 0)`})}}}function _e(){var u;if(!b.value)return;const S=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]"),R=ve(b.value.$el,m.value);C(b.value.$el,{transform:"translate3d(0, 0, 0)",transition:`transform ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`}),C((u=_.value)==null?void 0:u.$el,{transition:`opacity ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`,opacity:"1"}),d.value&&R&&R>0&&c.value&&C(S,{borderRadius:`${Xe}px`,overflow:"hidden",...x(m.value)?{transform:`scale(${ie()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,transformOrigin:"top"}:{transform:`scale(${ie()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:`${T.DURATION}s`,transitionTimingFunction:`cubic-bezier(${T.EASE.join(",")})`},!0)}function oe(u){b.value&&(o(),u||(c.value=!1),window.setTimeout(()=>{H.value&&($.value=H.value[0])},T.DURATION*1e3))}he(()=>{if(!c.value&&d.value&&kt){const u=setTimeout(()=>{Nt(document.body)},200);return()=>clearTimeout(u)}}),ae(v,()=>{c.value=v.value,v.value||oe()});function rt(u){if(!g.value||!b.value)return;b.value.$el.classList.remove(Ae),V.value=!1,g.value=!1,se.value=new Date;const S=ve(b.value.$el,m.value);if(!Pe(u.target,!1)||!S||Number.isNaN(S)||Y.value===null)return;const R=se.value.getTime()-Y.value.getTime(),B=ee.value-(x(m.value)?u.clientY:u.clientX),F=Math.abs(B)/R;if(F>.05&&(P.value=!0,window.setTimeout(()=>{P.value=!1},200)),H.value){const W=m.value==="bottom"||m.value==="right"?1:-1;Qe({draggedDistance:B*W,closeDrawer:oe,velocity:F,dismissible:r.value}),n(!0);return}if(m.value==="bottom"||m.value==="right"?B>0:B<0){_e(),n(!0);return}if(F>Ye){oe(),n(!1);return}const M=Math.min(b.value.$el.getBoundingClientRect().height??0,window.innerHeight);if(S>=M*i.value){oe(),n(!1);return}n(!0),_e()}ae(c,u=>{u&&(z.value=new Date),e(u)},{immediate:!0});function st(u){var S,R;const B=u?(window.innerWidth-pe)/window.innerWidth:1,F=u?-16:0;ne.value&&window.clearTimeout(ne.value),C((S=b.value)==null?void 0:S.$el,{transition:`transform ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`,transform:`scale(${B}) translate3d(0, ${F}px, 0)`}),!u&&(R=b.value)!=null&&R.$el&&(ne.value=window.setTimeout(()=>{var M,W;const te=ve((M=b.value)==null?void 0:M.$el,m.value);C((W=b.value)==null?void 0:W.$el,{transition:"none",transform:x(m.value)?`translate3d(0, ${te}px, 0)`:`translate3d(${te}px, 0, 0)`})},500))}function it(u){var S;if(u<0)return;const R=x(m.value)?window.innerHeight:window.innerWidth,B=(R-pe)/R,F=B+u*(1-B),M=-16+u*pe;C((S=b.value)==null?void 0:S.$el,{transform:x(m.value)?`scale(${F}) translate3d(0, ${M}px, 0)`:`scale(${F}) translate3d(${M}px, 0, 0)`,transition:"none"})}function ut(u){var S;const R=x(m.value)?window.innerHeight:window.innerWidth,B=u?(R-pe)/R:1,F=u?-16:0;u&&C((S=b.value)==null?void 0:S.$el,{transition:`transform ${T.DURATION}s cubic-bezier(${T.EASE.join(",")})`,transform:x(m.value)?`scale(${B}) translate3d(0, ${F}px, 0)`:`scale(${B}) translate3d(${F}px, 0, 0)`})}return{open:v,isOpen:c,modal:p,keyboardIsOpen:U,hasBeenOpened:h,drawerRef:b,drawerHeightRef:Oe,overlayRef:_,handleRef:Je,isDragging:g,dragStartTime:Y,isAllowedToDrag:V,snapPoints:H,activeSnapPoint:$,hasSnapPoints:Ge,pointerStart:ee,dismissible:r,snapPointsOffset:et,direction:m,shouldFade:Be,fadeFromIndex:O,shouldScaleBackground:d,setBackgroundColorOnScale:y,onPress:ot,onDrag:lt,onRelease:rt,closeDrawer:oe,onNestedDrag:it,onNestedRelease:ut,onNestedOpenChange:st,emitClose:o,emitDrag:a,emitRelease:n,emitOpenChange:e,nested:s,handleOnly:K,noBodyStyles:j}}const Kt=A({__name:"DrawerRoot",props:{activeSnapPoint:{default:void 0},closeThreshold:{default:zt},shouldScaleBackground:{type:Boolean,default:void 0},setBackgroundColorOnScale:{type:Boolean,default:!0},scrollLockTimeout:{default:Lt},fixed:{type:Boolean,default:void 0},dismissible:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},nested:{type:Boolean,default:!1},direction:{default:"bottom"},noBodyStyles:{type:Boolean},handleOnly:{type:Boolean,default:!1},preventScrollRestoration:{type:Boolean},snapPoints:{default:void 0},fadeFromIndex:{default:void 0}},emits:["drag","release","close","update:open","update:activeSnapPoint","animationEnd"],setup(t,{expose:a,emit:n}){const o=t,e=n;pt();const v=L(()=>o.fadeFromIndex??(o.snapPoints&&o.snapPoints.length-1)),r=ke(o,"open",e,{defaultValue:o.defaultOpen,passive:o.open===void 0}),s=ke(o,"activeSnapPoint",e,{passive:o.activeSnapPoint===void 0}),p={emitDrag:O=>e("drag",O),emitRelease:O=>e("release",O),emitClose:()=>e("close"),emitOpenChange:O=>{e("update:open",O),setTimeout(()=>{e("animationEnd",O)},T.DURATION*1e3)}},{closeDrawer:d,hasBeenOpened:y,modal:f,isOpen:i}=It(qt({...p,...Ne(o),activeSnapPoint:s,fadeFromIndex:v,open:r}));function $(O){if(r.value!==void 0){p.emitOpenChange(O);return}i.value=O,O?y.value=!0:d()}return a({open:i}),(O,m)=>(E(),I(l(Pt),{open:l(i),modal:l(f),"onUpdate:open":$},{default:k(()=>[N(O.$slots,"default",{open:l(i)})]),_:3},8,["open","modal"]))}}),Vt=A({__name:"DrawerOverlay",setup(t){const{overlayRef:a,hasSnapPoints:n,isOpen:o,shouldFade:e}=$e();return(v,r)=>(E(),I(l(Et),{ref_key:"overlayRef",ref:a,"data-vaul-overlay":"","data-vaul-snap-points":l(o)&&l(n)?"true":"false","data-vaul-snap-points-overlay":l(o)&&l(e)?"true":"false"},null,8,["data-vaul-snap-points","data-vaul-snap-points-overlay"]))}}),Yt=()=>()=>{};function Xt(){const{direction:t,isOpen:a,shouldScaleBackground:n,setBackgroundColorOnScale:o,noBodyStyles:e}=$e(),v=D(null),r=D(document.body.style.backgroundColor);function s(){return(window.innerWidth-Ze)/window.innerWidth}he(p=>{if(a.value&&n.value){v.value&&clearTimeout(v.value);const d=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]");if(!d)return;Ht(o.value&&!e.value?ye(document.body,{background:"black"}):Yt,ye(d,{transformOrigin:x(t.value)?"top":"left",transitionProperty:"transform, border-radius",transitionDuration:`${T.DURATION}s`,transitionTimingFunction:`cubic-bezier(${T.EASE.join(",")})`}));const y=ye(d,{borderRadius:`${Xe}px`,overflow:"hidden",...x(t.value)?{transform:`scale(${s()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`}:{transform:`scale(${s()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`}});p(()=>{y(),v.value=window.setTimeout(()=>{r.value?document.body.style.background=r.value:document.body.style.removeProperty("background")},T.DURATION*1e3)})}},{flush:"pre"})}const Zt=A({__name:"DrawerContent",setup(t){const{open:a,isOpen:n,snapPointsOffset:o,hasSnapPoints:e,drawerRef:v,onPress:r,onDrag:s,onRelease:p,modal:d,emitOpenChange:y,dismissible:f,keyboardIsOpen:i,closeDrawer:$,direction:O,handleOnly:m}=$e();Xt();const j=D(!1),K=L(()=>o.value&&o.value.length>0?`${o.value[0]}px`:"0");function w(g){if(!d.value||g.defaultPrevented){g.preventDefault();return}i.value&&(i.value=!1),f.value?y(!1):g.preventDefault()}function c(g){m.value||r(g)}function h(g){m.value||s(g)}return he(()=>{e.value&&window.requestAnimationFrame(()=>{j.value=!0})}),(g,P)=>(E(),I(l(St),{ref_key:"drawerRef",ref:v,"data-vaul-drawer":"","data-vaul-drawer-direction":l(O),"data-vaul-delayed-snap-points":j.value?"true":"false","data-vaul-snap-points":l(n)&&l(e)?"true":"false",style:vt({"--snap-point-height":K.value}),onPointerdown:c,onPointermove:h,onPointerup:l(p),onPointerDownOutside:w,onOpenAutoFocus:P[0]||(P[0]=ct(()=>{},["prevent"])),onEscapeKeyDown:P[1]||(P[1]=_=>{l(f)||_.preventDefault()})},{default:k(()=>[N(g.$slots,"default")]),_:3},8,["data-vaul-drawer-direction","data-vaul-delayed-snap-points","data-vaul-snap-points","style","onPointerup"]))}}),ca=A({__name:"Drawer",props:{activeSnapPoint:{},closeThreshold:{},shouldScaleBackground:{type:Boolean,default:!0},setBackgroundColorOnScale:{type:Boolean},scrollLockTimeout:{},fixed:{type:Boolean},dismissible:{type:Boolean},modal:{type:Boolean},open:{type:Boolean},defaultOpen:{type:Boolean},nested:{type:Boolean},direction:{},noBodyStyles:{type:Boolean},handleOnly:{type:Boolean},preventScrollRestoration:{type:Boolean},snapPoints:{},fadeFromIndex:{}},emits:["drag","release","close","update:open","update:activeSnapPoint","animationEnd"],setup(t,{emit:a}){const e=Ie(t,a);return(v,r)=>(E(),I(l(Kt),He(ze(l(e))),{default:k(()=>[N(v.$slots,"default")]),_:3},16))}}),Gt=A({__name:"DrawerOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(t){const a=t,n=L(()=>{const{class:o,...e}=a;return e});return(o,e)=>(E(),I(l(Vt),q(n.value,{class:l(re)("fixed inset-0 z-50 bg-black/80",a.class)}),null,16,["class"]))}}),Jt={key:0,class:"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted"},va=A({inheritAttrs:!1,__name:"DrawerContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{},hasHand:{type:Boolean}},emits:["close","escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:a}){const n=t,o=a,e=Ie(n,o);return(v,r)=>(E(),I(l(Ct),null,{default:k(()=>[fe(Gt),fe(l(Zt),q({"aria-describedby":"Drawer content","aria-description":"Drawer description"},l(e),{class:l(re)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background",n.class),onClose:r[0]||(r[0]=s=>o("close"))}),{default:k(()=>[n.hasHand?(E(),be("div",Jt)):Me("",!0),N(v.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),pa=A({__name:"DrawerFooter",props:{class:{}},setup(t){const a=t;return(n,o)=>(E(),be("div",{class:We(l(re)("mt-auto flex flex-col gap-2 p-4",a.class))},[N(n.$slots,"default")],2))}}),fa=A({__name:"DrawerHeader",props:{class:{}},setup(t){const a=t;return(n,o)=>(E(),be("div",{class:We(l(re)("grid gap-1.5 p-4 text-center sm:text-left",a.class))},[N(n.$slots,"default")],2))}}),ma=A({__name:"DrawerTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(t){const a=t,n=L(()=>{const{class:o,...e}=a;return e});return(o,e)=>(E(),I(l(Rt),q(n.value,{class:l(re)("text-lg font-semibold leading-none tracking-tight text-start",a.class)}),{default:k(()=>[N(o.$slots,"default")]),_:3},16,["class"]))}});export{va as _,fa as a,ma as b,pa as c,ca as d,Pt as e,Ct as f,Et as g,St as h,J as i,Rt as j};
