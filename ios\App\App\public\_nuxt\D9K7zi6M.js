import{u as p}from"./Bzz9boR6.js";import{u as d}from"./C0giEChS.js";import{r as f,m as y,l as S,d as g,o as q,s as z,a1 as v}from"./C3gyeM1K.js";import"./BsYnu6hU.js";import"./CvhKjyCU.js";function E(t){return d("lemonSqueezy",()=>({scriptInput:{src:"https://assets.lemonsqueezy.com/lemon.js",crossorigin:!1},scriptOptions:{use(){if(!(typeof window.createLemonSqueezy>"u"))return window.createLemonSqueezy(),window.LemonSqueezy}}}),t)}const k={__name:"ScriptLemonSqueezy",props:{trigger:{type:[String,Array,Boolean],required:!1,default:"visible"}},emits:["ready","lemonSqueezyEvent"],setup(t,{emit:a}){const u=t,n=a,o=f(null),s=p({trigger:u.trigger,el:o}),i=E({scriptOptions:{trigger:s}});y(()=>{var e;(e=o.value)==null||e.querySelectorAll("a[href]").forEach(r=>{r.classList.add("lemonsqueezy-button")}),i.onLoaded(({Setup:r,Refresh:l})=>{r({eventHandler(m){n("lemonSqueezyEvent",m)}}),l(),n("ready",i)})});const c=S(()=>({...s instanceof Promise?s.ssrAttrs||{}:{}}));return(e,r)=>(q(),g("div",v({ref_key:"rootEl",ref:o},c.value),[z(e.$slots,"default")],16))}};export{k as default};
