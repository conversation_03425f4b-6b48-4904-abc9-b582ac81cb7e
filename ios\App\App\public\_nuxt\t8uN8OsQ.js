import S from"./CuUhnQhf.js";import{Skeleton as V}from"./BaodNYLQ.js";import q from"./C4nYFGHw.js";import{_ as A}from"./DAA1LH-g.js";import{_ as E}from"./JNWsvxxg.js";import{b as I,_ as T,a as _}from"./CiqmSqHP.js";import{_ as j,a as D}from"./D3J2c5MI.js";import{i as F,Y as P,l as i,c as o,w as a,o as t,e as u,a as s,t as d,h as Y,q as e,p,d as f,a0 as x,$ as y}from"./C3gyeM1K.js";const z={class:"px-4 py-4 flex justify-between items-center"},G={class:"text-gray-600 font-semibold sm:text-md md:text-lg lg:text-xl"},g=10,Z=F({__name:"history",setup(H){const{data:h,error:l,status:b}=P("products/history",{query:{limit:g}});l.value&&console.error("Error fetching history:",l.value);const k=i(()=>h.value),c=i(()=>b.value!=="success");return(m,J)=>{const $=S,r=V,w=q,C=E,v=A;return t(),o(v,{class:"col-span-3 flex flex-col max-md:col-span-3"},{default:a(()=>[u("div",z,[u("span",G,d(m.$t("home.history-title")),1),s($,{to:"/categories/smartphones",class:"max-sm:text-xs text-primary-600 hidden max-sm:flex"},{default:a(()=>[Y(d(m.$t("home.see-more")),1)]),_:1})]),s(C,{class:"flex w-full gap-4 px-4 place-items-center"},{default:a(()=>[s(e(I),{opts:{align:"start",slidesToScroll:"auto"},class:"w-full"},{default:a(({canScrollNext:N,canScrollPrev:B})=>[s(e(T),null,{default:a(()=>[e(c)?(t(!0),f(y,{key:0},x(Array(g),(n,L)=>(t(),o(e(_),{key:`product-skeleton-${L}`,class:"basis-2/12 flex flex-col rounded h-full border p-2 border-gray-200 w-44 me-4"},{default:a(()=>[s(r,{class:"w-full h-40 bg-gray-200 mb-2"}),s(r,{class:"w-4/5 h-5 bg-gray-200 mb-2"}),s(r,{class:"w-4/5 h-5 bg-gray-200 mb-2"}),s(r,{class:"w-1/3 h-5 bg-gray-200 mb-2"})]),_:2},1024))),128)):(t(!0),f(y,{key:1},x(e(k),n=>(t(),o(e(_),{key:n.productId,class:"basis-4/8"},{default:a(()=>[(t(),o(w,{key:`product-${n.productId}`,product:n,variant:"lg"},null,8,["product"]))]),_:2},1024))),128))]),_:1}),!e(c)&&B?(t(),o(e(j),{key:0})):p("",!0),!e(c)&&N?(t(),o(e(D),{key:1})):p("",!0)]),_:1})]),_:1})]),_:1})}}});export{Z as _};
