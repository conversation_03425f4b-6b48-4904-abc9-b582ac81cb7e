import{_ as o,l as r,d as c,o as l,aC as i}from"./C3gyeM1K.js";const d=["styles"],n={__name:"ScriptLoadingIndicator",props:{color:{type:String,required:!1,default:"currentColor"},size:{type:Number,required:!1,default:30}},setup(e){i(a=>({a11f5a6e:e.color}));const s=e,t=r(()=>({width:`${s.size}px`,height:`${s.size}px`}));return(a,p)=>(l(),c("div",{class:"loader",styles:t.value,"aria-label":"Loading...",role:"status"},null,8,d))}},_=o(n,[["__scopeId","data-v-36deb12c"]]);export{_ as default};
