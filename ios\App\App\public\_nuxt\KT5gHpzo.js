import{i as u,l as c,d as f,e as t,a as _,t as n,aq as m,q as a,o as w,g as h,aC as p,_ as x}from"./C3gyeM1K.js";const g={class:"rate-row flex w-full"},C={class:"flex items-center gap-2 w-full"},v={class:"text-sm font-bold"},E={class:"flex w-full flex-grow h-3 bg-gray-200 rounded overflow-hidden"},A={class:"font-bold text-sm w-10 text-center"},B=u({__name:"rate-row",props:{rate:{}},setup(s){p(e=>({c8c03f80:a(d),"2c92f78e":a(l)}));const l=c(()=>{switch(s.rate.rating){case 1:return"#F36C32";case 2:return"#F36C32";case 3:return"#F3AC30";case 4:return"#82AE04";case 5:default:return"#38AE04"}}),d=c(()=>{switch(s.rate.rating){case 1:return"#9BD781";case 2:return"#B4CE68";case 3:return"#82AE04";case 4:case 5:default:return"#38AE04"}}),o=c(()=>{var e,r;return Number((((e=s.rate)==null?void 0:e.percentage)??1)/(((r=s.rate)==null?void 0:r.totalReviews)??1)||0)});return(e,r)=>{const i=h;return w(),f("div",g,[t("div",C,[t("span",v,n(e.rate.rating),1),_(i,{name:"ui:rate-star",class:"icon w-3 h-3 drop-shadow-sm"}),t("div",E,[t("div",{class:"progress flex w- h-full rounded",style:m({width:a(o)+"%"})},null,4)]),t("span",A,n(a(o))+"%",1)])])}}}),y=x(B,[["__scopeId","data-v-8c0f5c05"]]);export{y as default};
