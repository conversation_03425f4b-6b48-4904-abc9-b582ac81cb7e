import{i as e,a3 as r,d as n,e as o,a1 as a,q as l,o as c}from"./C3gyeM1K.js";const i={id:"a12",cx:".66",fx:".66",cy:".3125",fy:".3125",gradientTransform:"scale(1.5)"},p=["stop-color"],d=["stop-color"],f=["stop-color"],u=["stop-color"],_=["stop-color"],m={"transform-origin":"center",fill:"none",stroke:"url(#a12)","stroke-width":"17","stroke-linecap":"round","stroke-dasharray":"200 1000","stroke-dashoffset":"0",cx:"100",cy:"100",r:"70"},h=["dur"],k=["stroke"],v=e({__name:"spinner",props:{duration:{default:.5},color:{default:"#9C3D88"}},setup(y){const s=r();return(t,g)=>(c(),n("svg",a({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200"},l(s)),[o("radialGradient",i,[o("stop",{offset:"0","stop-color":t.color},null,8,p),o("stop",{offset:".3","stop-color":t.color,"stop-opacity":".9"},null,8,d),o("stop",{offset:".6","stop-color":t.color,"stop-opacity":".6"},null,8,f),o("stop",{offset:".8","stop-color":t.color,"stop-opacity":".3"},null,8,u),o("stop",{offset:"1","stop-color":t.color,"stop-opacity":"0"},null,8,_)]),o("circle",m,[o("animateTransform",{type:"rotate",attributeName:"transform",calcMode:"spline",dur:t.duration,values:"360;0",keyTimes:"0;1",keySplines:"0 0 1 1",repeatCount:"indefinite"},null,8,h)]),o("circle",{"transform-origin":"center",fill:"none",opacity:".2",stroke:t.color,"stroke-width":"17","stroke-linecap":"round",cx:"100",cy:"100",r:"70"},null,8,k)],16))}});export{v as _};
