const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as u,d as _,a as s,w as e,q as t,o as m,$ as p,a0 as h,e as a,t as i,ag as x,ah as w}from"./C3gyeM1K.js";import{_ as g}from"./BoqtTPyX.js";import{_ as $}from"./DAA1LH-g.js";import{_ as v}from"./JNWsvxxg.js";import{_ as b,a as y,b as B}from"./CiqmSqHP.js";const k=x(()=>w(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(l=>l.default||l)),z={class:"w-full sm:w-auto col-span-3"},C={class:"flex absolute w-full h-full z-1"},E={class:"flex flex-col gap-4 xs:p-4 sm:ps-20 sm:pt-12"},L={class:"text-white font-bold max-w-96 leading-normal text-md sm:text-4xl"},N={class:"text-white font-semibold w-full md:w-1/2 sm:w-2/3 leading-normal text-xs sm:text-2xl"},V={class:"text-xs sm:text-lg font-semibold"},j=u({__name:"hero",setup(l){const c=[{source:"/images/hero.png"}];return(o,q)=>{const r=g,d=k;return m(),_("div",z,[s(t(B),{class:"relative w-full"},{default:e(()=>[s(t(b),null,{default:e(()=>[(m(),_(p,null,h(c,(f,n)=>s(t(y),{id:`slide-${n}`,key:`slide-${n}`,class:"!p-0",disabled:""},{default:e(()=>[s(t($),{class:"p-0"},{default:e(()=>[s(t(v),{class:"relative flex aspect-video h-48 sm:h-96 sm:min-h-96 w-full p-0"},{default:e(()=>[a("div",C,[a("div",E,[a("h1",L,i(o.$t("home.hero-title")),1),a("p",N,i(o.$t("home.hero-sub-title")),1),s(r,{as:"a",href:"",variant:"white",class:"max-w-28 h-6 px-2 sm:h-10 sm:max-w-44"},{default:e(()=>[a("span",V,i(o.$t("home.widget-pay-now")),1)]),_:1})])]),s(d,{src:f.source,alt:"Slide image 1",quality:"90",height:"100%",width:"100%",loading:"eager",class:"object-fill w-full h-full aspect-auto"},null,8,["src"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["id"])),64))]),_:1})]),_:1})])}}});export{j as _};
