import g from"./CuUhnQhf.js";import{i as y,k as w,d as a,e,a as p,w as m,$ as u,a0 as b,q as o,o as n,h as f,t as r,p as k,Z as v,W as C}from"./C3gyeM1K.js";const L={class:"flex justify-center w-full bg-gray-100 will-change-transform",dir:"ltr"},N={class:"container flex justify-between gap-4 px-4 py-2 font-medium text-gray-500 xs:text-xs sm:text-md"},$={class:"flex gap-4 items-center"},V={class:"flex gap-4 items-center"},q=["disabled","onClick"],B={key:0,class:"flex w-px bg-gray-500 h-4"},F=y({__name:"top",setup(j){const{locale:c,locales:i,setLocale:x}=w(),h=async t=>{await x(t)};return(t,l)=>{const d=g;return n(),a("div",L,[e("div",N,[e("div",$,[p(d,{to:"/faq"},{default:m(()=>[f(r(t.$t("header.top-faq")),1)]),_:1}),l[0]||(l[0]=e("div",{class:"flex w-px bg-gray-500 h-4"},null,-1)),p(d,{to:"/about-us"},{default:m(()=>[f(r(t.$t("header.top-about")),1)]),_:1})]),e("div",V,[(n(!0),a(u,null,b(o(i),(s,_)=>(n(),a(u,{key:`lang-${_}`},[e("button",{disabled:o(c)===s.code,class:C(o(c)===s.code?"text-primary-600":""),onClick:v(z=>h(s.code),["prevent"])},r(s.name),11,q),o(i).length-1!==_?(n(),a("div",B)):k("",!0)],64))),128))])])])}}});export{F as _};
