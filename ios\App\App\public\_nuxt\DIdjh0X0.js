import{d as j,a as I,b as V,c as z,_ as M}from"./DEEq2QX3.js";import{_ as F}from"./B9XwVa_7.js";import{i as O,ai as S,l as _,k as T,m as q,c as u,w as e,q as t,o as x,a as s,p as A,e as a,h as l,t as i,g as H,Z as W,W as Z,_ as E}from"./C3gyeM1K.js";import{_ as G}from"./40aoOQ_a.js";import{_ as J}from"./BoqtTPyX.js";import K from"./CuUhnQhf.js";import{u as P}from"./BCJZ1_ur.js";import"./D3IGe8e3.js";import"./jAU0Cazi.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./C2RgRyPS.js";import"./ClItKS3j.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./lU3sMj3q.js";import"./BGs9QzNZ.js";import"./C0H2RQFE.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";const Q={class:"flex w-full justify-between items-center"},R={class:"font-bold text-sm"},U={class:"text-sm font-bold text-gray-600"},X={class:"flex w-full justify-between items-center"},Y=O({__name:"index",props:{isOpen:{type:Boolean,default:!0}},emits:["set:cart-list"],setup(tt,{emit:w}){const m=w,{priceFormat:b}=P(),n=S(),y=_(()=>(n==null?void 0:n.list)||[]),p=_(()=>{var c;return(c=y.value)==null?void 0:c.length}),{locale:d,t:r}=T(),g=_(()=>d.value!=="ar"?"right":"left");return q(()=>{n.fetchMounted()}),(c,o)=>{const h=V,C=F,$=H,v=I,D=G,f=J,k=K,B=z,L=M,N=j;return x(),u(N,{id:"cart-list",open:c.isOpen,direction:t(g),dismissible:!1},{default:e(()=>[s(L,{"aria-describedby":"Cart Shopping list",class:Z([`drawer-${t(d)}`,"max-w-lg w-full h-dvh max-sm:rounded-none"])},{default:e(()=>[s(v,{class:"border-b border-gray-200 flex justify-between"},{default:e(()=>[s(h,{class:"text-start text-xl font-bold"},{default:e(()=>[l(i(t(r)("cart-list.title",{item:t(r)("wish-list.item",{count:t(p)}),number:t(p)})),1)]),_:1}),s(C,{class:"hidden"},{default:e(()=>o[3]||(o[3]=[l(" drawer description ")])),_:1}),a("button",{class:"w-8 h-8 rounded-full justify-center flex items-center border border-gray-400 mx-2",onClick:o[0]||(o[0]=()=>m("set:cart-list",!1))},[s($,{name:"lucide:x",class:"cursor-pointer w-5 h-5 text-gray-700"})])]),_:1}),s(D,{"onAdd:cartList":o[1]||(o[1]=()=>m("set:cart-list",!1))}),t(p)?(x(),u(B,{key:0,class:"flex-1 w-full flex-col gap-3 border-t pt-3"},{default:e(()=>[a("div",Q,[a("span",R,i(t(r)("orders.total")),1),a("span",U,i(t(b)(t(n).total.value)),1)]),a("div",X,[s(f,{size:"sm",variant:"text",onClick:o[2]||(o[2]=W(()=>m("set:cart-list",!1),["prevent"]))},{default:e(()=>[l(i(t(r)("cart-list.continue-shopping")),1)]),_:1}),s(f,{"as-child":"",size:"sm"},{default:e(()=>[s(k,{to:"/cart"},{default:e(()=>[l(i(t(r)("wallet.pay-now")),1)]),_:1})]),_:1})])]),_:1})):A("",!0)]),_:1},8,["class"])]),_:1},8,["open","direction"])}}}),kt=E(Y,[["__scopeId","data-v-e68c8605"]]);export{kt as default};
