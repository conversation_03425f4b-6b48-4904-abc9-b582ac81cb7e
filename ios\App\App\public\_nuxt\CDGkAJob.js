import{_ as I}from"./DAA1LH-g.js";import{_ as L}from"./JNWsvxxg.js";import{i as W,r as p,u as H,X as K,n as P,l as v,d as c,a as l,c as D,p as S,w as i,q as a,Y as T,o as n,e,t as s,Z as U,h as X,g as Z,$ as G,a0 as J,W as O}from"./C3gyeM1K.js";import{_ as Q}from"./ziqIZt9y.js";import{_ as R}from"./BsHCAE3W.js";import{Skeleton as tt}from"./BaodNYLQ.js";import{_ as et}from"./BoqtTPyX.js";import st from"./CvVcjLbz.js";import{_ as at}from"./Bg2UpioA.js";import{_ as lt}from"./zZSnkz7W.js";import{u as ot}from"./BCJZ1_ur.js";import{u as nt}from"./CRC5xmnh.js";const rt={class:"flex flex-col w-full min-h-full gap-6"},ct={class:"font-bold text-xl"},it={key:1,class:"flex bg-sky-50 justify-between items-start p-4 rounded-lg"},dt={class:"flex flex-col"},_t={class:"text-lg font-medium"},ut={class:"text-lg font-bold"},mt={class:"font-bold text-xl"},ft={key:0,class:"flex flex-col w-full gap-4"},pt={key:1,class:"flex flex-col w-full h-full items-center gap-6"},ht={class:"flex text-lg max-w-sm text-center font-semibold"},xt={key:2,class:"min-w-full divide-y divide-gray-200 bg-white text-sm text-gray-700 border rounded-lg"},yt={class:"p-2"},wt={class:"px-1 py-3 text-start font-medium"},gt={class:"px-1 py-3 text-start font-medium"},vt={class:"px-1 py-3 text-start font-medium"},$t={class:"px-1 py-3 text-start font-medium"},bt={class:"px-1 py-3 text-start font-medium"},kt={class:"px-1 py-3 text-start font-medium"},Ct={class:"divide-y divide-gray-100"},Bt={class:"px-1 py-3"},Ft={class:"px-1 py-3"},At={class:"px-1 py-3 font-semibold text-start"},Dt={class:"px-1 py-3"},St={class:"px-1 py-3 font-semibold"},Yt={class:"px-1 py-3"},Mt={key:0,class:"flex justify-center items-center w-full"},Ut=W({__name:"index",async setup(Nt){let f,$;const{priceFormat:h}=ot(),Y=p(1),b=H(),x=p(1),y=p([]),{data:d,error:k,status:M}=([f,$]=K(()=>T("/my/transactions",{watch:[Y,x],query:{perPage:10,page:x,order_by:"createdAt,desc"}})),f=await f,$(),f);k.value&&console.log("error on fetching wallet",k.value),P(()=>d,()=>{var t;y.value=(t=d.value)==null?void 0:t.data},{immediate:!0,deep:!0});const w=v(()=>M.value!=="success"&&!d.value),C=v(()=>{var t;return!((t=y.value)!=null&&t.length)}),g=p(!1),N=v(()=>{var t,r;return((r=(t=b.user)==null?void 0:t.wallet)==null?void 0:r.value)||0});return(t,r)=>{const B=R,_=tt,V=et,F=L,A=I,j=Z,q=st,z=Q;return n(),c("div",rt,[l(A,{class:"w-full"},{default:i(()=>[l(B,{class:"max-sm:hidden"},{default:i(()=>[e("span",ct,s(t.$t("profile.link-wallet-title")),1)]),_:1}),l(F,null,{default:i(()=>[a(w)?(n(),D(_,{key:0,class:"h-24 w-full"})):(n(),c("div",it,[e("div",dt,[e("span",_t,s(t.$t("wallet.total-balance")),1),e("span",ut,s(a(h)(a(N))),1)]),l(V,{onClick:r[0]||(r[0]=U(o=>g.value=!0,["prevent"]))},{default:i(()=>[X(s(t.$t("wallet.add-balance")),1)]),_:1})]))]),_:1})]),_:1}),l(A,{class:"w-full min-h-96 gap-6 flex flex-col flex-1 flex-grow"},{default:i(()=>[l(B,{class:"max-sm:hidden"},{default:i(()=>[e("span",mt,s(t.$t("wallet.history")),1)]),_:1}),l(F,{class:"flex flex-col flex-1 flex-grow"},{default:i(()=>[a(w)?(n(),c("div",ft,[l(_,{class:"h-8 w-full"}),l(_,{class:"h-8 w-full"}),l(_,{class:"h-8 w-full"}),l(_,{class:"h-8 w-full"})])):a(C)?(n(),c("div",pt,[l(j,{name:"ui:empty-wallet",class:"w-full h-60 mt-20"}),e("div",ht,s(t.$t("wallet.empty-text")),1)])):(n(),c("table",xt,[e("thead",yt,[e("tr",null,[e("th",wt,s(t.$t("wallet.identifier")),1),e("th",gt,s(t.$t("wallet.datetime")),1),e("th",vt,s(t.$t("wallet.price")),1),e("th",$t,s(t.$t("wallet.description")),1),e("th",bt,s(t.$t("wallet.balance-after")),1),e("th",kt,s(t.$t("wallet.status")),1)])]),e("tbody",Ct,[(n(!0),c(G,null,J(a(y),(o,u)=>{var m;return n(),c("tr",{key:u,class:O(["hover:bg-gray-50 transition",{"bg-gray-100":u%2===0}])},[e("td",Bt,s(o.userId),1),e("td",Ft,s(("useDateFormat"in t?t.useDateFormat:a(nt))(o==null?void 0:o.createdAt,"DD-MM-YYYY hh:mm A")),1),e("td",At,s(a(h)(o.amount)),1),e("td",Dt,s(((m=o.description)==null?void 0:m.join(" "))||"-"),1),e("td",St,s(a(h)(o.closingBalance)),1),e("td",Yt,[l(q,{status:o.status,size:"sm"},null,8,["status"])])],2)}),128))])]))]),_:1}),l(z,null,{default:i(()=>{var o,u,m;return[!a(w)&&!a(C)?(n(),c("div",Mt,[l(lt,{"items-per-page":(o=a(d))==null?void 0:o.per_page,total:(u=a(d))==null?void 0:u.last_page,"sibling-count":1,"show-edges":!0,"default-page":(m=a(d))==null?void 0:m.current_page,"onUpdate:page":r[1]||(r[1]=E=>x.value=E)},null,8,["items-per-page","total","default-page"])])):S("",!0)]}),_:1})]),_:1}),a(g)?(n(),D(at,{key:0,user:a(b).user,"onClose:modal":r[2]||(r[2]=o=>g.value=!1)},null,8,["user"])):S("",!0)])}}});export{Ut as _};
