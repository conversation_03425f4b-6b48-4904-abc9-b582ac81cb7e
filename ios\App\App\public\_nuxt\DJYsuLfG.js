const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as $,Y as L,l as f,d as t,a as o,w as l,q as e,o as s,p as r,$ as i,a0 as p,e as g,c,ag as N,ah as A}from"./C3gyeM1K.js";import B from"./CuUhnQhf.js";import{_ as C,a as E,b as V}from"./CiqmSqHP.js";import{_ as q,a as z}from"./D3J2c5MI.js";import{Skeleton as P}from"./BaodNYLQ.js";const I=N(()=>A(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(n=>n.default||n)),j={class:"col-span-3 w-full"},D={class:"flex flex-col items-center gap-4 w-full h-full"},F={class:"flex flex-col h-full items-center justify-center shadow rounded-lg bg-gray-100 min-w-32 p-4"},J=$({__name:"brands-slider",setup(n){const{data:h,error:_,status:x}=L("/lookups-website/brands",{query:{inHomePage:1}});_.value&&console.error("Error fetching brands slider section:",_.value);const y=f(()=>h.value),m=f(()=>x.value!=="success");return(H,O)=>{const b=I,k=B;return s(),t("div",j,[o(e(V),{opts:{align:"center"},class:"w-full rounded-lg p-4 bg-white"},{default:l(({canScrollNext:w,canScrollPrev:v})=>[o(e(C),{class:"gap-x-6 px-4 min-h-28"},{default:l(()=>[e(m)?(s(!0),t(i,{key:0},p(Array(8),a=>(s(),t("div",{key:`card-${a}`,class:"basis-1/10 sm:basis-1/8 md:basis-1/10 lg:basis-1/12 xl:basis-1/14"},[g("div",D,[o(e(P),{class:"w-32 h-full"})])]))),128)):(s(!0),t(i,{key:1},p(e(y),a=>(s(),c(e(E),{key:a.value,class:"basis-1/8 sm:basis-1/5 xl:basis-1/10"},{default:l(()=>[o(k,{to:`/categories/${a.meta.slug}`},{default:l(()=>{var u,d;return[g("div",F,[o(b,{src:(d=(u=a.meta.media.logoName)==null?void 0:u[0])==null?void 0:d.preview,alt:a.text,class:"max-h-16 h-full",width:"auto",height:"63",format:"webp",quality:"90",fit:"contain",loading:"lazy"},null,8,["src","alt"])])]}),_:2},1032,["to"])]),_:2},1024))),128))]),_:1}),e(m)?r("",!0):(s(),t(i,{key:0},[v?(s(),c(e(q),{key:0})):r("",!0),w?(s(),c(e(z),{key:1})):r("",!0)],64))]),_:1})])}}});export{J as _};
