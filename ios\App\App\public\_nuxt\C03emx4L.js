import{Skeleton as k}from"./BaodNYLQ.js";import{_ as N}from"./CaU5TI22.js";import{i as I,k as V,r as B,l as v,n as C,d as m,e as s,a as l,t as n,W as D,q as c,K as S,Z as U,o as g,g as K,a2 as T,ab as q}from"./C3gyeM1K.js";const z={class:"flex rounded-lg border border-gray-200 p-4 gap-4 w-full"},A={key:0,class:"w-full flex gap-2 flex-col"},E={class:"flex gap-2 justify-between items-center"},M={class:"flex gap-2 justify-between items-center"},O={key:1,class:"flex flex-col flex-grow gap-4 max-sm:text-sm"},P={class:"flex justify-between items-center w-full"},R={class:"flex w-1/3 font-bold"},W={class:"flex justify-end items-center gap-2 w-2/3"},Z={for:"set-default"},F={class:"flex justify-between items-center gap-4 w-full"},G={class:"flex w-full items-center gap-4"},H={class:"w-24 font-semibold"},J={class:"flex w-full items-center gap-4"},L={class:"w-24 font-semibold"},Q={class:"flex justify-between items-center gap-4 w-full"},X={class:"flex w-full items-center gap-4"},Y={class:"w-24 font-semibold"},ee={dir:"ltr",class:"text-nowrap"},se={class:"flex items-center gap-2 w-full justify-end"},le=I({__name:"card",props:{address:{},loading:{type:Boolean,default:!1}},emits:["fetch:address","delete:address","edit:address"],setup(d,{emit:y}){var h;const u=y,{t:_}=V(),i=B(((h=d.address)==null?void 0:h.default)===1),x=v(()=>{var e,t,a,o,r,f,p,w,b;return[((a=(t=(e=d.address)==null?void 0:e.city)==null?void 0:t.country)==null?void 0:a.name)||"",((r=(o=d.address)==null?void 0:o.city)==null?void 0:r.name)||"",((f=d.address)==null?void 0:f.district)||"",((p=d.address)==null?void 0:p.street)||"",((w=d.address)==null?void 0:w.apartmentNumber)||"",((b=d.address)==null?void 0:b.buildingNumber)||""].join(", ")}),$=v(()=>{var e,t,a,o;return["+",((t=(e=d.address)==null?void 0:e.phone)==null?void 0:t.code)+" ",(o=(a=d.address)==null?void 0:a.phone)==null?void 0:o.number].join("")}),j=async()=>{const{$api:e}=T();return e(`my/addresses/set-default-address/${d.address.addressId}`,{method:"PUT",body:{...d.address,default:1}}).then(()=>{q.success(_("form.address-default-updated")),u("fetch:address")}).catch(t=>{console.log("toggle default error",t)})};return C(()=>d.address,()=>{var e;i.value=((e=d.address)==null?void 0:e.default)===1},{immediate:!0,deep:!0}),(e,t)=>{const a=k,o=N,r=K;return g(),m("div",z,[e.loading?(g(),m("div",A,[s("div",E,[l(a,{class:"w-1/3 h-5"}),l(a,{class:"w-1/3 h-5"})]),l(a,{class:"w-1/2 h-5"}),s("div",M,[l(a,{class:"w-1/3 h-5"}),l(a,{class:"w-1/3 h-5"})])])):(g(),m("div",O,[s("div",P,[s("div",R,n(e.$t(`form.${e.address.buildingType}`)),1),s("div",W,[s("label",Z,n(e.$t("form.set-default-address")),1),l(o,{id:`set-default-${e.address.addressId}`,modelValue:c(i),"onUpdate:modelValue":t[0]||(t[0]=f=>S(i)?i.value=f:null),disabled:c(i),class:D({"opacity-50 pointer-event-none":c(i)}),"onUpdate:modelValueOnce":j},null,8,["id","modelValue","disabled","class"])])]),s("div",F,[s("div",G,[s("div",H,n(e.$t("form.receiver-name"))+": ",1),s("span",null,n(e.address.recipientName),1)])]),s("div",J,[s("div",L,n(e.$t("form.address"))+": ",1),s("span",null,n(c(x)),1)]),s("div",Q,[s("div",X,[s("div",Y,n(e.$t("form.phone"))+": ",1),s("span",ee,n(c($)),1)]),s("div",se,[s("button",{class:"flex p-1 hover:bg-gray-100 rounded-lg text-gray-600",onClick:t[1]||(t[1]=U(()=>u("edit:address",e.address.addressId),["prevent"]))},[l(r,{name:"lucide:pencil"})]),s("button",{class:"flex p-1 hover:bg-gray-100 rounded-lg text-gray-600",onClick:t[2]||(t[2]=()=>u("delete:address",e.address.addressId))},[l(r,{name:"lucide:trash-2"})])])])]))])}}});export{le as _};
