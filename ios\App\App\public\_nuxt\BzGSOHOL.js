import{_ as r,d as c,a as e,e as o,t as s,w as _,f as i,o as l,g as d,h as m}from"./C3gyeM1K.js";const x={},p={class:"flex flex-col gap-4 items-center justify-center py-28 bg-white text-primary-700 px-4 rounded-lg mt-12"},f={class:"flex flex-col items-center"},u={class:"text-base my-4 max-w-[800px] text-center font-bold text-gray-600"};function h(t,y){const a=d,n=i;return l(),c("div",p,[e(a,{name:"ui:404",size:"320px",class:"text-primary-600"}),o("div",f,[o("p",u,s(t.$t("error.404-title")),1),e(n,{to:"/",class:"mt-6 px-6 py-3 text-sm font-semibold text-white bg-primary-700 rounded-lg shadow-md hover:bg-primary-600 transition-all duration-300"},{default:_(()=>[m(s(t.$t("error.back-btn-title")),1)]),_:1})])])}const b=r(x,[["render",h]]);export{b as default};
