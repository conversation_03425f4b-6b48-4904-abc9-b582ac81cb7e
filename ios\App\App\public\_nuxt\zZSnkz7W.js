import{i as m,T as A,l as c,c as g,o as d,w as u,s as v,q as l,h as w,a1 as I,v as N,x as L,t as T,a as f,g as S,k as q,W as E,d as k,$ as F,a0 as D}from"./C3gyeM1K.js";import{c as V}from"./jAU0Cazi.js";import{u as y,c as U}from"./C2RgRyPS.js";import{P as C}from"./ClItKS3j.js";import{_ as j}from"./BoqtTPyX.js";import{a as W}from"./BBuxlZld.js";const[M,G]=U("PaginationRoot"),H=m({__name:"PaginationRoot",props:{page:{},defaultPage:{default:1},itemsPerPage:{},total:{default:0},siblingCount:{default:2},disabled:{type:Boolean},showEdges:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"nav"}},emits:["update:page"],setup(n,{emit:t}){const e=n,s=t,{siblingCount:o,disabled:a,showEdges:i}=A(e);y();const r=W(e,"page",s,{defaultValue:e.defaultPage,passive:e.page===void 0}),_=c(()=>Math.max(1,Math.ceil(e.total/(e.itemsPerPage||1))));return G({page:r,onPageChange(p){r.value=p},pageCount:_,siblingCount:o,disabled:a,showEdges:i}),(p,h)=>(d(),g(l(C),{as:p.as,"as-child":p.asChild},{default:u(()=>[v(p.$slots,"default",{page:l(r),pageCount:_.value})]),_:3},8,["as","as-child"]))}}),J=m({__name:"PaginationEllipsis",props:{asChild:{type:Boolean},as:{}},setup(n){const t=n;return y(),(e,s)=>(d(),g(l(C),I(t,{"data-type":"ellipsis"}),{default:u(()=>[v(e.$slots,"default",{},()=>[s[0]||(s[0]=w("…"))])]),_:3},16))}});function P(n,t){const e=t-n+1;return Array.from({length:e},(s,o)=>o+n)}function K(n){return n.map(t=>typeof t=="number"?{type:"page",value:t}:{type:"ellipsis"})}const B="ellipsis";function O(n,t,e,s){const a=t,i=Math.max(n-e,1),r=Math.min(n+e,a);if(s){const p=Math.min(2*e+5,t)-2,h=i>3&&Math.abs(a-p-1+1)>2&&Math.abs(i-1)>2,$=r<a-2&&Math.abs(a-p)>2&&Math.abs(a-r)>2;if(!h&&$)return[...P(1,p),B,a];if(h&&!$){const b=P(a-p+1,a);return[1,B,...b]}if(h&&$){const b=P(i,r);return[1,B,...b,B,a]}return P(1,a)}else{const _=e*2+1;return t<_?P(1,a):n<=e+1?P(1,_):t-n<=e?P(t-_+1,a):P(i,r)}}const Q=m({__name:"PaginationList",props:{asChild:{type:Boolean},as:{}},setup(n){const t=n;y();const e=M(),s=c(()=>K(O(e.page.value,e.pageCount.value,e.siblingCount.value,e.showEdges.value)));return(o,a)=>(d(),g(l(C),N(L(t)),{default:u(()=>[v(o.$slots,"default",{items:s.value})]),_:3},16))}}),X=m({__name:"PaginationListItem",props:{value:{},asChild:{type:Boolean},as:{default:"button"}},setup(n){const t=n;y();const e=M(),s=c(()=>e.page.value===t.value),o=c(()=>e.disabled.value);return(a,i)=>(d(),g(l(C),I(t,{"data-type":"page","aria-label":`Page ${a.value}`,"aria-current":s.value?"page":void 0,"data-selected":s.value?"true":void 0,disabled:o.value,type:a.as==="button"?"button":void 0,onClick:i[0]||(i[0]=r=>!o.value&&l(e).onPageChange(a.value))}),{default:u(()=>[v(a.$slots,"default",{},()=>[w(T(a.value),1)])]),_:3},16,["aria-label","aria-current","data-selected","disabled","type"]))}}),Y=m({__name:"PaginationNext",props:{asChild:{type:Boolean},as:{default:"button"}},setup(n){const t=n;y();const e=M(),s=c(()=>e.page.value===e.pageCount.value||e.disabled.value);return(o,a)=>(d(),g(l(C),I(t,{"aria-label":"Next Page",type:o.as==="button"?"button":void 0,disabled:s.value,onClick:a[0]||(a[0]=i=>!s.value&&l(e).onPageChange(l(e).page.value+1))}),{default:u(()=>[v(o.$slots,"default",{},()=>[a[1]||(a[1]=w("Next page"))])]),_:3},16,["type","disabled"]))}}),Z=m({__name:"PaginationPrev",props:{asChild:{type:Boolean},as:{default:"button"}},setup(n){const t=n;y();const e=M(),s=c(()=>e.page.value===1||e.disabled.value);return(o,a)=>(d(),g(l(C),I(t,{"aria-label":"Previous Page",type:o.as==="button"?"button":void 0,disabled:s.value,onClick:a[0]||(a[0]=i=>!s.value&&l(e).onPageChange(l(e).page.value-1))}),{default:u(()=>[v(o.$slots,"default",{},()=>[a[1]||(a[1]=w("Prev page"))])]),_:3},16,["type","disabled"]))}}),ee=m({__name:"PaginationEllipsis",props:{asChild:{type:Boolean},as:{},class:{}},setup(n){const t=n,e=c(()=>{const{class:s,...o}=t;return o});return(s,o)=>{const a=S;return d(),g(l(J),I(e.value,{class:l(V)("w-9 h-9 flex items-center justify-center",t.class)}),{default:u(()=>[v(s.$slots,"default",{},()=>[f(a,{name:"lucide:more-horizontal"})])]),_:3},16,["class"])}}}),ae=m({__name:"PaginationNext",props:{asChild:{type:Boolean,default:!0},as:{},class:{}},setup(n){const t=n,e=c(()=>{const{class:a,...i}=t;return i}),{locale:s}=q(),o=c(()=>s.value==="ar");return(a,i)=>{const r=S;return d(),g(l(Y),N(L(e.value)),{default:u(()=>[f(l(j),{class:E(l(V)("w-10 h-10 p-0",t.class)),variant:"white"},{default:u(()=>[v(a.$slots,"default",{},()=>[f(r,{name:"lucide:chevron-right",class:E(["h-4 w-4",{"rotate-180":o.value}])},null,8,["class"])])]),_:3},8,["class"])]),_:3},16)}}}),te=m({__name:"PaginationPrev",props:{asChild:{type:Boolean,default:!0},as:{},class:{}},setup(n){const t=n,e=c(()=>{const{class:a,...i}=t;return i}),{locale:s}=q(),o=c(()=>s.value==="ar");return(a,i)=>{const r=S;return d(),g(l(Z),N(L(e.value)),{default:u(()=>[f(l(j),{class:E(l(V)("w-10 h-10 p-0",t.class)),variant:"white"},{default:u(()=>[v(a.$slots,"default",{},()=>[f(r,{name:"lucide:chevron-left",class:E(["h-4 w-4",{"rotate-180":o.value}])},null,8,["class"])])]),_:3},8,["class"])]),_:3},16)}}}),se={class:"flex justify-center items-center w-full"},de=m({__name:"Paginate",props:{itemPerPage:{},total:{},defaultPage:{},siblingCount:{},showEdges:{type:Boolean}},emits:["update:page"],setup(n,{emit:t}){const e=t;return(s,o)=>{const a=te,i=j,r=X,_=ee,p=ae,h=Q,$=H;return d(),k("div",se,[f($,{"items-per-page":s.itemPerPage,total:s.total,"sibling-count":s.siblingCount,"show-edges":s.showEdges,"default-page":s.defaultPage,"onUpdate:page":o[0]||(o[0]=R=>e("update:page",R))},{default:u(({page:R})=>[f(h,{class:"flex items-center gap-1"},{default:u(({items:b})=>[f(a),(d(!0),k(F,null,D(b,(x,z)=>(d(),k(F,null,[x.type==="page"?(d(),g(r,{key:z,value:x.value,"as-child":""},{default:u(()=>[f(i,{class:"w-9 h-9",variant:x.value===R?"default":"outline-secondary"},{default:u(()=>[w(T(x.value),1)]),_:2},1032,["variant"])]),_:2},1032,["value"])):(d(),g(_,{key:x.type,index:z},null,8,["index"]))],64))),256)),f(p)]),_:2},1024)]),_:1},8,["items-per-page","total","sibling-count","show-edges","default-page"])])}}});export{de as _};
