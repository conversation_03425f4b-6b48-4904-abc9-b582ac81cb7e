import{_ as d}from"./Dr0cUkmi.js";import{_ as u}from"./CCzc7fMa.js";import{i as c,l as p,r as f,n as g,d as y,a as v,c as w,p as _,q as s,o as m}from"./C3gyeM1K.js";const x={class:"flex gap-4 xs:w-full sm:w-1/2 md:max-w-md w-full"},C=c({__name:"gallery",props:{product:{},loading:{type:Boolean}},setup(t){const n=p(()=>{var a,e,o,r,i;return((o=(e=(a=t.product)==null?void 0:a.variance)==null?void 0:e.media)==null?void 0:o.gallery)||((i=(r=t.product)==null?void 0:r.media)==null?void 0:i.gallery)}),l=f(!1);return g(()=>l.value,a=>{document.body.style.overflowY=a?"hidden":"auto"},{immediate:!0}),(a,e)=>(m(),y("div",x,[v(d,{images:s(n),loading:a.loading,"onSet:preview":e[0]||(e[0]=o=>l.value=!!o)},null,8,["images","loading"]),s(l)?(m(),w(u,{key:0,style:{"z-index":"99999999"},images:s(n),"onSet:preview":e[1]||(e[1]=o=>l.value=!!o)},null,8,["images"])):_("",!0)]))}});export{C as _};
