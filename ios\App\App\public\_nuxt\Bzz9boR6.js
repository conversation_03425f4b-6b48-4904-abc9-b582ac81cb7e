import{m as O,L as C,M as L,N as P,n as M,H as w,l as S,G as p,D as A,q as T}from"./C3gyeM1K.js";function h(e){return L()?(P(e),!0):!1}const W=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const N=e=>e!=null,k=Object.prototype.toString,D=e=>k.call(e)==="[object Object]",b=()=>{};function v(e){return Array.isArray(e)?e:[e]}function G(e){return w()}function R(e,t=!0,o){G()?O(e,o):t?e():C(e)}function B(e,t,o){return M(e,t,{...o,immediate:!0})}const I=W?window:void 0;function g(e){var t;const o=p(e);return(t=o==null?void 0:o.$el)!=null?t:o}function V(...e){const t=[],o=()=>{t.forEach(n=>n()),t.length=0},u=(n,r,i,s)=>(n.addEventListener(r,i,s),()=>n.removeEventListener(r,i,s)),c=S(()=>{const n=v(p(e[0])).filter(r=>r!=null);return n.every(r=>typeof r!="string")?n:void 0}),d=B(()=>{var n,r;return[(r=(n=c.value)==null?void 0:n.map(i=>g(i)))!=null?r:[I].filter(i=>i!=null),v(p(c.value?e[1]:e[0])),v(T(c.value?e[2]:e[1])),p(c.value?e[3]:e[2])]},([n,r,i,s])=>{if(o(),!(n!=null&&n.length)||!(r!=null&&r.length)||!(i!=null&&i.length))return;const a=D(s)?{...s}:s;t.push(...n.flatMap(y=>r.flatMap(m=>i.map(f=>u(y,m,f,a)))))},{flush:"post"}),l=()=>{d(),o()};return h(o),l}function q(){const e=A(!1),t=w();return t&&O(()=>{e.value=!0},t),e}function H(e){const t=q();return S(()=>(t.value,!!e()))}function $(e,t,o={}){const{root:u,rootMargin:c="0px",threshold:d=0,window:l=I,immediate:n=!0}=o,r=H(()=>l&&"IntersectionObserver"in l),i=S(()=>{const f=p(e);return v(f).map(g).filter(N)});let s=b;const a=A(n),y=r.value?M(()=>[i.value,g(u),a.value],([f,j])=>{if(s(),!a.value||!f.length)return;const x=new IntersectionObserver(t,{root:g(j),rootMargin:c,threshold:d});f.forEach(E=>E&&x.observe(E)),s=()=>{x.disconnect(),s=b}},{immediate:n,flush:"post"}):b,m=()=>{s(),y(),a.value=!1};return h(m),{isSupported:r,isActive:a,pause(){s(),a.value=!1},resume(){a.value=!0},stop:m}}function z(e){let t;return new Promise(o=>{t=$(e,u=>{for(const c of u)c.isIntersecting&&o(!0)},{rootMargin:"30px 0px 0px 0px",threshold:0}),h(()=>o(!1))}).finally(()=>{t.stop()})}function J(e){const{el:t,trigger:o}=e,u=(Array.isArray(e.trigger)?e.trigger:[e.trigger]).filter(Boolean);if(!o||u.includes("immediate")||u.includes("onNuxtReady"))return"onNuxtReady";if(u.some(l=>["visibility","visible"].includes(l)))return t?z(t):new Promise(()=>{});const c={},d=new Promise(l=>{const n=typeof t<"u"?t:document.body,r=V(n,u,()=>{r(),l(!0)},{once:!0,passive:!0});R(()=>{M(n,i=>{i&&u.forEach(s=>{i.dataset[`script_${s}`]&&(r(),l(!0))})},{immediate:!0})}),h(()=>l(!1))});return Object.assign(d,{ssrAttrs:c})}export{J as u};
