const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as r,c as m,w as a,q as l,o as f,a as t,e,g as d,t as n,ag as p,ah as x}from"./C3gyeM1K.js";import{_ as u}from"./BoqtTPyX.js";import{_ as g}from"./DAA1LH-g.js";import{_ as y}from"./JNWsvxxg.js";const h=p(()=>x(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(s=>s.default||s)),w={class:"flex gap-2 items-center justify-center xs:flex-row sm:flex-col-reverse lg:flex-row"},v={class:"flex flex-col gap-2 flex-nowrap"},$={class:"flex items-center"},k={class:"text-primary-600 text-lg font-semibold text-nowrap ms-1 md:font-md"},B={class:"text-gray-600 text-2xl font-bold md:font-lg"},b={class:"flex justify-center items-center mt-5"},V=r({__name:"delivery",setup(s){return(o,j)=>{const _=d,i=h,c=u;return f(),m(l(g),{class:"h-full py-6 bg-sky-50"},{default:a(()=>[t(l(y),{class:"flex flex-col gap-4 justify-center"},{default:a(()=>[e("div",w,[e("div",v,[e("div",$,[t(_,{name:"lucide:truck",width:"30px",class:"text-primary-600"}),e("span",k,n(o.$t("home.widget-delivery")),1)]),e("span",B,n(o.$t("home.widget-delivery-time")),1)]),t(i,{src:"/images/fast-delivery.png",class:"w-full h-auto lg:max-w-28 md:max-w-40 sm:max-w-28"})]),e("div",b,[t(c,{variant:"default",size:"lg"},{default:a(()=>[e("span",null,n(o.$t("home.widget-pay-now")),1)]),_:1})])]),_:1})]),_:1})}}});export{V as _};
