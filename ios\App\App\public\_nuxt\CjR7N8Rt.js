import{u as f}from"./BzSOFl6X.js";import{c as u}from"./jAU0Cazi.js";import{u as i}from"./C2RgRyPS.js";import{P as _}from"./ClItKS3j.js";import{i as l,c as n,o as p,w as c,s as d,a1 as m,q as a,l as b,W as C}from"./C3gyeM1K.js";const h=l({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(r){const o=r;return i(),(t,e)=>(p(),n(a(_),m(o,{onMousedown:e[0]||(e[0]=s=>{!s.defaultPrevented&&s.detail>1&&s.preventDefault()})}),{default:c(()=>[d(t.$slots,"default")]),_:3},16))}}),w=l({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(r){const o=r,t=b(()=>{const{class:e,...s}=o;return s});return(e,s)=>(p(),n(a(h),m(t.value,{class:a(u)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",o.class)}),{default:c(()=>[d(e.$slots,"default")]),_:3},16,["class"]))}}),F=l({__name:"FormLabel",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(r){const o=r,{error:t,formItemId:e}=f();return(s,x)=>(p(),n(a(w),{class:C(a(u)(a(t)&&"text-destructive",o.class)),for:a(e)},{default:c(()=>[d(s.$slots,"default")]),_:3},8,["class","for"]))}});export{F as _};
