import{i as _,c as g,o as d,w as p,s as h,a1 as x,q as e,r as D,E as Q,a as B,Z as V,ar as ee,L as te,v as T,x as F,K as W,l as k,k as Y,g as oe,W as ne,e as S,t as E,f as ae,d as K,p as se,a0 as re,$ as z}from"./C3gyeM1K.js";import{P as ie}from"./ClItKS3j.js";import{c as le,u as M}from"./C2RgRyPS.js";import{_ as ue}from"./CoTDbsFa.js";import{b as I,a as pe}from"./D3IGe8e3.js";import{a as q}from"./BBuxlZld.js";import{_ as de}from"./DYRsuAWe.js";import{i as L,p as ce,b as H,a as fe,f as me,g as ge,d as _e,e as ye}from"./D_nR533G.js";import{c as J}from"./jAU0Cazi.js";import{d as he,a as we,c as N,e as ve}from"./B12VHTgT.js";import{u as X}from"./DSkQD3L1.js";import{P as be}from"./CY42fsWK.js";const Ce=_({__name:"MenuGroup",props:{asChild:{type:Boolean},as:{}},setup(u){const a=u;return(o,f)=>(d(),g(e(ie),x({role:"group"},a),{default:p(()=>[h(o.$slots,"default")]),_:3},16))}}),[Z,Be]=le("MenuSub"),$e=_({__name:"MenuSub",props:{open:{type:Boolean,default:void 0}},emits:["update:open"],setup(u,{emit:a}){const o=u,t=q(o,"open",a,{defaultValue:!1,passive:o.open===void 0}),n=L(),s=D(),l=D();return Q(m=>{(n==null?void 0:n.open.value)===!1&&(t.value=!1),m(()=>t.value=!1)}),ce({open:t,onOpenChange:m=>{t.value=m},content:l,onContentChange:m=>{l.value=m}}),Be({triggerId:"",contentId:"",trigger:s,onTriggerChange:m=>{s.value=m}}),(m,$)=>(d(),g(e(de),null,{default:p(()=>[h(m.$slots,"default")]),_:3}))}}),Pe=_({__name:"MenuSubContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},sideOffset:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean,default:!0},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(u,{emit:a}){const t=I(u,a),n=L(),s=H(),l=Z(),{forwardRef:m,currentElement:$}=M();return l.contentId||(l.contentId=X(void 0,"reka-menu-sub-content")),(P,i)=>(d(),g(e(be),{present:P.forceMount||e(n).open.value},{default:p(()=>[B(fe,x(e(t),{id:e(l).contentId,ref:e(m),"aria-labelledby":e(l).triggerId,align:"start",side:e(s).dir.value==="rtl"?"left":"right","disable-outside-pointer-events":!1,"disable-outside-scroll":!1,"trap-focus":!1,onOpenAutoFocus:i[0]||(i[0]=V(r=>{var c;e(s).isUsingKeyboardRef.value&&((c=e($))==null||c.focus())},["prevent"])),onCloseAutoFocus:i[1]||(i[1]=V(()=>{},["prevent"])),onFocusOutside:i[2]||(i[2]=r=>{r.defaultPrevented||r.target!==e(l).trigger.value&&e(n).onOpenChange(!1)}),onEscapeKeyDown:i[3]||(i[3]=r=>{e(s).onClose(),r.preventDefault()}),onKeydown:i[4]||(i[4]=r=>{var v,b;const c=(v=r.currentTarget)==null?void 0:v.contains(r.target),y=e(he)[e(s).dir.value].includes(r.key);c&&y&&(e(n).onOpenChange(!1),(b=e(l).trigger.value)==null||b.focus(),r.preventDefault())})}),{default:p(()=>[h(P.$slots,"default")]),_:3},16,["id","aria-labelledby","side"])]),_:3},8,["present"]))}}),xe=_({__name:"MenuSubTrigger",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(u){const a=u,o=L(),f=H(),t=Z(),n=me(),s=D(null);t.triggerId||(t.triggerId=X(void 0,"reka-menu-sub-trigger"));function l(){s.value&&window.clearTimeout(s.value),s.value=null}ee(()=>{l()});function m(i){!N(i)||n.onItemEnter(i)||!a.disabled&&!o.open.value&&!s.value&&(n.onPointerGraceIntentChange(null),s.value=window.setTimeout(()=>{o.onOpenChange(!0),l()},100))}async function $(i){var c,y;if(!N(i))return;l();const r=(c=o.content.value)==null?void 0:c.getBoundingClientRect();if(r!=null&&r.width){const v=(y=o.content.value)==null?void 0:y.dataset.side,b=v==="right",w=b?-5:5,C=r[b?"left":"right"],O=r[b?"right":"left"];n.onPointerGraceIntentChange({area:[{x:i.clientX+w,y:i.clientY},{x:C,y:r.top},{x:O,y:r.top},{x:O,y:r.bottom},{x:C,y:r.bottom}],side:v}),window.clearTimeout(n.pointerGraceTimerRef.value),n.pointerGraceTimerRef.value=window.setTimeout(()=>n.onPointerGraceIntentChange(null),300)}else{if(n.onTriggerLeave(i))return;n.onPointerGraceIntentChange(null)}}async function P(i){var c;const r=n.searchRef.value!=="";a.disabled||r&&i.key===" "||ve[f.dir.value].includes(i.key)&&(o.onOpenChange(!0),await te(),(c=o.content.value)==null||c.focus(),i.preventDefault())}return(i,r)=>(d(),g(_e,{"as-child":""},{default:p(()=>[B(ge,x(a,{id:e(t).triggerId,ref:c=>{var y;(y=e(t))==null||y.onTriggerChange(c==null?void 0:c.$el)},"aria-haspopup":"menu","aria-expanded":e(o).open.value,"aria-controls":e(t).contentId,"data-state":e(we)(e(o).open.value),onClick:r[0]||(r[0]=async c=>{a.disabled||c.defaultPrevented||(c.currentTarget.focus(),e(o).open.value||e(o).onOpenChange(!0))}),onPointermove:m,onPointerleave:$,onKeydown:P}),{default:p(()=>[h(i.$slots,"default")]),_:3},16,["id","aria-expanded","aria-controls","data-state"])]),_:3}))}}),Me=_({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(u){const a=u;return M(),(o,f)=>(d(),g(e(Ce),T(F(a)),{default:p(()=>[h(o.$slots,"default")]),_:3},16))}}),Oe=_({__name:"DropdownMenuSub",props:{defaultOpen:{type:Boolean},open:{type:Boolean,default:void 0}},emits:["update:open"],setup(u,{emit:a}){const o=u,t=q(o,"open",a,{passive:o.open===void 0,defaultValue:o.defaultOpen??!1});return M(),(n,s)=>(d(),g(e($e),{open:e(t),"onUpdate:open":s[0]||(s[0]=l=>W(t)?t.value=l:null)},{default:p(()=>[h(n.$slots,"default",{open:e(t)})]),_:3},8,["open"]))}}),Se=_({__name:"DropdownMenuSubContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},sideOffset:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(u,{emit:a}){const t=I(u,a);return M(),(n,s)=>(d(),g(e(Pe),x(e(t),{style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:p(()=>[h(n.$slots,"default")]),_:3},16))}}),De=_({__name:"DropdownMenuSubTrigger",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(u){const a=u;return M(),(o,f)=>(d(),g(e(xe),T(F(a)),{default:p(()=>[h(o.$slots,"default")]),_:3},16))}}),ke=_({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(u){const a=u;return(o,f)=>(d(),g(e(Me),T(F(a)),{default:p(()=>[h(o.$slots,"default")]),_:3},16))}}),Te=_({__name:"DropdownMenuSub",props:{defaultOpen:{type:Boolean},open:{type:Boolean}},emits:["update:open"],setup(u,{emit:a}){const t=I(u,a);return(n,s)=>(d(),g(e(Oe),T(F(e(t))),{default:p(()=>[h(n.$slots,"default")]),_:3},16))}}),Fe=_({__name:"DropdownMenuSubContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},sideOffset:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(u,{emit:a}){const o=u,f=a,t=k(()=>{const{class:s,...l}=o;return l}),n=I(t,f);return(s,l)=>(d(),g(e(Se),x(e(n),{class:e(J)("z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",o.class)}),{default:p(()=>[h(s.$slots,"default")]),_:3},16,["class"]))}}),Ie=_({__name:"DropdownMenuSubTrigger",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},setup(u){const a=u,o=k(()=>{const{class:s,...l}=a;return l}),f=pe(o),{locale:t}=Y(),n=k(()=>t.value==="ar");return(s,l)=>{const m=oe;return d(),g(e(De),x(e(f),{class:e(J)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a.class)}),{default:p(()=>[h(s.$slots,"default"),B(m,{name:"lucide:chevron-right",class:ne(["ml-auto h-4 w-4",{"rotate-180":n.value}])},null,8,["class"])]),_:3},16,["class"])}}}),Ee={class:"cursor-default select-none rounded-sm outline-none focus:bg-accent data-[state=open]:bg-accent flex justify-between items-start text-sm font-normal text-gray-500 py-2 min-w-52"},Ke={key:0},Le=_({__name:"sub-menu",props:{item:{},path:{},close:{type:Function}},setup(u){const{locale:a}=Y(),o=k(()=>a.value==="ltr"),f=D(!1);return(t,n)=>{const s=ae,l=ue,m=Ie,$=Le,P=Fe,i=ye,r=Te,c=ke;return d(),g(c,{open:e(f),"onUpdate:open":n[1]||(n[1]=y=>W(f)?f.value=y:null)},{default:p(()=>{var y,v,b;return[(b=(v=(y=t.item)==null?void 0:y.meta)==null?void 0:v.children)!=null&&b.length?(d(),g(r,{key:1,class:"flex w-full"},{default:p(()=>[B(m,{class:"flex justify-between items-start text-sm font-normal text-gray-500 px-2 py-3 min-w-52"},{default:p(()=>{var w;return[B(s,{to:`${t.path}/${(w=t.item.meta)==null?void 0:w.slug}`,class:"flex w-full justify-between",onClick:n[0]||(n[0]=()=>t.close())},{default:p(()=>[S("span",null,E(t.item.text),1),e(o)?se("",!0):(d(),K("span",Ke))]),_:1},8,["to"])]}),_:1}),B(i,null,{default:p(()=>[B(P,null,{default:p(()=>{var w;return[(d(!0),K(z,null,re((w=t.item.meta)==null?void 0:w.children,(C,O)=>{var A,G,R;return d(),K(z,{key:`${O}-${JSON.stringify(t.item.text)}`},[(G=(A=C==null?void 0:C.meta)==null?void 0:A.children)!=null&&G.length?(d(),g($,{key:1,item:C,close:()=>t.close(),path:`${t.path}/${(R=t.item.meta)==null?void 0:R.slug}`},null,8,["item","close","path"])):(d(),g(l,{key:0,class:"cursor-pointer text-sm font-normal text-gray-500 px-2 py-3 min-w-52"},{default:p(()=>{var j,U;return[B(s,{to:`${t.path}/${(j=t.item.meta)==null?void 0:j.slug}/${(U=C.meta)==null?void 0:U.slug}`,class:"flex w-full justify-between"},{default:p(()=>[S("span",null,E(C.text),1)]),_:2},1032,["to"])]}),_:2},1024))],64)}),128))]}),_:1})]),_:1})]),_:1})):(d(),g(l,{key:0,class:"!py-0 min-w-52"},{default:p(()=>{var w;return[S("div",Ee,[B(s,{to:`${t.path}/${(w=t.item.meta)==null?void 0:w.slug}`,class:"flex w-full justify-between py-1"},{default:p(()=>[S("span",null,E(t.item.text),1)]),_:1},8,["to"])])]}),_:1}))]}),_:1},8,["open"])}}});export{Le as _};
