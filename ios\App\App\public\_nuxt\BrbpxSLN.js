const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as G,z as H,ai as J,k as Q,ak as X,j as Z,r as h,au as ee,l as L,n as te,ar as se,d,e as a,a as e,w as r,q as t,W as f,p as S,a4 as ae,a5 as oe,K as le,$ as q,a0 as z,o as m,ag as ne,g as ce,t as j,L as re,ah as ie,a2 as ue}from"./C3gyeM1K.js";import de from"./CuUhnQhf.js";import{_ as me}from"./BispfOyS.js";import{e as fe}from"./BBuxlZld.js";import{_ as v}from"./BoqtTPyX.js";import{Skeleton as w}from"./BaodNYLQ.js";import{q as _e}from"./CRC5xmnh.js";const pe=ne(()=>ie(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(g=>g.default||g)),he={class:"hidden w-full flex-col max-sm:flex pt-4"},ve={class:"flex w-full items-center justify-between px-4"},ge={class:"flex gap-2"},ye={class:"text-2xs font-semibold text-white leading-none"},xe={class:"flex flex-col w-full items-center"},be={class:"p-4 w-full"},we={class:"relative flex w-full gap-2 border rounded-lg border-gray-200 p-2"},ke=["placeholder"],Ce={key:0,class:"relative w-full z-40 top-4"},$e={class:"flex w-full flex-col min-h-dvh bg-white absolute left-0 overflow-auto -top-3"},Re={class:"flex flex-col justify-center items-center"},Le=["onClick"],Se={class:"flex p-2 rounded-lg col-span-1"},qe={class:"col-span-1 text-sm"},ze={class:"col-span-1 flex flex-col justify-center items-center"},De=G({__name:"mobile",emits:["open:drawer"],setup(g,{emit:I}){var k;const N=H(),A=J(),{locale:P}=Q(),T=I,V=X(),y=Z(),c=h((k=y.query)==null?void 0:k.q),x=h(!1),_=h(null),i=h(!1),B=ee("searchRef"),D=L(()=>P.value==="ar"),b=L(()=>A.count),E=_e(()=>O(),1e3),O=async()=>{var l;if(!((l=c.value)!=null&&l.trim()))return null;const{$api:s}=ue();return s(`search?q${c.value}&perPage=10`).then(o=>{_.value=(o==null?void 0:o.items)||[]}).catch(o=>{console.error(`ERROR Search, ${o}`)}).finally(()=>{x.value=!1})},F=()=>{i.value=!0,_.value=null,x.value=!0,E()},Y=()=>{var s;i.value=!!((s=_.value)!=null&&s.length)};te(()=>i.value,s=>{document.body.style.overflowY=s?"hidden":"auto"},{immediate:!0}),fe(B,()=>{i.value=!1});const M=s=>{c.value=s==null?void 0:s.name,i.value=!1,re(()=>{var l,o;return document.body.style.overflowY="auto",V.push({path:N("/search"),query:{q:c.value,...(l=y.query)!=null&&l.category?{category:(o=y.query)==null?void 0:o.category}:{}}})})};se(()=>{i.value=!1});const U=()=>{c.value="",i.value=!1,document.body.style.overflowY="auto"};return(s,l)=>{const o=pe,p=de,u=ce,K=me;return m(),d("div",he,[a("div",ve,[e(p,{to:"/"},{default:r(()=>[e(o,{class:"h-10",alt:"Action Mobile",src:"/images/logo.png"})]),_:1}),a("div",ge,[e(t(v),{variant:"icon",class:"bg-gray-100","as-child":""},{default:r(()=>[e(p,{to:"/notifications"},{default:r(()=>[e(u,{name:"lucide:bell"})]),_:1})]),_:1}),e(t(v),{variant:"icon",class:"bg-gray-100","as-child":""},{default:r(()=>[e(p,{to:"/"},{default:r(()=>[e(u,{name:"ui:compare"})]),_:1})]),_:1}),e(t(v),{variant:"icon",class:"bg-gray-100","as-child":""},{default:r(()=>[e(p,{to:"/my/profile"},{default:r(()=>[e(u,{name:"lucide:circle-user"})]),_:1})]),_:1}),e(t(v),{variant:"icon",class:f(["bg-gray-100 relative",{"bg-primary-300 !text-primary-600":t(b)}]),onClick:l[0]||(l[0]=n=>T("open:drawer","cart"))},{default:r(()=>[e(u,{name:"lucide:shopping-cart"}),e(K,{class:f(["absolute top-1.5 right-1.5 !bg-primary-600 px-1 pointer-events-none",{hidden:!t(b)}])},{default:r(()=>[a("span",ye,j(t(b)),1)]),_:1},8,["class"])]),_:1},8,["class"])])]),a("div",xe,[a("div",be,[a("div",we,[e(u,{name:"lucide:search",class:"text-gray-400 text-2xl my-auto"}),ae(a("input",{"onUpdate:modelValue":l[1]||(l[1]=n=>le(c)?c.value=n:null),name:"keywords",type:"text",class:"outline-none w-full h-full",placeholder:s.$t("header.search-placeholder"),onClick:Y,onInput:F},null,40,ke),[[oe,t(c)]]),t(c)?(m(),d("div",{key:0,role:"button",class:"flex flex-col absolute left-0 top-0 px-2 py-3 items-center justify-center",onClick:U},[e(u,{name:"lucide:circle-x",size:"20px",class:"bg-gray-400 rounded-full text-gray-900"})])):S("",!0)])]),t(i)?(m(),d("div",Ce,[a("div",$e,[t(x)?(m(!0),d(q,{key:0},z(Array(5),n=>(m(),d("div",{key:`search-loading-${n}`,class:f([{"bg-gray-50":n%2===0},"grid grid-cols-[auto_1fr_auto] w-full gap-2 my-2 px-4 hover:bg-primary-100"])},[e(t(w),{class:"w-10 h-12"}),e(t(w),{class:"w-full h-12"}),a("div",Re,[e(t(w),{class:"w-6 h-4"})])],2))),128)):(m(!0),d(q,{key:1},z(t(_),(n,W)=>{var C,$,R;return m(),d("div",{key:`search-${n.productId}`,role:"button",class:f([{"bg-gray-50":W%2===0},"grid grid-cols-[auto_1fr_auto] w-full items-center gap-2 px-4 hover:bg-primary-100"]),onClick:je=>M(n)},[a("div",Se,[e(o,{src:(R=($=(C=n.media)==null?void 0:C.cover)==null?void 0:$[0])==null?void 0:R.src,class:"object-cover w-12 h-12"},null,8,["src"])]),a("span",qe,j(n.name),1),a("div",ze,[e(u,{name:"lucide:chevron-right",size:"18px",class:f({"rotate-180":t(D)})},null,8,["class"])])],10,Le)}),128))])])):S("",!0)])])}}});export{De as _};
