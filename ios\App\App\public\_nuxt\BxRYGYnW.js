import{Skeleton as $}from"./BaodNYLQ.js";import{i as B,c as _,d as c,q as e,e as t,a,t as n,g as C,$ as F,p as l,w as S,o}from"./C3gyeM1K.js";import{_ as D}from"./BbzLkj_4.js";import{_ as N}from"./L0mwwrGq.js";const P={key:1,class:"flex bg-sky-50 flex-col p-4 rounded"},V={key:0,class:"flex w-full flex-col gap-2"},j={class:"flex w-full justify-between items-center"},A={class:"text-2xl font-bold"},I={class:"flex items-center gap-2"},O={class:"text-sm font-normal"},q={class:"flex items-start justify-between"},z={class:"flex flex-col"},E={class:"text-3xl text-primary-600 font-bold"},G={key:0,class:"flex gap-2 items-center text-md"},H={class:"text-gray-600"},J={class:"text-orange-400 font-medium"},K={key:0,class:"flex"},L={class:"bg-orange-400 text-white text-xs font-semibold px-4 py-1 rounded-full"},W=B({__name:"price-card",props:{product:{},loading:{type:Boolean}},setup(u){const{hasStock:m,hasOffer:p,discountPercent:f,discountAmount:x,priceFormatted:h,offerPriceFormatted:k,discountAmountFormatted:y,hasDiscount:g}=u.product;return(s,M)=>{const v=$,i=C,w=D,b=N;return s.loading?(o(),_(v,{key:0,class:"w-full h-full min-h-24 bg-sky-50 rounded"})):(o(),c("div",P,[e(m)?(o(),c(F,{key:1},[t("div",q,[t("div",z,[t("span",E,n(e(h)),1),e(x)?(o(),c("div",G,[t("span",H,n(e(k)),1),t("span",J,n(s.$t("product.discount-amount-title",{amount:e(y)})),1)])):l("",!0)]),e(g)?(o(),c("div",K,[t("span",L,n(s.$t("product.card-discount",{amount:e(f)})),1)])):l("",!0)]),a(b,null,{default:S(()=>{var d,r;return[e(p)?(o(),_(w,{key:0,stock:(r=(d=s.product)==null?void 0:d.variance)==null?void 0:r.stock},null,8,["stock"])):l("",!0)]}),_:1})],64)):(o(),c("div",V,[t("div",j,[t("div",A,n(s.$t("product.out-stock")),1),a(i,{name:"ui:out-stock",size:"25px"})]),t("div",I,[a(i,{name:"ui:bell-ringing"}),t("span",O,n(s.$t("product.let-me-know-when-available")),1)])]))]))}}});export{W as _};
