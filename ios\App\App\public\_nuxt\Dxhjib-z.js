import{_ as y,u as b}from"./BD9sJOps.js";import{i as v,X as w,l as n,d as C,a as s,q as a,w as _,$ as k,Y as B,o as $,e as t,t as r,g as H}from"./C3gyeM1K.js";import{_ as L}from"./DAA1LH-g.js";import{_ as A}from"./JNWsvxxg.js";import{_ as D}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const M={class:"font-bold text-2xl"},N={class:"text-lg text-gray-600"},S={class:"flex flex-col gap-4 bg-sky-50 p-4 rounded"},T={class:"flex items-center gap-2"},V={class:"flex rounded-full p-2 bg-sky-100"},j={class:"text-lg"},q={class:"space-y-5 text-sm text-gray-600 ps-6"},E=["innerHTML"],Q=v({__name:"wallet-usage",async setup(F){let o,i;const{data:c}=([o,i]=w(()=>B("pages/terms-of-wallet")),o=await o,i(),o),d=n(()=>{var e;return(e=c.value)==null?void 0:e.body}),l=n(()=>{var e;return(e=c.value)==null?void 0:e.name}),m=n(()=>{var e;return(e=c.value)==null?void 0:e.metaDescription}),p=n(()=>b().buildSinglePage(l.value));return(e,I)=>{const u=y,f=D,x=H,g=A,h=L;return $(),C(k,null,[s(u,{links:a(p),class:"!border-0 !shadow-none"},null,8,["links"]),s(h,{class:"flex flex-col w-full h-full my-6"},{default:_(()=>[s(f,{class:"text-center justify-center gap-4 py-12"},{default:_(()=>[t("h1",M,r(a(l)),1),t("h2",N,r(a(m)),1)]),_:1}),s(g,{class:"flex flex-col gap-6"},{default:_(()=>[t("div",S,[t("div",T,[t("div",V,[s(x,{name:"ui:privacy"})]),t("span",j,r(a(l)),1)]),t("div",q,[t("div",{class:"text-gray-600 whitespace-pre-line",innerHTML:a(d)},null,8,E)])])]),_:1})]),_:1})],64)}}});export{Q as default};
