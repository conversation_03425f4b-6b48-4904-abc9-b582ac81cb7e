import{o as i}from"./BBuxlZld.js";import{_ as a}from"./Dr0cUkmi.js";import{i as n,d as p,a as m,o as c}from"./C3gyeM1K.js";const l={class:"flex flex-col items-center justify-center fixed !w-screen h-screen bg-black bg-opacity-90 left-0 top-0 z-50 sm:px-6 xs:p-0"},g=n({__name:"fullscreen",props:{images:{default:()=>[]}},emits:["set:preview"],setup(f,{emit:r}){const s=r;return i("Escape",e=>{e.preventDefault(),s("set:preview",!1)}),(e,t)=>(c(),p("div",l,[m(a,{images:e.images,"is-full-screen":!0,"onSet:preview":t[0]||(t[0]=o=>s("set:preview",o))},null,8,["images"])]))}});export{g as _};
