const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as z,k as D,r as F,l as i,X as q,d as r,a as t,q as a,w as d,$ as f,Y as H,o as n,c as O,p as R,e as s,t as u,a0 as C,ag as T,ah as U}from"./C3gyeM1K.js";import{_ as X,u as Y}from"./BD9sJOps.js";import{_ as G}from"./DAA1LH-g.js";import{_ as J}from"./JNWsvxxg.js";import{_ as K}from"./ziqIZt9y.js";import{_ as M}from"./BsHCAE3W.js";import{Skeleton as Q}from"./BaodNYLQ.js";import{_ as W}from"./zZSnkz7W.js";import"./CuUhnQhf.js";import"./jAU0Cazi.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./ClItKS3j.js";import"./BoqtTPyX.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";const Z=T(()=>U(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(_=>_.default||_)),ee={class:"font-bold text-2xl"},te={class:"text-xl"},se={class:"flex flex-col gap-2 h-full items-center justify-between"},ae={class:"flex flex-grow justify-center items-center"},oe={class:"flex flex-col gap-2 h-full items-center justify-between"},ne={class:"flex flex-grow justify-center items-center"},re={class:"text-base font-semibold"},le={class:"flex justify-center items-center w-full"},Ce=z({__name:"brands",async setup(_){let l,g;const{t:j}=D(),m=F(1),B=i(()=>Y().buildSinglePage(j("brands.title"))),{data:h,error:b,status:P}=([l,g]=q(()=>H("/brands",{params:{perPage:25,page:m},watch:[m]})),l=await l,g(),l);b.value&&console.log(`Error on fetching brands: ${b.value}`);const x=i(()=>h.value.items),p=i(()=>h.value.pagination),A=i(()=>P.value!=="success");return(w,y)=>{const N=X,E=M,v=Q,L=Z,S=J,V=K,I=G;return n(),r(f,null,[t(N,{links:a(B),class:"!border-0 !shadow-none"},null,8,["links"]),t(I,{class:"flex flex-col w-full h-full gap-2 my-6"},{default:d(()=>{var k;return[t(E,{class:"text-center justify-center gap-4 rounded-lg"},{default:d(()=>[s("h1",ee,u(w.$t("brands.title")),1),s("h2",te,u(w.$t("brands.sub-title")),1)]),_:1}),t(S,{class:"grid grid-cols-5 max-sm:grid-cols-2 gap-6 py-12"},{default:d(()=>[a(A)?(n(!0),r(f,{key:0},C(Array(25),(e,o)=>(n(),r("div",{key:`loading-brand-${o}`,class:"flex flex-col p-4 rounded-lg shadow gap-2 border"},[s("div",se,[s("div",ae,[t(v,{class:"h-16 w-28"})]),t(v,{class:"h-4 w-28"})])]))),128)):(n(!0),r(f,{key:1},C(a(x),(e,o)=>{var c,$;return n(),r("div",{key:o,class:"flex flex-col p-4 rounded-lg shadow gap-2 border"},[s("div",oe,[s("div",ne,[t(L,{src:($=(c=e==null?void 0:e.media)==null?void 0:c.logoName)==null?void 0:$.preview,alt:`${e.name} brand logo`,"object-fit":"cover","object-position":"center",height:"56",width:"100",format:"webp",loading:"eager"},null,8,["src","alt"])]),s("span",re,u(e.name),1)])])}),128))]),_:1}),(k=a(x))!=null&&k.length?(n(),O(V,{key:0},{default:d(()=>{var e,o;return[s("div",le,[t(W,{"items-per-page":(e=a(p))==null?void 0:e.perPage,total:(o=a(p))==null?void 0:o.lastPage,"sibling-count":1,"show-edges":!0,"default-page":a(p).page,"onUpdate:page":y[0]||(y[0]=c=>m.value=c)},null,8,["items-per-page","total","default-page"])])]}),_:1})):R("",!0)]}),_:1})],64)}}});export{Ce as default};
