import{Skeleton as j}from"./BaodNYLQ.js";import{i as q,X as z,l as _,r as D,d as n,a as o,w as f,$ as g,Y as E,o as l,e as s,q as a,a0 as k,W as M,t as i,Z as $,h as I,a2 as O,_ as P}from"./C3gyeM1K.js";import{_ as W}from"./JNWsvxxg.js";import{_ as X}from"./ziqIZt9y.js";import{_ as Y}from"./BoqtTPyX.js";import{u as Z}from"./BCJZ1_ur.js";import"./jAU0Cazi.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";const G={class:"grid grid-cols-2 w-full gap-6 max-md:grid-cols-1"},H=["disabled","onClick"],J={class:"flex gap-2 border-b p-2"},K={class:"font-bold text-base flex-grow"},Q={class:"font-bold"},R={class:"flex w-full items-center text-sm gap-4 px-4 pb-2"},U={class:"w-18 text-gray-600"},ee=q({__name:"shipment",props:{order:{}},emits:["set:flow"],async setup(d,{emit:A}){var v;let c,x;const{priceFormat:N}=Z(),h=A,{data:m,error:w,status:u}=([c,x]=z(()=>{var e;return E(`/orders/${(e=d.order)==null?void 0:e.orderId}/shipping-carriers`)}),c=await c,x(),c);w.value&&console.log("error on fetching shipping list",w.value);const B=_(()=>m==null?void 0:m.value),F=_(()=>(u==null?void 0:u.value)!=="success"),p=D(((v=d.order)==null?void 0:v.shippingCarrierId)||null),S=_(()=>{var e;return((e=d.order)==null?void 0:e.status)!=="draft"}),L=async()=>{const{$api:e}=O();e(`/orders/${d.order.orderId}/shipping-carriers`,{method:"POST",body:{shippingCarrierId:p.value}}).then(()=>{h("set:flow",3)})};return(e,t)=>{const b=j,V=W,C=Y,T=X;return l(),n(g,null,[o(V,{class:"flex-grow p-4"},{default:f(()=>[s("div",G,[a(F)?(l(!0),n(g,{key:0},k(Array(4),(r,y)=>(l(),n("div",{key:y,class:"flex w-full max-w-full border rounded-lg p-2 gap-2 flex-col"},[o(b,{class:"w-full h-7"}),o(b,{class:"w-full h-5"})]))),128)):(l(!0),n(g,{key:1},k(a(B),r=>(l(),n("button",{key:r.shippingCarrierId,disabled:a(S),class:M(["flex flex-col border rounded-lg gap-2 cursor-pointer text-start",{active:r.shippingCarrierId===a(p)}]),onClick:y=>p.value=r.shippingCarrierId},[s("div",J,[t[2]||(t[2]=s("div",{class:"check flex rounded-full w-5 h-5 border p-0.5"},[s("div",{class:"child flex w-full h-full rounded-full"})],-1)),s("div",K,i(r.label),1),s("div",Q,i(a(N)(r.price.value)),1)]),s("div",R,[s("span",U,i(e.$t("form.the-delivery"))+": ",1),t[3]||(t[3]=s("span",{class:"text-gray-700"}," N/A ",-1))])],10,H))),128))])]),_:1}),o(T,{class:"gap-4 justify-end"},{default:f(()=>[o(C,{variant:"outline",class:"sm:min-w-24 xs:min-w-1/2",onClick:t[0]||(t[0]=$(()=>h("set:flow",1),["prevent"]))},{default:f(()=>[I(i(e.$t("form.prev")),1)]),_:1}),o(C,{disabled:!a(p),class:"sm:min-w-24 xs:min-w-1/2",onClick:t[1]||(t[1]=$(()=>L(),["prevent"]))},{default:f(()=>[I(i(e.$t("form.next")),1)]),_:1},8,["disabled"])]),_:1})],64)}}}),pe=P(ee,[["__scopeId","data-v-75e840b3"]]);export{pe as default};
