import{_ as A,a as B,b as I}from"./BzSOFl6X.js";import{u as E,t as N,o as Z,s as C,F as L}from"./ZoTeemxs.js";import{_ as M}from"./CjR7N8Rt.js";import{_ as U}from"./CvMKnfiC.js";import{i as j,u as D,k as K,r as k,l as R,ab as W,L as G,c as H,w as t,o as J,e as l,a as e,h as _,t as n,q as o,a1 as F,g as O,W as w}from"./C3gyeM1K.js";import{_ as Q}from"./BoqtTPyX.js";import{_ as X}from"./C0H2RQFE.js";const Y={class:"flex flex-col gap-4 px-4"},ee={class:"flex absolute top-7 end-4"},se={class:"flex absolute top-7 end-4"},oe={class:"flex flex-col gap-3 text-sm"},le={class:"font-semibold"},ae={class:"text-sm text-gray-500"},te={class:"flex items-center gap-2"},ne={class:"flex items-center gap-2"},re={class:"flex items-center gap-2"},ce={class:"flex items-center gap-2"},ie={class:"flex absolute top-7 end-4"},de={class:"flex w-full mt-2"},be=j({__name:"password",emits:["close:modal"],setup(ue,{emit:z}){const S=D(),{t:d}=K(),m=k(!1),p=k(!1),f=k(!1),r=E({validationSchema:N(Z({oldPassword:C().min(7,d("error.required")),password:C().min(7,d("error.required")).regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/,{message:d("error.password-strength")}),passwordConfirmation:C().min(7,d("error.required"))}).refine(s=>s.password===s.passwordConfirmation,{path:["passwordConfirmation"],message:d("error.passwords-not-match")})),initialValues:{oldPassword:"",password:"",passwordConfirmation:""}}),c=R(()=>{var a;if(!r.values)return;const s=(a=r.values)==null?void 0:a.password;return{"rule-1":/[a-z]/.test(s)&&/[A-Z]/.test(s),"rule-2":s.length>=8,"rule-3":/\d/.test(s),"rule-4":/[^a-zA-Z0-9]/.test(s)}}),V=z,P=r.handleSubmit(async s=>S.changePassword(s).then(()=>{W.success(d("form.password-changed-success")),G(()=>V("close:modal"))}));return(s,a)=>{const g=M,h=U,b=B,x=I,i=O,$=A,y=L,T=Q,q=X;return J(),H(q,{title:s.$t("form.change-password-title"),dismissible:!0,onClose:a[5]||(a[5]=u=>V("close:modal"))},{body:t(()=>[l("form",Y,[e(y,{name:"oldPassword"},{default:t(({componentField:u})=>[e($,{class:"relative"},{default:t(()=>[e(g,{class:"font-bold"},{default:t(()=>[_(n(s.$t("form.current-password"))+"* ",1)]),_:1}),e(b,null,{default:t(()=>[e(h,F({type:o(m)?"text":"password",placeholder:s.$t("form.current-password"),autocomplete:"current-password"},u),null,16,["type","placeholder"])]),_:2},1024),e(x),l("div",ee,[l("button",{class:"p-1",onClick:a[0]||(a[0]=()=>m.value=!o(m))},[e(i,{name:o(m)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1}),e(y,{name:"password"},{default:t(({componentField:u})=>[e($,{class:"w-full relative"},{default:t(()=>[e(g,{class:"font-bold"},{default:t(()=>[_(n(s.$t("form.new-password"))+"* ",1)]),_:1}),e(b,null,{default:t(()=>[e(h,F({modelValue:o(r).values.password,"onUpdate:modelValue":a[1]||(a[1]=v=>o(r).values.password=v),type:o(p)?"text":"password",autocomplete:"new-password",placeholder:s.$t("form.new-password")},u),null,16,["modelValue","type","placeholder"])]),_:2},1024),e(x),l("div",se,[l("button",{class:"p-1",onClick:a[2]||(a[2]=()=>p.value=!o(p))},[e(i,{name:o(p)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1}),l("div",oe,[l("span",le,n(s.$t("form.password-hint-title")),1),l("ul",ae,[l("li",te,[e(i,{class:w({"text-green-600":o(c)["rule-1"]}),name:o(c)["rule-1"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),l("span",null,n(s.$t("password.rule-1")),1)]),l("li",ne,[e(i,{class:w({"text-green-600":o(c)["rule-2"]}),name:o(c)["rule-2"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),l("span",null,n(s.$t("password.rule-2")),1)]),l("li",re,[e(i,{class:w({"text-green-600":o(c)["rule-3"]}),name:o(c)["rule-3"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),l("span",null,n(s.$t("password.rule-3")),1)]),l("li",ce,[e(i,{class:w({"text-green-600":o(c)["rule-4"]}),name:o(c)["rule-4"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),l("span",null,n(s.$t("password.rule-4")),1)])])]),e(y,{name:"passwordConfirmation"},{default:t(({componentField:u})=>[e($,{class:"relative"},{default:t(()=>[e(g,{class:"font-bold"},{default:t(()=>[_(n(s.$t("form.confirm-new-password"))+"* ",1)]),_:1}),e(b,null,{default:t(()=>[e(h,F({modelValue:o(r).values.passwordConfirmation,"onUpdate:modelValue":a[3]||(a[3]=v=>o(r).values.passwordConfirmation=v),autocomplete:"new-password",type:o(f)?"text":"password",placeholder:s.$t("form.confirm-new-password")},u),null,16,["modelValue","type","placeholder"])]),_:2},1024),e(x),l("div",ie,[l("button",{class:"p-1",onClick:a[4]||(a[4]=()=>f.value=!o(f))},[e(i,{name:o(f)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1})])]),footer:t(()=>[l("div",de,[e(T,{class:"w-full",disabled:o(r).isSubmitting.value,onClick:o(P)},{default:t(()=>[_(n(s.$t("form.change-password-title")),1)]),_:1},8,["disabled","onClick"])])]),_:1},8,["title"])}}});export{be as _};
