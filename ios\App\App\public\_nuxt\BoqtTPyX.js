import{_ as l}from"./D8XAHdeJ.js";import{c as d}from"./D1vxl8R8.js";import{c}from"./jAU0Cazi.js";import{P as u}from"./ClItKS3j.js";import{i as g,c as t,w as p,W as a,q as r,o as i,e as v,p as m,s as f}from"./C3gyeM1K.js";const k=g({__name:"Button",props:{variant:{},size:{},class:{},loading:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"button"}},setup(o){const s=o;return(e,h)=>{const n=l;return i(),t(r(u),{as:e.as,"as-child":e.asChild,class:a(r(c)(r(b)({variant:e.variant,size:e.size}),s.class,{relative:e.loading})),variant:e.variant,disabled:e.loading||void 0},{default:p(()=>[v("span",{class:a({invisible:e.loading})},[f(e.$slots,"default")],2),e.loading?(i(),t(n,{key:0,class:"absolute inset-0 m-auto w-5 h-5",color:e.variant==="default"?"#fff":"#9C3D88"},null,8,["color"])):m("",!0)]),_:3},8,["as","as-child","class","variant","disabled"])}}}),b=d("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-5 [&_svg]:shrink-0",{variants:{variant:{icon:"bg-gray-100 text-gray-700 aspect-square hover:text-gray-400 text-gray-500/80 box-shadow",default:"bg-primary-600 text-white hover:bg-primary-200",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-primary-600 text-primary-600 hover:bg-primary-200 hover:text-primary-500 active:text-primary-700 disabled:text-opacity-60","outline-secondary":"border border-gray-300 text-gray-600 hover:bg-gray-100 hover:text-gray-600 active:text-gray-200 disabled:text-opacity-60",secondary:"bg-gray-300 text-gray-600 hover:bg-gray-200",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary-100 underline-offset-4 hover:underline",white:"text-black bg-white  hover:shadow-lg",text:"text-primary-500 hover:bg-primary-100 cursor-pointer",danger:"text-white bg-rose-500 cursor-pointer"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 text-xs",lg:"h-11 rounded-md px-8",icon:"h-10 w-10",circle:"rounded-full"}},defaultVariants:{variant:"default",size:"default"}});export{k as _};
