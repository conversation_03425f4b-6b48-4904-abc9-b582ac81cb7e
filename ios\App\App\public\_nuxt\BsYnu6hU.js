import{aF as C,aG as k,H as R,m as $,K as A,n as E,N as w,r as H,ay as L,a2 as N,S as T,aH as M,ax as W}from"./C3gyeM1K.js";import{i as j}from"./CvhKjyCU.js";function F(e={}){const c=[];let f=-1;const r=(t=!1)=>({get(a,l,u){if(!t){const d=Reflect.get(a,l,u);if(typeof d<"u")return d;f++,c[f]=[]}return c[f].push({type:"get",key:l}),new Proxy(()=>{},r(!0))},apply(a,l,u){c[f].push({type:"apply",key:"",args:u})}});return{proxy:new Proxy(e||{},r()),stack:c}}function I(e){const c={get(f,r,t){const a=Reflect.get(f,r,t);return typeof a=="object"?new Proxy(a,c):a},apply(f,r,t){Reflect.apply(f,r,t)}};return new Proxy(e,c)}function K(e,c){c.forEach(f=>{let r=e,t=e;f.forEach(({type:a,key:l,args:u})=>{a==="get"?(t=r,r=r[l]):a==="apply"&&(r=r.call(t,...u))})})}function O(e){return e.key||e.src||(typeof e.innerHTML=="string"?e.innerHTML:"")}const q=["preconnect","dns-prefetch"];function D(e,c,f){var x,P,S;const r=typeof c=="string"?{src:c}:c,t=f||{},a=O(r),l=(x=e._scripts)==null?void 0:x[a];if(l)return l.setupTriggerHandler(t.trigger),l;(P=t.beforeInit)==null||P.call(t);const u=s=>{n.status=s,e.hooks.callHook("script:updated",v)};C.forEach(s=>{const i=s,o=typeof r[i]=="function"?r[i].bind(t.eventContext):null;r[i]=g=>{u(s==="onload"?"loaded":s==="onerror"?"error":"loading"),o==null||o(g)}});const d={loaded:[],error:[]},y=new Set,m=(s,i,o)=>{if(!e.ssr){if(o!=null&&o.key){const g=`${o==null?void 0:o.key}:${o.key}`;if(y.has(g))return;y.add(g)}if(d[s]){const g=d[s].push(i);return()=>{var p;return(p=d[s])==null?void 0:p.splice(g-1,1)}}return i(n.instance),()=>{}}},_=new Promise(s=>{if(e.ssr)return;const i=g=>requestAnimationFrame(()=>s(g)),o=e.hooks.hook("script:updated",({script:g})=>{const p=g.status;if(g.id===a&&(p==="loaded"||p==="error")){if(p==="loaded")if(typeof t.use=="function"){const h=t.use();h&&i(h)}else i({});else p==="error"&&s(!1);o()}})}),n={_loadPromise:_,instance:!e.ssr&&((S=t==null?void 0:t.use)==null?void 0:S.call(t))||null,proxy:null,id:a,status:"awaitingLoad",remove(){var s,i,o;return(s=n._triggerAbortController)==null||s.abort(),n._triggerPromises=[],(i=n._warmupEl)==null||i.dispose(),n.entry?(n.entry.dispose(),n.entry=void 0,u("removed"),(o=e._scripts)==null||delete o[a],!0):!1},warmup(s){const{src:i}=r,o=!i.startsWith("/")||i.startsWith("//"),g=s&&q.includes(s);let p=i;if(!s||g&&!o)return;if(g){const b=new URL(i);p=`${b.protocol}//${b.host}`}const h={href:p,rel:s,crossorigin:typeof r.crossorigin<"u"?r.crossorigin:o?"anonymous":void 0,referrerpolicy:typeof r.referrerpolicy<"u"?r.referrerpolicy:o?"no-referrer":void 0,fetchpriority:typeof r.fetchpriority<"u"?r.fetchpriority:"low",integrity:r.integrity,as:s==="preload"?"script":void 0};return n._warmupEl=e.push({link:[h]},{head:e,tagPriority:"high"}),n._warmupEl},load(s){var i;if((i=n._triggerAbortController)==null||i.abort(),n._triggerPromises=[],!n.entry){u("loading");const o={defer:!0,fetchpriority:"low"};r.src&&(r.src.startsWith("http")||r.src.startsWith("//"))&&(o.crossorigin="anonymous",o.referrerpolicy="no-referrer"),n.entry=e.push({script:[{...o,...r}]},t)}return s&&m("loaded",s),_},onLoaded(s,i){return m("loaded",s,i)},onError(s,i){return m("error",s,i)},setupTriggerHandler(s){if(n.status==="awaitingLoad")if((typeof s>"u"||s==="client")&&!e.ssr||s==="server")n.load();else if(s instanceof Promise){if(e.ssr)return;n._triggerAbortController||(n._triggerAbortController=new AbortController,n._triggerAbortPromise=new Promise(o=>{n._triggerAbortController.signal.addEventListener("abort",()=>{n._triggerAbortController=null,o()})})),n._triggerPromises=n._triggerPromises||[];const i=n._triggerPromises.push(Promise.race([s.then(o=>typeof o>"u"||o?n.load:void 0),n._triggerAbortPromise]).catch(()=>{}).then(o=>{o==null||o()}).finally(()=>{var o;(o=n._triggerPromises)==null||o.splice(i,1)}))}else typeof s=="function"&&s(n.load)},_cbs:d};_.then(s=>{var i,o;s!==!1?(n.instance=s,(i=d.loaded)==null||i.forEach(g=>g(s)),d.loaded=null):((o=d.error)==null||o.forEach(g=>g()),d.error=null)});const v={script:n};if(n.setupTriggerHandler(t.trigger),t.use){const{proxy:s,stack:i}=F(e.ssr?{}:t.use()||{});n.proxy=s,n.onLoaded(o=>{K(o,i),n.proxy=I(o)})}return!t.warmupStrategy&&(typeof t.trigger>"u"||t.trigger==="client")&&(t.warmupStrategy="preload"),t.warmupStrategy&&n.warmup(t.warmupStrategy),e._scripts=Object.assign(e._scripts||{},{[a]:n}),n}function G(e,c){if(!c)return;const f=(r,t)=>{if(!e._cbs[r])return t(e.instance),()=>{};let a=e._cbs[r].push(t);const l=()=>{var u;a&&((u=e._cbs[r])==null||u.splice(a-1,1),a=null)};return w(l),l};e.onLoaded=r=>f("loaded",r),e.onError=r=>f("error",r),w(()=>{var r;(r=e._triggerAbortController)==null||r.abort()})}function U(e,c){const f=typeof e=="string"?{src:e}:e,r=c||{},t=(r==null?void 0:r.head)||k();r.head=t;const a=R();if(r.eventContext=a,a&&typeof r.trigger>"u")r.trigger=$;else if(A(r.trigger)){const u=r.trigger;let d;r.trigger=new Promise(y=>{d=E(u,m=>{m&&y(!0)},{immediate:!0}),w(()=>y(!1),!0)}).then(y=>(d==null||d(),y))}t._scriptStatusWatcher=t._scriptStatusWatcher||t.hooks.hook("script:updated",({script:u})=>{u._statusRef.value=u.status});const l=D(t,f,r);return l._statusRef=l._statusRef||H(l.status),G(l,a),new Proxy(l,{get(u,d,y){return Reflect.get(u,d==="status"?"_statusRef":d,y)}})}function V(){return W().public["nuxt-scripts"]}function z(e){return e.key||e.src||(typeof e.innerHTML=="string"?e.innerHTML:"")}function Q(e,c){var l,u;e=typeof e=="string"?{src:e}:e,c=L(c,(l=V())==null?void 0:l.defaultScriptOptions);const f=String(z(e)),r=N();if(c.head=c.head||j(),!c.head)throw new Error("useScript() has been called without Nuxt context.");r.$scripts=r.$scripts||T({}),(u=r.$scripts)!=null&&u[f],(c.trigger==="onNuxtReady"||c.trigger==="client")&&(c.warmupStrategy||(c.warmupStrategy="preload"),c.trigger==="onNuxtReady"&&(c.trigger=M));const t=U(e,c),a=t.remove;return t.remove=()=>(r.$scripts[f]=void 0,a()),r.$scripts[f]=t,t}export{Q as u};
