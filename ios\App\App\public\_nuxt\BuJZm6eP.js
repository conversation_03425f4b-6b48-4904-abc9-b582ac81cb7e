import{_ as j}from"./UTzmdM4z.js";import{c as F}from"./jAU0Cazi.js";import{a as J,b as K}from"./D3IGe8e3.js";import{a as X,i as A,f as Y}from"./BBuxlZld.js";import{c as G,u as W}from"./C2RgRyPS.js";import{P as Q}from"./CY42fsWK.js";import{P as q}from"./ClItKS3j.js";import{u as U}from"./Bqx9KQ63.js";import{u as Z}from"./BRIKaOVL.js";import{u as ee}from"./DSkQD3L1.js";import{s as te,q as oe}from"./CRC5xmnh.js";import{i as N,r as M,T as ae,l as B,E as ne,c as $,o as b,w as T,a as E,q as t,s as L,n as O,a1 as R,d as D,k as ie,X as se,p as re,$ as I,a0 as z,Y as le}from"./C3gyeM1K.js";import{Skeleton as ue}from"./BaodNYLQ.js";const[de,ce]=G(["NavigationMenuRoot","NavigationMenuSub"],"NavigationMenuContext"),pe=N({__name:"NavigationMenuRoot",props:{modelValue:{default:void 0},defaultValue:{},dir:{},orientation:{default:"horizontal"},delayDuration:{default:200},skipDelayDuration:{default:300},disableClickTrigger:{type:Boolean,default:!1},disableHoverTrigger:{type:Boolean,default:!1},disablePointerLeaveClose:{type:Boolean},unmountOnHide:{type:Boolean,default:!0},asChild:{type:Boolean},as:{default:"nav"}},emits:["update:modelValue"],setup(h,{emit:l}){const u=h,o=X(u,"modelValue",l,{defaultValue:u.defaultValue??"",passive:u.modelValue===void 0}),c=M(""),{forwardRef:f,currentElement:k}=W(),_=M(),y=M(),v=M(),{getItems:a,CollectionSlot:V}=U({key:"NavigationMenu",isProvider:!0}),{delayDuration:P,skipDelayDuration:d,dir:n,disableClickTrigger:i,disableHoverTrigger:m,unmountOnHide:w}=ae(u),g=Z(n),C=te(!1,d),x=B(()=>o.value!==""||C.value?150:P.value),s=oe(e=>{typeof e=="string"&&(c.value=o.value,o.value=e)},x);return ne(()=>{if(!o.value)return;const e=a().map(r=>r.ref);v.value=e.find(r=>r.id.includes(o.value))}),ce({isRootMenu:!0,modelValue:o,previousValue:c,baseId:ee(void 0,"reka-navigation-menu"),disableClickTrigger:i,disableHoverTrigger:m,dir:g,unmountOnHide:w,orientation:u.orientation,rootNavigationMenu:k,indicatorTrack:_,activeTrigger:v,onIndicatorTrackChange:e=>{_.value=e},viewport:y,onViewportChange:e=>{y.value=e},onTriggerEnter:e=>{s(e)},onTriggerLeave:()=>{C.value=!0,s("")},onContentEnter:()=>{s()},onContentLeave:()=>{u.disablePointerLeaveClose||s("")},onItemSelect:e=>{c.value=o.value,o.value=e},onItemDismiss:()=>{c.value=o.value,o.value=""}}),(e,r)=>(b(),$(t(V),null,{default:T(()=>[E(t(q),{ref:t(f),"aria-label":"Main",as:e.as,"as-child":e.asChild,"data-orientation":e.orientation,dir:t(g),"data-reka-navigation-menu":""},{default:T(()=>[L(e.$slots,"default",{modelValue:t(o)})]),_:3},8,["as","as-child","data-orientation","dir"])]),_:3}))}});function fe(h){return h?"open":"closed"}function ve(h){return l=>l.pointerType==="mouse"?h(l):void 0}const me=N({inheritAttrs:!1,__name:"NavigationMenuViewport",props:{forceMount:{type:Boolean},align:{default:"center"},asChild:{type:Boolean},as:{}},setup(h){var P;const l=h,{forwardRef:u,currentElement:p}=W(),o=de(),{activeTrigger:c,rootNavigationMenu:f,modelValue:k}=o,_=M(),y=M(),v=B(()=>!!o.modelValue.value);O(p,()=>{o.onViewportChange(p.value)});const a=M();O([k,v],()=>{p.value&&requestAnimationFrame(()=>{var n;const d=(n=p.value)==null?void 0:n.querySelector("[data-state=open]");a.value=d})},{immediate:!0,flush:"post"});function V(){if(a.value&&c.value&&f.value){const d=document.documentElement.offsetWidth,n=document.documentElement.offsetHeight,i=f.value.getBoundingClientRect(),m=c.value.getBoundingClientRect(),{offsetWidth:w,offsetHeight:g}=a.value,C=m.left-i.left,x=m.top-i.top;let s=null,e=null;switch(l.align){case"start":s=C,e=x;break;case"end":s=C-w+m.width,e=x-g+m.height;break;default:s=C-w/2+m.width/2,e=x-g/2+m.height/2}const r=10;s+i.left<r&&(s=r-i.left);const H=s+i.left+w;H>d-r&&(s-=H-d+r,s<r-i.left&&(s=r-i.left)),e+i.top<r&&(e=r-i.top);const S=e+i.top+g;S>n-r&&(e-=S-n+r,e<r-i.top&&(e=r-i.top)),s=Math.round(s),e=Math.round(e),y.value={left:s,top:e}}}return A(a,()=>{a.value&&(_.value={width:a.value.offsetWidth,height:a.value.offsetHeight},V())}),A([(P=globalThis.document)==null?void 0:P.body,f],()=>{V()}),(d,n)=>(b(),$(t(Q),{present:d.forceMount||v.value,"force-mount":!t(o).unmountOnHide.value,onAfterLeave:n[2]||(n[2]=()=>{_.value=void 0,y.value=void 0})},{default:T(({present:i})=>{var m,w,g,C;return[E(t(q),R(d.$attrs,{ref:t(u),as:d.as,"as-child":d.asChild,"data-state":t(fe)(v.value),"data-orientation":t(o).orientation,style:{pointerEvents:!v.value&&t(o).isRootMenu?"none":void 0,"--reka-navigation-menu-viewport-width":_.value?`${(m=_.value)==null?void 0:m.width}px`:void 0,"--reka-navigation-menu-viewport-height":_.value?`${(w=_.value)==null?void 0:w.height}px`:void 0,"--reka-navigation-menu-viewport-left":y.value?`${(g=y.value)==null?void 0:g.left}px`:void 0,"--reka-navigation-menu-viewport-top":y.value?`${(C=y.value)==null?void 0:C.top}px`:void 0},hidden:!i,onPointerenter:n[0]||(n[0]=x=>t(o).onContentEnter(t(o).modelValue.value)),onPointerleave:n[1]||(n[1]=x=>t(ve)(()=>t(o).onContentLeave())(x))}),{default:T(()=>[L(d.$slots,"default")]),_:2},1040,["as","as-child","data-state","data-orientation","style","hidden"])]}),_:3},8,["present","force-mount"]))}}),ge={class:"absolute rtl:-end-1/2 ltr:-start-1/2 top-full flex justify-center"},he=N({__name:"NavigationMenuViewport",props:{forceMount:{type:Boolean},align:{},asChild:{type:Boolean},as:{},class:{}},setup(h){const l=h,u=B(()=>{const{class:o,...c}=l;return c}),p=J(u);return(o,c)=>(b(),D("div",ge,[E(t(me),R(t(p),{class:t(F)("origin-top-center relative mt-1.5 h-[--reka-navigation-menu-viewport-height] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 max-md:w-[--reka-navigation-menu-viewport-width]",l.class)}),null,16,["class"])]))}}),we=N({__name:"NavigationMenu",props:{modelValue:{},defaultValue:{},dir:{},orientation:{},delayDuration:{},skipDelayDuration:{},disableClickTrigger:{type:Boolean},disableHoverTrigger:{type:Boolean},disablePointerLeaveClose:{type:Boolean},unmountOnHide:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(h,{emit:l}){const u=h,p=l,o=B(()=>{const{class:f,...k}=u;return k}),c=K(o,p);return(f,k)=>(b(),$(t(pe),R(t(c),{class:t(F)("relative z-10 flex max-w-max flex-1 items-center justify-center",u.class)}),{default:T(()=>[L(f.$slots,"default"),E(he,{align:"end"})]),_:3},16,["class"]))}}),_e={key:0,class:"container w-full flex gap-4 text-gray-500 flex-wrap will-change-auto transition-all max-md:!px-2"},De=N({__name:"menu",async setup(h){let l,u;const p=M([]),{t:o,locale:c}=ie(),{data:f,error:k,status:_}=([l,u]=se(()=>le("lookups-website/categories",{watch:[c]})),l=await l,u(),l),y=B(()=>_.value!=="success"),{width:v}=Y();let a=null;k.value&&console.error("API Error: fetching lockup category",k.value);const V=()=>{var d,n;v.value>=1200?a=7:v.value<1200&&v.value>=600?a=3:v.value<600&&(a=1),a&&(p.value=((d=f.value)==null?void 0:d.slice(0,a))||[],p.value.push({text:o("header.other-sections"),children:(n=f.value)==null?void 0:n.slice(a,f.value.length),value:"OTHER_SECTION"}))};O([()=>v,()=>f],()=>{V()},{deep:!0});const P=B(()=>c.value==="ar"?"rtl":"ltr");return V(),(d,n)=>{const i=j,m=we;return t(a)?(b(),D("div",_e,[t(y)?(b(!0),D(I,{key:0},z(Array(t(a)),(w,g)=>(b(),$(t(ue),{key:`home-link-${g}`,as:"div",class:"w-32 h-10"}))),128)):(b(!0),D(I,{key:1},z(t(p),(w,g)=>(b(),$(m,{key:`${g}-${JSON.stringify(w.text)}`,dir:t(P)},{default:T(()=>[E(i,{item:w},null,8,["item"])]),_:2},1032,["dir"]))),128))])):re("",!0)}}});export{De as _};
