import{k as q,o as V,d as Y}from"./BBuxlZld.js";import{i as T,c as B,p as j,q as g,o as k,s as _,aU as X,r as h,E as L,L as C,l as w,S as W,w as R,aq as G,n as J,ay as Q}from"./C3gyeM1K.js";import{b as S,x as Z,y as ee,z as te,i as I}from"./CRC5xmnh.js";import{u as M}from"./C2RgRyPS.js";import{P as U}from"./ClItKS3j.js";import{g as D}from"./FUKBWQwh.js";import{i as ne}from"./BK9Yhb3N.js";const De=T({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=q();return(n,s)=>g(e)||n.forceMount?(k(),B(X,{key:0,to:n.to,disabled:n.disabled,defer:n.defer},[_(n.$slots,"default")],8,["to","disabled","defer"])):j("",!0)}});function K(t,e,n){const s=n.originalEvent.target,i=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:n});e&&s.addEventListener(t,e,{once:!0}),s.dispatchEvent(i)}const se="dismissableLayer.pointerDownOutside",oe="dismissableLayer.focusOutside";function H(t,e){const n=e.closest("[data-dismissable-layer]"),s=t.dataset.dismissableLayer===""?t:t.querySelector("[data-dismissable-layer]"),i=Array.from(t.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&s===n||i.indexOf(s)<i.indexOf(n))}function ae(t,e){var u;const n=((u=e==null?void 0:e.value)==null?void 0:u.ownerDocument)??(globalThis==null?void 0:globalThis.document),s=h(!1),i=h(()=>{});return L(f=>{if(!S)return;const v=async m=>{const a=m.target;if(e!=null&&e.value){if(H(e.value,a)){s.value=!1;return}if(m.target&&!s.value){let r=function(){K(se,t,c)};const c={originalEvent:m};m.pointerType==="touch"?(n.removeEventListener("click",i.value),i.value=r,n.addEventListener("click",i.value,{once:!0})):r()}else n.removeEventListener("click",i.value);s.value=!1}},p=window.setTimeout(()=>{n.addEventListener("pointerdown",v)},0);f(()=>{window.clearTimeout(p),n.removeEventListener("pointerdown",v),n.removeEventListener("click",i.value)})}),{onPointerDownCapture:()=>s.value=!0}}function re(t,e){var i;const n=((i=e==null?void 0:e.value)==null?void 0:i.ownerDocument)??(globalThis==null?void 0:globalThis.document),s=h(!1);return L(u=>{if(!S)return;const f=async v=>{e!=null&&e.value&&(await C(),await C(),!(!e.value||H(e.value,v.target))&&v.target&&!s.value&&K(oe,t,{originalEvent:v}))};n.addEventListener("focusin",f),u(()=>n.removeEventListener("focusin",f))}),{onFocusCapture:()=>s.value=!0,onBlurCapture:()=>s.value=!1}}const E=W({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Le=T({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(t,{emit:e}){const n=t,s=e,{forwardRef:i,currentElement:u}=M(),f=w(()=>{var o;return((o=u.value)==null?void 0:o.ownerDocument)??globalThis.document}),v=w(()=>E.layersRoot),p=w(()=>u.value?Array.from(v.value).indexOf(u.value):-1),m=w(()=>E.layersWithOutsidePointerEventsDisabled.size>0),a=w(()=>{const o=Array.from(v.value),[d]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),l=o.indexOf(d);return p.value>=l}),r=ae(async o=>{const d=[...E.branches].some(l=>l==null?void 0:l.contains(o.target));!a.value||d||(s("pointerDownOutside",o),s("interactOutside",o),await C(),o.defaultPrevented||s("dismiss"))},u),c=re(o=>{[...E.branches].some(l=>l==null?void 0:l.contains(o.target))||(s("focusOutside",o),s("interactOutside",o),o.defaultPrevented||s("dismiss"))},u);V("Escape",o=>{p.value===v.value.size-1&&(s("escapeKeyDown",o),o.defaultPrevented||s("dismiss"))});let y;return L(o=>{u.value&&(n.disableOutsidePointerEvents&&(E.layersWithOutsidePointerEventsDisabled.size===0&&(y=f.value.body.style.pointerEvents,f.value.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(u.value)),v.value.add(u.value),o(()=>{n.disableOutsidePointerEvents&&E.layersWithOutsidePointerEventsDisabled.size===1&&(f.value.body.style.pointerEvents=y)}))}),L(o=>{o(()=>{u.value&&(v.value.delete(u.value),E.layersWithOutsidePointerEventsDisabled.delete(u.value))})}),(o,d)=>(k(),B(g(U),{ref:g(i),"as-child":o.asChild,as:o.as,"data-dismissable-layer":"",style:G({pointerEvents:m.value?a.value?"auto":"none":void 0}),onFocusCapture:g(c).onFocusCapture,onBlurCapture:g(c).onBlurCapture,onPointerdownCapture:g(r).onPointerDownCapture},{default:R(()=>[_(o.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),F="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0};function ie(t,{select:e=!1}={}){const n=D();for(const s of t)if(b(s,{select:e}),D()!==n)return!0}function ue(t){const e=$(t),n=x(e,t),s=x(e.reverse(),t);return[n,s]}function $(t){const e=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:s=>{const i=s.tagName==="INPUT"&&s.type==="hidden";return s.disabled||s.hidden||i?NodeFilter.FILTER_SKIP:s.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)e.push(n.currentNode);return e}function x(t,e){for(const n of t)if(!le(n,{upTo:e}))return n}function le(t,{upTo:e}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(e!==void 0&&t===e)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function ce(t){return t instanceof HTMLInputElement&&"select"in t}function b(t,{select:e=!1}={}){if(t&&t.focus){const n=D();t.focus({preventScroll:!0}),t!==n&&ce(t)&&e&&t.select()}}const de=Z(()=>h([]));function fe(){const t=de();return{add(e){const n=t.value[0];e!==n&&(n==null||n.pause()),t.value=A(t.value,e),t.value.unshift(e)},remove(e){var n;t.value=A(t.value,e),(n=t.value[0])==null||n.resume()}}}function A(t,e){const n=[...t],s=n.indexOf(e);return s!==-1&&n.splice(s,1),n}function ve(t){return t.filter(e=>e.tagName!=="A")}const Ce=T({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(t,{emit:e}){const n=t,s=e,{currentRef:i,currentElement:u}=M(),f=h(null),v=fe(),p=W({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});L(a=>{if(!S)return;const r=u.value;if(!n.trapped)return;function c(l){if(p.paused||!r)return;const O=l.target;r.contains(O)?f.value=O:b(f.value,{select:!0})}function y(l){if(p.paused||!r)return;const O=l.relatedTarget;O!==null&&(r.contains(O)||b(f.value,{select:!0}))}function o(l){r.contains(f.value)||b(r)}document.addEventListener("focusin",c),document.addEventListener("focusout",y);const d=new MutationObserver(o);r&&d.observe(r,{childList:!0,subtree:!0}),a(()=>{document.removeEventListener("focusin",c),document.removeEventListener("focusout",y),d.disconnect()})}),L(async a=>{const r=u.value;if(await C(),!r)return;v.add(p);const c=D();if(!r.contains(c)){const o=new CustomEvent(F,N);r.addEventListener(F,d=>s("mountAutoFocus",d)),r.dispatchEvent(o),o.defaultPrevented||(ie(ve($(r)),{select:!0}),D()===c&&b(r))}a(()=>{r.removeEventListener(F,l=>s("mountAutoFocus",l));const o=new CustomEvent(P,N),d=l=>{s("unmountAutoFocus",l)};r.addEventListener(P,d),r.dispatchEvent(o),setTimeout(()=>{o.defaultPrevented||b(c??document.body,{select:!0}),r.removeEventListener(P,d),v.remove(p)},0)})});function m(a){if(!n.loop&&!n.trapped||p.paused)return;const r=a.key==="Tab"&&!a.altKey&&!a.ctrlKey&&!a.metaKey,c=D();if(r&&c){const y=a.currentTarget,[o,d]=ue(y);o&&d?!a.shiftKey&&c===d?(a.preventDefault(),n.loop&&b(o,{select:!0})):a.shiftKey&&c===o&&(a.preventDefault(),n.loop&&b(d,{select:!0})):c===y&&a.preventDefault()}}return(a,r)=>(k(),B(g(U),{ref_key:"currentRef",ref:i,tabindex:"-1","as-child":a.asChild,as:a.as,onKeydown:m},{default:R(()=>[_(a.$slots,"default")]),_:3},8,["as-child","as"]))}}),pe=ee(()=>{const t=h(new Map),e=h(),n=w(()=>{for(const f of t.value.values())if(f)return!0;return!1}),s=ne({scrollBody:h(!0)});let i=null;const u=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.body.style.removeProperty("--scrollbar-width"),document.body.style.overflow=e.value??"",I&&(i==null||i()),e.value=void 0};return J(n,(f,v)=>{var r;if(!S)return;if(!f){v&&u();return}e.value===void 0&&(e.value=document.body.style.overflow);const p=window.innerWidth-document.documentElement.clientWidth,m={padding:p,margin:0},a=(r=s.scrollBody)!=null&&r.value?typeof s.scrollBody.value=="object"?Q({padding:s.scrollBody.value.padding===!0?p:s.scrollBody.value.padding,margin:s.scrollBody.value.margin===!0?p:s.scrollBody.value.margin},m):m:{padding:0,margin:0};p>0&&(document.body.style.paddingRight=typeof a.padding=="number"?`${a.padding}px`:String(a.padding),document.body.style.marginRight=typeof a.margin=="number"?`${a.margin}px`:String(a.margin),document.body.style.setProperty("--scrollbar-width",`${p}px`),document.body.style.overflow="hidden"),I&&(i=Y(document,"touchmove",c=>me(c),{passive:!1})),C(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),t});function Se(t){const e=Math.random().toString(36).substring(2,7),n=pe();n.value.set(e,t??!1);const s=w({get:()=>n.value.get(e)??!1,set:i=>n.value.set(e,i)});return te(()=>{n.value.delete(e)}),s}function z(t){const e=window.getComputedStyle(t);if(e.overflowX==="scroll"||e.overflowY==="scroll"||e.overflowX==="auto"&&t.clientWidth<t.scrollWidth||e.overflowY==="auto"&&t.clientHeight<t.scrollHeight)return!0;{const n=t.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:z(n)}}function me(t){const e=t||window.event,n=e.target;return n instanceof Element&&z(n)?!1:e.touches.length>1?!0:(e.preventDefault&&e.cancelable&&e.preventDefault(),!1)}export{De as _,Le as a,Ce as b,K as h,Se as u};
