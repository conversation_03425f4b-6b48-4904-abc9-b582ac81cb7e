import{r as u,m as g,l as f,R as v,d as y,o as b,s,p as S,a1 as k,av as A}from"./C3gyeM1K.js";import{u as E}from"./Bzz9boR6.js";const d="_carbonads_js",B={__name:"ScriptCarbonAds",props:{serve:{type:String,required:!0},placement:{type:String,required:!0},format:{type:String,required:!0},trigger:{type:[String,Array,Boolean],required:!1}},emits:["error","ready"],setup(c,{emit:m}){const a=c,n=m,t=u(document.getElementById(d)),r=u("awaitingLoad");function i(){if(!t.value)return;r.value="loading";const e=document.createElement("script");e.setAttribute("src",A("https://cdn.carbonads.com/carbon.js",{serve:a.serve,placement:a.placement,format:a.format})),e.setAttribute("id",d),e.onerror=l=>{r.value!=="error"&&(r.value="error",n("error",l))},e.onload=()=>{r.value!=="loaded"&&(r.value="loaded",n("ready",e))},t.value.appendChild(e)}const o=E({trigger:a.trigger,el:t});g(()=>{o==="onNuxtReady"?i():o.then(i)});const p=f(()=>({...o instanceof Promise?o.ssrAttrs||{}:{}}));return v(()=>{t.value&&t.value.remove()}),(e,l)=>(b(),y("div",k({ref_key:"carbonadsEl",ref:t},p.value),[r.value==="awaitingLoad"?s(e.$slots,"awaitingLoad",{key:0}):r.value==="loading"?s(e.$slots,"loading",{key:1}):r.value==="error"?s(e.$slots,"error",{key:2}):S("",!0)],16))}};export{B as default};
