const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as G,j as J,k as K,l as S,d as a,e,c as h,$ as m,a as s,p as g,q as f,t as n,w as x,a0 as y,f as Q,o as l,g as U,W as v,ag as X,ah as Y,_ as Z}from"./C3gyeM1K.js";import{Skeleton as ee}from"./BaodNYLQ.js";import{_ as se}from"./BoqtTPyX.js";import{_ as te}from"./BxRYGYnW.js";import"./jAU0Cazi.js";import"./D8XAHdeJ.js";import"./D1vxl8R8.js";import"./ClItKS3j.js";import"./BbzLkj_4.js";import"./L0mwwrGq.js";const le=X(()=>Y(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(u=>u.default||u)),ae={class:"flex flex-col w-1/2 gap-4 max-md:w-full"},oe={class:"flex gap-4"},ce={class:"flex gap-1"},de={class:"text-sm"},ne={key:0,class:"flex"},re={class:"text-sm font-bold"},ie={class:"flex gap-1 items-center justify-center"},ue={class:"bg-green-600 rounded-full w-5 h-5 flex justify-center items-center"},me={class:"text-sm font-bold text-green-600"},fe={class:"flex text-xl gap-2 w-full flex-col"},_e={key:1,class:"font-bold"},pe={key:0,class:"flex items-center gap-2 flex-col"},he={key:1,class:"flex items-center gap-2"},ge={class:"flex gap-1 items-center"},xe={class:"text-sm font-semibold"},ye={key:2,class:"flex items-center gap-2 flex-col"},ve={key:3,class:"flex flex-col py-2 gap-4"},be={class:"flex items-center p-4 rounded-lg bg-sky-50 gap-2"},ke={class:"flex items-center justify-center p-2 rounded bg-primary-500"},we={class:"text-sm"},$e={key:4,class:"flex gap-2 flex-col"},Ie={class:"flex flex-col gap-2 py-4"},Ce={class:"flex gap-2"},Le={class:"text-sm font-medium"},je={class:"text-sm font-bold text-primary-600"},ze={class:"flex gap-2 flex-wrap max-w-full"},Ae=["onClick"],Oe={class:"variant-src"},Be={key:6,class:"flex gap-2 flex-col"},Ne={key:7,class:"flex flex-col gap-4"},Ve={class:"flex text-md font-medium"},Re=["innerHTML"],Se={key:8,class:"flex gap-4"},Ee={key:9,class:"flex gap-4"},Te={class:"flex gap-2"},qe={class:"text-sm font-semibold text-primary-500"},De={class:"flex text-sm"},He={key:10,class:"flex gap-2"},Me={class:"text-md font-medium leading-none"},Pe=G({__name:"details",props:{product:{},loading:{type:Boolean}},emits:["select:variant"],setup(u,{emit:E}){const T=E,b=J(),{locale:q}=K(),{brand:D,brandLogo:k,isAvailable:H,sku:w}=u.product,M=`${b.path}#details`,P=`${b.path}?tab=rating&form=rating#details`,F=S(()=>q.value==="ar"),$=()=>{var o,_;const t=(o=u.product)==null?void 0:o.variationAttributes.map(r=>{var p;return(p=r.value)==null?void 0:p.attributeOptionId}),i=(_=u.product)==null?void 0:_.variationAttributes.map(r=>r.value).map(r=>r.slug).join("-");T("select:variant",{varianceSlug:i,varianceIds:t})};return S(()=>{var t;return((t=u.product)==null?void 0:t.description)??""}),(t,i)=>{var I,C,L,j,z,A,O;const o=ee,_=le,r=U,p=Q,W=se;return l(),a("div",ae,[e("div",oe,[t.loading?(l(),a(m,{key:0},[s(o,{class:"w-1/5 h-5"}),s(o,{class:"w-1/5 h-5"}),s(o,{class:"w-1/6 h-5"})],64)):(l(),a(m,{key:1},[e("div",ce,[f(k)?(l(),h(_,{key:0,src:f(k),class:"h-5",height:"20",loading:"eager",fit:"contain"},null,8,["src"])):g("",!0),e("strong",de,n((I=f(D))==null?void 0:I.name),1)]),f(w)?(l(),a("div",ne,[e("span",re,n(t.$t("product.sku-title",{sku:f(w)})),1)])):g("",!0),e("div",ie,[f(H)?(l(),a(m,{key:0},[e("span",ue,[s(r,{name:"lucide:check",size:"16px",class:"text-white font-bold"})]),e("span",me,n(t.$t("product.available")),1)],64)):g("",!0)])],64))]),e("div",fe,[t.loading?(l(),a(m,{key:0},[s(o,{class:"w-full h-5"}),s(o,{class:"w-full h-5"})],64)):(l(),a("h1",_e,n((L=(C=t.product)==null?void 0:C.variance)==null?void 0:L.name),1))]),t.loading?(l(),a("div",pe,[s(o,{class:"w-full h-6"}),s(o,{class:"w-full h-6"})])):(l(),a("div",he,[e("div",ge,[s(r,{name:"ui:start-filled",size:"14px",class:"text-yellow-400 drop-shadow"}),e("span",xe,n(t.$t("product.rate-details",{rate:(j=t.product)==null?void 0:j.avgRate})),1)]),i[0]||(i[0]=e("div",{class:"flex"},[e("div",{class:"h-3 w-px bg-gray-300"})],-1)),s(p,{to:P,class:"flex gap-1 items-center hover:underline"},{default:x(()=>[s(r,{name:"lucide:message-square-more",size:"12px"}),e("span",null,n(t.$t("product.write-comment")),1)]),_:1})])),i[2]||(i[2]=e("div",{class:"flex w-full border-y border-dashed border-gray-200"},null,-1)),t.loading?(l(),a("div",ye,[s(o,{class:"w-full h-6"}),s(o,{class:"w-full h-6"})])):(l(),a("div",ve,[s(te,{class:"md:hidden sm:flex xs:hidden",loading:t.loading,product:t.product},null,8,["loading","product"]),e("div",be,[e("div",ke,[s(r,{name:"lucide:truck",class:"text-white"})]),e("p",we,n(t.$t("product.delivery-text")),1)])])),i[3]||(i[3]=e("div",{class:"flex w-full border-y border-dashed border-gray-200"},null,-1)),t.loading?(l(),a("div",$e,[s(o,{class:"w-1/3 h-6"}),s(o,{class:"w-full h-6"}),s(o,{class:"w-full h-6"})])):(l(!0),a(m,{key:5},y((z=t.product)==null?void 0:z.variationAttributes,d=>(l(),a("div",{key:d==null?void 0:d.attributeId,class:"flex flex-col w-full"},[e("div",Ie,[e("div",Ce,[e("span",Le,n(d.name)+" : ",1),e("span",je,n(d.value.name),1)]),e("div",ze,[(l(!0),a(m,null,y(d==null?void 0:d.options,c=>{var B,N,V,R;return l(),a("div",{key:c==null?void 0:c.attributeOptionId,class:v([{"not-in-stock":!(c!=null&&c.stock)},"option flex items-center justify-center rounded border-primary-500"])},[d.type==="color"?(l(),a("button",{key:0,class:v([{"border-primary-600 bg-gray-100":(c==null?void 0:c.attributeOptionId)===((B=d.value)==null?void 0:B.attributeOptionId)},"variant flex rounded-lg border-2 border-gray-200 p-2 justify-center items-center hover:border-primary-600"]),onClick:()=>{d.value=c,$()}},[s(_,{src:`https://action-v2-backend.b-cdn.net/${(V=(N=c==null?void 0:c.media)==null?void 0:N.gallery)==null?void 0:V.preview}`,alt:c==null?void 0:c.name,class:"variant-src w-14 h-14 object-contain",sizes:"xs:100vw md:100px",width:"100",height:"100",format:"webp",fit:"contain"},null,8,["src","alt"])],10,Ae)):(l(),h(W,{key:1,class:"variant sm",variant:`${(c==null?void 0:c.attributeOptionId)===((R=d.value)==null?void 0:R.attributeOptionId)?"outline":"secondary"}`,onClick:()=>{d.value=c,$()}},{default:x(()=>[e("span",Oe,n(c==null?void 0:c.name),1)]),_:2},1032,["variant","onClick"]))],2)}),128))])]),i[1]||(i[1]=e("div",{class:"flex w-full border-y border-dashed border-gray-200"},null,-1))]))),128)),t.loading?(l(),a("div",Be,[s(o,{class:"w-1/3 h-6"}),s(o,{class:"w-full h-6"}),s(o,{class:"w-full h-6"})])):(l(),a("div",Ne,[e("div",Ve,n(t.$t("product.about-title")),1),e("div",{class:"text-md line-clamp-6",innerHTML:(A=t.product)==null?void 0:A.description},null,8,Re)])),t.loading?(l(),a("div",Se,[s(o,{class:"w-1/3 h-10"}),s(o,{class:"w-1/3 h-10"}),s(o,{class:"w-1/3 h-10"})])):(l(),a("div",Ee,[(l(!0),a(m,null,y((O=t.product)==null?void 0:O.variationAttributes,d=>(l(),a("div",{key:`selected-${d.attributeId}`,class:"flex items-center bg-sky-50 py-2 px-4 rounded-lg flex-col"},[e("div",Te,[s(r,{name:`ui:${d.icon||"colors"}`,class:"text-primary-600"},null,8,["name"]),e("span",qe,n(d.name),1)]),e("div",De,n(d.value.name),1)]))),128))])),t.loading?(l(),a("div",He,[s(o,{class:"w-44 h-6"}),s(o,{class:"w-6 h-6"})])):(l(),h(p,{key:11,to:M,class:"flex w-full items-center gap-1 underline hover:text-primary-700"},{default:x(()=>[e("span",Me,n(t.$t("product.see-more-details")),1),s(r,{name:"lucide:chevron-right",class:v({"rotate-180":f(F)})},null,8,["class"])]),_:1}))])}}}),es=Z(Pe,[["__scopeId","data-v-ce7bbead"]]);export{es as default};
