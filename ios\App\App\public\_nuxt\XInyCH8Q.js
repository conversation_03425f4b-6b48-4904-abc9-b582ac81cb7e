import{_ as v}from"./BoqtTPyX.js";import{i as g,k as b,l as a,n as h,d as r,e as n,a as w,$ as k,a0 as _,q as c,w as x,o as l}from"./C3gyeM1K.js";import{u as $}from"./CvhKjyCU.js";import{u as B}from"./BsYnu6hU.js";const I={class:"flex relative flex-col w-full gap-2",dir:"ltr"},S=["action"],C=["name","value"],P=g({__name:"hyperpay-form",props:{params:{},callBackUrl:{default:"my/wallet"}},setup(o){const{locale:s}=b(),i=a(()=>{var t;return((t=o.params)==null?void 0:t.params)||o.params.formSign}),m=a(()=>{var t;return(t=i.value)==null?void 0:t.checkoutId}),d=a(()=>[window.location.origin,s.value,o.callBackUrl].join("/")),p=a(()=>"oppwa.com");return $({script:[{innerHTML:`var wpwlOptions = {
          paymentTarget: "_top",
          style:"card",
          locale: "${s.value}",
          iframeStyles: {
            'card-number-placeholder': {
              'direction': '"${s.value==="ar"?"rtl":"ltr"}"',
              'text-align': 'center'
            },
            'cvv-placeholder': {
              'direction': 'ltr',
              'text-align': 'center'
            }
          },
          onReady: function () {}
        };`,tagPosition:"bodyClose",type:"text/javascript",body:!1}]}),h(m,async t=>{t&&B({src:`https://${p.value}/v1/paymentWidgets.js?checkoutId=${t}`,crossorigin:"anonymous"})},{immediate:!0,deep:!0}),(t,e)=>{const f=v;return l(),r("div",I,[n("form",{method:"GET",class:"paymentWidgets","data-brands":"VISA MASTER",action:c(d)},[(l(!0),r(k,null,_(c(i),(y,u)=>(l(),r("input",{key:u,type:"hidden",name:u,value:y},null,8,C))),128)),e[1]||(e[1]=n("input",{id:"signature",type:"hidden",name:"signature",value:"sign($formParams)"},null,-1)),w(f,{class:"w-full","as-child":""},{default:x(()=>e[0]||(e[0]=[n("input",{id:"submit",type:"submit",value:"Confirm"},null,-1)])),_:1}),e[2]||(e[2]=n("input",{id:"submit",class:"btn btn-purple btn-block",type:"submit",value:""},null,-1))],8,S)])}}});export{P as _};
