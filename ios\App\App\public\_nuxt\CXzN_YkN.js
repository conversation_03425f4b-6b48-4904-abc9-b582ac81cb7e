import{_}from"./DAA1LH-g.js";import{i as w,j as f,ak as h,l as e,c as s,w as C,o as r,al as k,q as I}from"./C3gyeM1K.js";const R=w({__name:"index",props:{loading:{type:Boolean},order:{}},setup(a){const c=[{component:"CheckoutFlowAddress"},{component:"CheckoutFlowShipment"},{component:"CheckoutFlowPayment"},{component:"CheckoutFlowReviews"}],t=f(),p=h(),m=e(()=>Number(t.params.stepId)),u=e(()=>m.value-1),d=e(()=>c[u.value]),l=o=>{p.push({name:t.name,params:{orderId:a.order.orderId,stepId:o}})};return(o,v)=>{const i=_;return r(),s(i,null,{default:C(()=>{var n;return[(r(),s(k((n=I(d))==null?void 0:n.component),{order:o.order,"onSet:flow":l},null,40,["order"]))]}),_:1})}}});export{R as _};
