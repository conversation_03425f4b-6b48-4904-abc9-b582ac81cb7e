import{_ as h,u as b}from"./BD9sJOps.js";import{i as v,k,X as C,l as n,d as w,a as s,q as a,w as r,$ as B,Y as $,o as H,e,t as _,g as L}from"./C3gyeM1K.js";import{_ as A}from"./DAA1LH-g.js";import{_ as D}from"./JNWsvxxg.js";import{_ as I}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const M={class:"font-bold text-2xl"},N={class:"text-lg text-gray-600"},S={class:"flex flex-col gap-4 bg-sky-50 p-4 rounded-lg"},T={class:"flex items-center gap-2"},V={class:"flex rounded-full p-2 bg-sky-100"},j={class:"text-lg"},q=["innerHTML"],R=v({__name:"privacy",async setup(E){let o,i;const{t:F}=k(),{data:c}=([o,i]=C(()=>$("pages/privacy-policy")),o=await o,i(),o),p=n(()=>{var t;return(t=c.value)==null?void 0:t.body}),l=n(()=>{var t;return(t=c.value)==null?void 0:t.name}),d=n(()=>{var t;return(t=c.value)==null?void 0:t.metaDescription}),m=n(()=>b().buildSinglePage(l.value));return(t,P)=>{const u=h,f=I,x=L,g=D,y=A;return H(),w(B,null,[s(u,{links:a(m),class:"!border-0 !shadow-none"},null,8,["links"]),s(y,{class:"flex flex-col w-full h-full my-6"},{default:r(()=>[s(f,{class:"text-center justify-center gap-4 py-12"},{default:r(()=>[e("h1",M,_(a(l)),1),e("h2",N,_(a(d)),1)]),_:1}),s(g,{class:"flex flex-col gap-6"},{default:r(()=>[e("div",S,[e("div",T,[e("div",V,[s(x,{name:"ui:privacy"})]),e("span",j,_(a(l)),1)]),e("div",{class:"text-start text-sm text-gray-600 ps-6",innerHTML:a(p)},null,8,q)])]),_:1})]),_:1})],64)}}});export{R as default};
