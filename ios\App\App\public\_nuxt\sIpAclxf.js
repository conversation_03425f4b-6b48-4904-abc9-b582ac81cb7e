import{n as w,r as b,l as s,aO as E,m as N,R as z,d as L,o as q,a4 as _,s as c,p as d,az as D,e as F,v as H,a1 as y,q as g,a as I,ay as A}from"./C3gyeM1K.js";import{u as W}from"./Bzz9boR6.js";import{u as x}from"./CvhKjyCU.js";import{u as M}from"./C0giEChS.js";import U from"./DiFCG9It.js";import"./BsYnu6hU.js";function G(m){const l=M("vimeoPlayer",()=>({scriptInput:{src:"https://player.vimeo.com/api/player.js"},scriptOptions:{use(){return{Vimeo:window.Vimeo}}}}),m);{const p=w(l.status,o=>{o==="loading"&&(x({link:[{rel:"preconnect",href:"https://i.vimeocdn.com"},{rel:"preconnect",href:"https://f.vimeocdn.com"},{rel:"preconnect",href:"https://fresnel.vimeocdn.com"}]}),p())})}return l}const ee={__name:"ScriptVimeoPlayer",props:{trigger:{type:[String,Array,Boolean],required:!1,default:"mousedown"},placeholderAttrs:{type:Object,required:!1},rootAttrs:{type:Object,required:!1},aboveTheFold:{type:Boolean,required:!1},vimeoOptions:{type:Object,required:!1},id:{type:null,required:!1},url:{type:null,required:!1}},emits:["play","playing","pause","ended","timeupdate","progress","seeking","seeked","texttrackchange","chapterchange","cuechange","cuepoint","volumechange","playbackratechange","bufferstart","bufferend","error","loaded","durationchange","fullscreenchange","qualitychange","camerachange","resize","enterpictureinpicture","leavepictureinpicture"],setup(m,{expose:l,emit:p}){const o=m,P=p,T=["play","playing","pause","ended","timeupdate","progress","seeking","seeked","texttrackchange","chapterchange","cuechange","cuepoint","volumechange","playbackratechange","bufferstart","bufferend","error","loaded","durationchange","fullscreenchange","qualitychange","camerachange","resize","enterpictureinpicture","leavepictureinpicture"],u=b(),V=b(),n=W({trigger:o.trigger,el:V});let h=!1;o.trigger==="mousedown"&&n instanceof Promise&&n.then(t=>{t&&(h=!0)});const f=b(!1),{onLoaded:S,status:i}=G({scriptOptions:{trigger:n}}),v=s(()=>{var t;return((t=o.vimeoOptions)==null?void 0:t.id)||o.id}),{data:O}=E(`vimeo-embed:${v.value}`,()=>$fetch(`https://vimeo.com/api/v2/video/${v.value}.json`).then(t=>t[0]),{watch:[v]}),k=s(()=>{var t;return(t=O.value)==null?void 0:t.thumbnail_large});let e;l({play:()=>e==null?void 0:e.play(),pause:()=>e==null?void 0:e.pause(),getDuration:()=>e==null?void 0:e.getDuration(),getCurrentTime:()=>e==null?void 0:e.getCurrentTime(),setCurrentTime:t=>e==null?void 0:e.setCurrentTime(t),getVolume:()=>e==null?void 0:e.getVolume(),setVolume:t=>e==null?void 0:e.setVolume(t),getPaused:()=>e==null?void 0:e.getPaused(),getEnded:()=>e==null?void 0:e.getEnded(),getLoop:()=>e==null?void 0:e.getLoop(),setLoop:t=>e==null?void 0:e.setLoop(t),getPlaybackRate:()=>e==null?void 0:e.getPlaybackRate(),setPlaybackRate:t=>e==null?void 0:e.setPlaybackRate(t)});const $=s(()=>{var t,a,r;return((t=o.vimeoOptions)==null?void 0:t.width)||((r=(a=u.value)==null?void 0:a.parentNode)==null?void 0:r.offsetWidth)||640}),R=s(()=>{var t,a,r;return((t=o.vimeoOptions)==null?void 0:t.height)||((r=(a=u.value)==null?void 0:a.parentNode)==null?void 0:r.offsetHeight)||480});N(()=>{S(async({Vimeo:t})=>{const a=o.vimeoOptions||{};!a.id&&o.id&&(a.id=o.id),!a.url&&o.url&&(a.url=o.url),a.width=$.value,a.height=R.value,e=new t.Player(u.value,a),h&&(e.play(),h=!1);for(const r of T)e.on(r,C=>{P(r,C,e),r==="loaded"&&(f.value=!0)})})}),w(()=>o.id,t=>{t&&(e==null||e.loadVideo(Number(t)))}),w(i,t=>{t==="error"&&P("error")});const j=s(()=>A(o.rootAttrs,{"aria-busy":i.value==="loading","aria-label":i.value==="awaitingLoad"?"Vimeo Player - Placeholder":i.value==="loading"?"Vimeo Player - Loading":"Vimeo Player - Loaded","aria-live":"polite",role:"application",style:{maxWidth:"100%",width:"auto",height:"auto",aspectRatio:"16/9",position:"relative",backgroundColor:"black"},...n instanceof Promise?n.ssrAttrs||{}:{}})),B=s(()=>A(o.placeholderAttrs,{src:k.value,alt:"",loading:o.aboveTheFold?"eager":"lazy",fetchpriority:o.aboveTheFold?"high":void 0,style:{cursor:"pointer",width:"100%",objectFit:"contain",height:"100%"}}));return z(()=>e==null?void 0:e.unload()),(t,a)=>(q(),L("div",y({ref_key:"rootEl",ref:V},j.value),[_(F("div",{ref_key:"elVimeo",ref:u,class:"vimeo-player"},null,512),[[D,f.value]]),f.value?d("",!0):c(t.$slots,"placeholder",y({key:0},g(O),{placeholder:k.value}),()=>[k.value?(q(),L("img",H(y({key:0},B.value)),null,16)):d("",!0)]),g(i)==="loading"?c(t.$slots,"loading",{key:1},()=>[I(U,{color:"white"})]):d("",!0),g(i)==="awaitingLoad"?c(t.$slots,"awaitingLoad",{key:2}):g(i)==="error"?c(t.$slots,"error",{key:3}):d("",!0),c(t.$slots,"default")],16))}};export{ee as default};
