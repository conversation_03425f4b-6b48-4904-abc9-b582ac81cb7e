import{_ as y,u as b}from"./BD9sJOps.js";import{i as v,X as w,l as o,d as C,a as t,q as s,w as r,$ as k,Y as B,o as $,e,t as _,g as H}from"./C3gyeM1K.js";import{_ as L}from"./DAA1LH-g.js";import{_ as A}from"./JNWsvxxg.js";import{_ as D}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const M={class:"font-bold text-2xl"},N={class:"text-lg text-gray-600"},S={class:"flex flex-col gap-4 bg-sky-50 p-4 rounded"},T={class:"flex items-center gap-2"},V={class:"flex rounded-full p-2 bg-sky-100"},j={class:"text-lg"},q=["innerHTML"],O=v({__name:"website-usage",async setup(E){let a,i;const{data:n}=([a,i]=w(()=>B("pages/website-use-policy")),a=await a,i(),a),p=o(()=>n.value.body),c=o(()=>n.value.name),d=o(()=>{var l;return(l=n.value)==null?void 0:l.metaDescription}),m=o(()=>b().buildSinglePage(c.value));return(l,F)=>{const u=y,f=D,x=H,g=A,h=L;return $(),C(k,null,[t(u,{links:s(m),class:"!border-0 !shadow-none"},null,8,["links"]),t(h,{class:"flex flex-col w-full h-full my-6"},{default:r(()=>[t(f,{class:"text-center justify-center gap-4 py-12"},{default:r(()=>[e("h1",M,_(s(c)),1),e("h2",N,_(s(d)),1)]),_:1}),t(g,{class:"flex flex-col gap-6"},{default:r(()=>[e("div",S,[e("div",T,[e("div",V,[t(x,{name:"ui:privacy"})]),e("span",j,_(s(c)),1)]),e("div",{class:"whitespace-pre-line text-sm text-gray-600 ps-6 text-start",innerHTML:s(p)},null,8,q)])]),_:1})]),_:1})],64)}}});export{O as default};
