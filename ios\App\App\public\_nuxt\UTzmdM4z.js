import{i as b,z as y,l as p,k as B,r as x,c as _,w as n,K as L,q as t,o,a as l,e as d,t as N,g as S,d as C,a0 as I,$ as M}from"./C3gyeM1K.js";import{_ as P}from"./Tzk92Vbf.js";import{_ as V,a as q,b as z}from"./CUNFy6WN.js";const A={class:"pe-4"},J=b({__name:"list",props:{item:{}},setup(D){const f=y(),h=p(()=>f("/categories")),{locale:$}=B(),g=p(()=>$.value==="ar"?"rtl":"ltr"),a=x(!1);return(e,r)=>{const v=S,k=P;return o(),_(t(z),{open:t(a),"onUpdate:open":r[0]||(r[0]=s=>L(a)?a.value=s:null),dir:g.value},{default:n(()=>[l(t(V),{"as-child":"",class:"hover:bg-gray-50 p-2 flex items-center text-md"},{default:n(()=>[d("button",null,[d("span",A,N(e.item.text),1),l(v,{name:"lucide:chevron-down",class:"w-3 inline"})])]),_:1}),l(t(q),{class:"min-w-56"},{default:n(()=>{var s,i;return[(o(!0),C(M,null,I(((s=e.item)==null?void 0:s.children)||((i=e.item.meta)==null?void 0:i.children),(c,w)=>{var m,u;return o(),_(k,{key:`${w}-${JSON.stringify(c.text)}`,item:c,close:()=>a.value=!1,path:`${h.value}${(m=e.item.meta)!=null&&m.slug?`/${(u=e.item.meta)==null?void 0:u.slug}`:""}`},null,8,["item","close","path"])}),128))]}),_:1})]),_:1},8,["open","dir"])}}});export{J as _};
