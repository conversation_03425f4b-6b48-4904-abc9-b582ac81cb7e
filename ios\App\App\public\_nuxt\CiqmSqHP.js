import{c as Tt}from"./jAU0Cazi.js";import{K as Gt,D as Ht,m as Yt,R as mn,n as Kt,k as gn,r as qt,i as At,d as Dt,o as wt,W as Zt,q as V,s as Pt,e as hn,a1 as Sn}from"./C3gyeM1K.js";import{v as yn}from"./CRC5xmnh.js";function bn(t){return Object.prototype.toString.call(t)==="[object Object]"}function Ut(t){return bn(t)||Array.isArray(t)}function vn(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Ot(t,n){const r=Object.keys(t),s=Object.keys(n);if(r.length!==s.length)return!1;const i=JSON.stringify(Object.keys(t.breakpoints||{})),o=JSON.stringify(Object.keys(n.breakpoints||{}));return i!==o?!1:r.every(e=>{const u=t[e],c=n[e];return typeof u=="function"?`${u}`==`${c}`:!Ut(u)||!Ut(c)?u===c:Ot(u,c)})}function Qt(t){return t.concat().sort((n,r)=>n.name>r.name?1:-1).map(n=>n.options)}function xn(t,n){if(t.length!==n.length)return!1;const r=Qt(t),s=Qt(n);return r.every((i,o)=>{const e=s[o];return Ot(i,e)})}function Mt(t){return typeof t=="number"}function Lt(t){return typeof t=="string"}function St(t){return typeof t=="boolean"}function Jt(t){return Object.prototype.toString.call(t)==="[object Object]"}function O(t){return Math.abs(t)}function Ct(t){return Math.sign(t)}function lt(t,n){return O(t-n)}function En(t,n){if(t===0||n===0||O(t)<=O(n))return 0;const r=lt(O(t),O(n));return O(r/t)}function Ln(t){return Math.round(t*100)/100}function ft(t){return dt(t).map(Number)}function j(t){return t[mt(t)]}function mt(t){return Math.max(0,t.length-1)}function Nt(t,n){return n===mt(t)}function Xt(t,n=0){return Array.from(Array(t),(r,s)=>n+s)}function dt(t){return Object.keys(t)}function Wt(t,n){return[t,n].reduce((r,s)=>(dt(s).forEach(i=>{const o=r[i],e=s[i],u=Jt(o)&&Jt(e);r[i]=u?Wt(o,e):e}),r),{})}function It(t,n){return typeof n.MouseEvent<"u"&&t instanceof n.MouseEvent}function In(t,n){const r={start:s,center:i,end:o};function s(){return 0}function i(c){return o(c)/2}function o(c){return n-c}function e(c,a){return Lt(t)?r[t](c):t(n,c,a)}return{measure:e}}function pt(){let t=[];function n(i,o,e,u={passive:!0}){let c;if("addEventListener"in i)i.addEventListener(o,e,u),c=()=>i.removeEventListener(o,e,u);else{const a=i;a.addListener(e),c=()=>a.removeListener(e)}return t.push(c),s}function r(){t=t.filter(i=>i())}const s={add:n,clear:r};return s}function Tn(t,n,r,s){const i=pt(),o=1e3/60;let e=null,u=0,c=0;function a(){i.add(t,"visibilitychange",()=>{t.hidden&&f()})}function g(){S(),i.clear()}function d(h){if(!c)return;e||(e=h,r(),r());const p=h-e;for(e=h,u+=p;u>=o;)r(),u-=o;const y=u/o;s(y),c&&(c=n.requestAnimationFrame(d))}function l(){c||(c=n.requestAnimationFrame(d))}function S(){n.cancelAnimationFrame(c),e=null,u=0,c=0}function f(){e=null,u=0}return{init:a,destroy:g,start:l,stop:S,update:r,render:s}}function An(t,n){const r=n==="rtl",s=t==="y",i=s?"y":"x",o=s?"x":"y",e=!s&&r?-1:1,u=g(),c=d();function a(f){const{height:m,width:h}=f;return s?m:h}function g(){return s?"top":r?"right":"left"}function d(){return s?"bottom":r?"left":"right"}function l(f){return f*e}return{scroll:i,cross:o,startEdge:u,endEdge:c,measureSize:a,direction:l}}function nt(t=0,n=0){const r=O(t-n);function s(a){return a<t}function i(a){return a>n}function o(a){return s(a)||i(a)}function e(a){return o(a)?s(a)?t:n:a}function u(a){return r?a-r*Math.ceil((a-n)/r):a}return{length:r,max:n,min:t,constrain:e,reachedAny:o,reachedMax:i,reachedMin:s,removeOffset:u}}function tn(t,n,r){const{constrain:s}=nt(0,t),i=t+1;let o=e(n);function e(l){return r?O((i+l)%i):s(l)}function u(){return o}function c(l){return o=e(l),d}function a(l){return g().set(u()+l)}function g(){return tn(t,u(),r)}const d={get:u,set:c,add:a,clone:g};return d}function Dn(t,n,r,s,i,o,e,u,c,a,g,d,l,S,f,m,h,p,y){const{cross:v,direction:L}=t,w=["INPUT","SELECT","TEXTAREA"],I={passive:!1},x=pt(),E=pt(),T=nt(50,225).constrain(S.measure(20)),M={mouse:300,touch:400},A={mouse:500,touch:600},F=f?43:25;let R=!1,_=0,$=0,Z=!1,X=!1,K=!1,q=!1;function st(b){if(!y)return;function D(k){(St(y)||y(b,k))&&ct(k)}const C=n;x.add(C,"dragstart",k=>k.preventDefault(),I).add(C,"touchmove",()=>{},I).add(C,"touchend",()=>{}).add(C,"touchstart",D).add(C,"mousedown",D).add(C,"touchcancel",N).add(C,"contextmenu",N).add(C,"click",Q,!0)}function G(){x.clear(),E.clear()}function et(){const b=q?r:n;E.add(b,"touchmove",B,I).add(b,"touchend",N).add(b,"mousemove",B,I).add(b,"mouseup",N)}function ot(b){const D=b.nodeName||"";return w.includes(D)}function U(){return(f?A:M)[q?"mouse":"touch"]}function it(b,D){const C=d.add(Ct(b)*-1),k=g.byDistance(b,!f).distance;return f||O(b)<T?k:h&&D?k*.5:g.byIndex(C.get(),0).distance}function ct(b){const D=It(b,s);q=D,K=f&&D&&!b.buttons&&R,R=lt(i.get(),e.get())>=2,!(D&&b.button!==0)&&(ot(b.target)||(Z=!0,o.pointerDown(b),a.useFriction(0).useDuration(0),i.set(e),et(),_=o.readPoint(b),$=o.readPoint(b,v),l.emit("pointerDown")))}function B(b){if(!It(b,s)&&b.touches.length>=2)return N(b);const C=o.readPoint(b),k=o.readPoint(b,v),H=lt(C,_),J=lt(k,$);if(!X&&!q&&(!b.cancelable||(X=H>J,!X)))return N(b);const W=o.pointerMove(b);H>m&&(K=!0),a.useFriction(.3).useDuration(.75),u.start(),i.add(L(W)),b.preventDefault()}function N(b){const C=g.byDistance(0,!1).index!==d.get(),k=o.pointerUp(b)*U(),H=it(L(k),C),J=En(k,H),W=F-10*J,Y=p+J/50;X=!1,Z=!1,E.clear(),a.useDuration(W).useFriction(Y),c.distance(H,!f),q=!1,l.emit("pointerUp")}function Q(b){K&&(b.stopPropagation(),b.preventDefault(),K=!1)}function z(){return Z}return{init:st,destroy:G,pointerDown:z}}function wn(t,n){let s,i;function o(d){return d.timeStamp}function e(d,l){const f=`client${(l||t.scroll)==="x"?"X":"Y"}`;return(It(d,n)?d:d.touches[0])[f]}function u(d){return s=d,i=d,e(d)}function c(d){const l=e(d)-e(i),S=o(d)-o(s)>170;return i=d,S&&(s=d),l}function a(d){if(!s||!i)return 0;const l=e(i)-e(s),S=o(d)-o(s),f=o(d)-o(i)>170,m=l/S;return S&&!f&&O(m)>.1?m:0}return{pointerDown:u,pointerMove:c,pointerUp:a,readPoint:e}}function Pn(){function t(r){const{offsetTop:s,offsetLeft:i,offsetWidth:o,offsetHeight:e}=r;return{top:s,right:i+o,bottom:s+e,left:i,width:o,height:e}}return{measure:t}}function On(t){function n(s){return t*(s/100)}return{measure:n}}function Mn(t,n,r,s,i,o,e){const u=[t].concat(s);let c,a,g=[],d=!1;function l(h){return i.measureSize(e.measure(h))}function S(h){if(!o)return;a=l(t),g=s.map(l);function p(y){for(const v of y){if(d)return;const L=v.target===t,w=s.indexOf(v.target),I=L?a:g[w],x=l(L?t:s[w]);if(O(x-I)>=.5){h.reInit(),n.emit("resize");break}}}c=new ResizeObserver(y=>{(St(o)||o(h,y))&&p(y)}),r.requestAnimationFrame(()=>{u.forEach(y=>c.observe(y))})}function f(){d=!0,c&&c.disconnect()}return{init:S,destroy:f}}function Cn(t,n,r,s,i,o){let e=0,u=0,c=i,a=o,g=t.get(),d=0;function l(){const I=s.get()-t.get(),x=!c;let E=0;return x?(e=0,r.set(s),t.set(s),E=I):(r.set(t),e+=I/c,e*=a,g+=e,t.add(e),E=g-d),u=Ct(E),d=g,w}function S(){const I=s.get()-n.get();return O(I)<.001}function f(){return c}function m(){return u}function h(){return e}function p(){return v(i)}function y(){return L(o)}function v(I){return c=I,w}function L(I){return a=I,w}const w={direction:m,duration:f,velocity:h,seek:l,settled:S,useBaseFriction:y,useBaseDuration:p,useFriction:L,useDuration:v};return w}function Nn(t,n,r,s,i){const o=i.measure(10),e=i.measure(50),u=nt(.1,.99);let c=!1;function a(){return!(c||!t.reachedAny(r.get())||!t.reachedAny(n.get()))}function g(S){if(!a())return;const f=t.reachedMin(n.get())?"min":"max",m=O(t[f]-n.get()),h=r.get()-n.get(),p=u.constrain(m/e);r.subtract(h*p),!S&&O(h)<o&&(r.set(t.constrain(r.get())),s.useDuration(25).useBaseFriction())}function d(S){c=!S}return{shouldConstrain:a,constrain:g,toggleActive:d}}function kn(t,n,r,s,i){const o=nt(-n+t,0),e=d(),u=g(),c=l();function a(f,m){return lt(f,m)<=1}function g(){const f=e[0],m=j(e),h=e.lastIndexOf(f),p=e.indexOf(m)+1;return nt(h,p)}function d(){return r.map((f,m)=>{const{min:h,max:p}=o,y=o.constrain(f),v=!m,L=Nt(r,m);return v?p:L||a(h,y)?h:a(p,y)?p:y}).map(f=>parseFloat(f.toFixed(3)))}function l(){if(n<=t+i)return[o.max];if(s==="keepSnaps")return e;const{min:f,max:m}=u;return e.slice(f,m)}return{snapsContained:c,scrollContainLimit:u}}function Fn(t,n,r){const s=n[0],i=r?s-t:j(n);return{limit:nt(i,s)}}function Bn(t,n,r,s){const o=n.min+.1,e=n.max+.1,{reachedMin:u,reachedMax:c}=nt(o,e);function a(l){return l===1?c(r.get()):l===-1?u(r.get()):!1}function g(l){if(!a(l))return;const S=t*(l*-1);s.forEach(f=>f.add(S))}return{loop:g}}function zn(t){const{max:n,length:r}=t;function s(o){const e=o-n;return r?e/-r:0}return{get:s}}function Vn(t,n,r,s,i){const{startEdge:o,endEdge:e}=t,{groupSlides:u}=i,c=d().map(n.measure),a=l(),g=S();function d(){return u(s).map(m=>j(m)[e]-m[0][o]).map(O)}function l(){return s.map(m=>r[o]-m[o]).map(m=>-O(m))}function S(){return u(a).map(m=>m[0]).map((m,h)=>m+c[h])}return{snaps:a,snapsAligned:g}}function jn(t,n,r,s,i,o){const{groupSlides:e}=i,{min:u,max:c}=s,a=g();function g(){const l=e(o),S=!t||n==="keepSnaps";return r.length===1?[o]:S?l:l.slice(u,c).map((f,m,h)=>{const p=!m,y=Nt(h,m);if(p){const v=j(h[0])+1;return Xt(v)}if(y){const v=mt(o)-j(h)[0]+1;return Xt(v,j(h)[0])}return f})}return{slideRegistry:a}}function Rn(t,n,r,s,i){const{reachedAny:o,removeOffset:e,constrain:u}=s;function c(f){return f.concat().sort((m,h)=>O(m)-O(h))[0]}function a(f){const m=t?e(f):u(f),h=n.map((y,v)=>({diff:g(y-m,0),index:v})).sort((y,v)=>O(y.diff)-O(v.diff)),{index:p}=h[0];return{index:p,distance:m}}function g(f,m){const h=[f,f+r,f-r];if(!t)return f;if(!m)return c(h);const p=h.filter(y=>Ct(y)===m);return p.length?c(p):j(h)-r}function d(f,m){const h=n[f]-i.get(),p=g(h,m);return{index:f,distance:p}}function l(f,m){const h=i.get()+f,{index:p,distance:y}=a(h),v=!t&&o(h);if(!m||v)return{index:p,distance:f};const L=n[p]-y,w=f+g(L,0);return{index:p,distance:w}}return{byDistance:l,byIndex:d,shortcut:g}}function _n(t,n,r,s,i,o,e){function u(d){const l=d.distance,S=d.index!==n.get();o.add(l),l&&(s.duration()?t.start():(t.update(),t.render(1),t.update())),S&&(r.set(n.get()),n.set(d.index),e.emit("select"))}function c(d,l){const S=i.byDistance(d,l);u(S)}function a(d,l){const S=n.clone().set(d),f=i.byIndex(S.get(),l);u(f)}return{distance:c,index:a}}function $n(t,n,r,s,i,o,e,u){const c={passive:!0,capture:!0};let a=0;function g(S){if(!u)return;function f(m){if(new Date().getTime()-a>10)return;e.emit("slideFocusStart"),t.scrollLeft=0;const y=r.findIndex(v=>v.includes(m));Mt(y)&&(i.useDuration(0),s.index(y,0),e.emit("slideFocus"))}o.add(document,"keydown",d,!1),n.forEach((m,h)=>{o.add(m,"focus",p=>{(St(u)||u(S,p))&&f(h)},c)})}function d(S){S.code==="Tab"&&(a=new Date().getTime())}return{init:g}}function at(t){let n=t;function r(){return n}function s(c){n=e(c)}function i(c){n+=e(c)}function o(c){n-=e(c)}function e(c){return Mt(c)?c:c.get()}return{get:r,set:s,add:i,subtract:o}}function nn(t,n){const r=t.scroll==="x"?e:u,s=n.style;let i=null,o=!1;function e(l){return`translate3d(${l}px,0px,0px)`}function u(l){return`translate3d(0px,${l}px,0px)`}function c(l){if(o)return;const S=Ln(t.direction(l));S!==i&&(s.transform=r(S),i=S)}function a(l){o=!l}function g(){o||(s.transform="",n.getAttribute("style")||n.removeAttribute("style"))}return{clear:g,to:c,toggleActive:a}}function Gn(t,n,r,s,i,o,e,u,c){const g=ft(i),d=ft(i).reverse(),l=p().concat(y());function S(x,E){return x.reduce((T,M)=>T-i[M],E)}function f(x,E){return x.reduce((T,M)=>S(T,E)>0?T.concat([M]):T,[])}function m(x){return o.map((E,T)=>({start:E-s[T]+.5+x,end:E+n-.5+x}))}function h(x,E,T){const M=m(E);return x.map(A=>{const F=T?0:-r,R=T?r:0,_=T?"end":"start",$=M[A][_];return{index:A,loopPoint:$,slideLocation:at(-1),translate:nn(t,c[A]),target:()=>u.get()>$?F:R}})}function p(){const x=e[0],E=f(d,x);return h(E,r,!1)}function y(){const x=n-e[0]-1,E=f(g,x);return h(E,-r,!0)}function v(){return l.every(({index:x})=>{const E=g.filter(T=>T!==x);return S(E,n)<=.1})}function L(){l.forEach(x=>{const{target:E,translate:T,slideLocation:M}=x,A=E();A!==M.get()&&(T.to(A),M.set(A))})}function w(){l.forEach(x=>x.translate.clear())}return{canLoop:v,clear:w,loop:L,loopPoints:l}}function Hn(t,n,r){let s,i=!1;function o(c){if(!r)return;function a(g){for(const d of g)if(d.type==="childList"){c.reInit(),n.emit("slidesChanged");break}}s=new MutationObserver(g=>{i||(St(r)||r(c,g))&&a(g)}),s.observe(t,{childList:!0})}function e(){s&&s.disconnect(),i=!0}return{init:o,destroy:e}}function Kn(t,n,r,s){const i={};let o=null,e=null,u,c=!1;function a(){u=new IntersectionObserver(f=>{c||(f.forEach(m=>{const h=n.indexOf(m.target);i[h]=m}),o=null,e=null,r.emit("slidesInView"))},{root:t.parentElement,threshold:s}),n.forEach(f=>u.observe(f))}function g(){u&&u.disconnect(),c=!0}function d(f){return dt(i).reduce((m,h)=>{const p=parseInt(h),{isIntersecting:y}=i[p];return(f&&y||!f&&!y)&&m.push(p),m},[])}function l(f=!0){if(f&&o)return o;if(!f&&e)return e;const m=d(f);return f&&(o=m),f||(e=m),m}return{init:a,destroy:g,get:l}}function qn(t,n,r,s,i,o){const{measureSize:e,startEdge:u,endEdge:c}=t,a=r[0]&&i,g=f(),d=m(),l=r.map(e),S=h();function f(){if(!a)return 0;const y=r[0];return O(n[u]-y[u])}function m(){if(!a)return 0;const y=o.getComputedStyle(j(s));return parseFloat(y.getPropertyValue(`margin-${c}`))}function h(){return r.map((y,v,L)=>{const w=!v,I=Nt(L,v);return w?l[v]+g:I?l[v]+d:L[v+1][u]-y[u]}).map(O)}return{slideSizes:l,slideSizesWithGaps:S,startGap:g,endGap:d}}function Un(t,n,r,s,i,o,e,u,c){const{startEdge:a,endEdge:g,direction:d}=t,l=Mt(r);function S(p,y){return ft(p).filter(v=>v%y===0).map(v=>p.slice(v,v+y))}function f(p){return p.length?ft(p).reduce((y,v,L)=>{const w=j(y)||0,I=w===0,x=v===mt(p),E=i[a]-o[w][a],T=i[a]-o[v][g],M=!s&&I?d(e):0,A=!s&&x?d(u):0,F=O(T-A-(E+M));return L&&F>n+c&&y.push(v),x&&y.push(p.length),y},[]).map((y,v,L)=>{const w=Math.max(L[v-1]||0);return p.slice(w,y)}):[]}function m(p){return l?S(p,r):f(p)}return{groupSlides:m}}function Qn(t,n,r,s,i,o,e){const{align:u,axis:c,direction:a,startIndex:g,loop:d,duration:l,dragFree:S,dragThreshold:f,inViewThreshold:m,slidesToScroll:h,skipSnaps:p,containScroll:y,watchResize:v,watchSlides:L,watchDrag:w,watchFocus:I}=o,x=2,E=Pn(),T=E.measure(n),M=r.map(E.measure),A=An(c,a),F=A.measureSize(T),R=On(F),_=In(u,F),$=!d&&!!y,Z=d||!!y,{slideSizes:X,slideSizesWithGaps:K,startGap:q,endGap:st}=qn(A,T,M,r,Z,i),G=Un(A,F,h,d,T,M,q,st,x),{snaps:et,snapsAligned:ot}=Vn(A,_,T,M,G),U=-j(et)+j(K),{snapsContained:it,scrollContainLimit:ct}=kn(F,U,ot,y,x),B=$?it:ot,{limit:N}=Fn(U,B,d),Q=tn(mt(B),g,d),z=Q.clone(),P=ft(r),b=({dragHandler:rt,scrollBody:xt,scrollBounds:Et,options:{loop:gt}})=>{gt||Et.constrain(rt.pointerDown()),xt.seek()},D=({scrollBody:rt,translate:xt,location:Et,offsetLocation:gt,previousLocation:sn,scrollLooper:cn,slideLooper:un,dragHandler:an,animation:ln,eventHandler:Vt,scrollBounds:fn,options:{loop:jt}},Rt)=>{const _t=rt.settled(),dn=!fn.shouldConstrain(),$t=jt?_t:_t&&dn;$t&&!an.pointerDown()&&(ln.stop(),Vt.emit("settle")),$t||Vt.emit("scroll");const pn=Et.get()*Rt+sn.get()*(1-Rt);gt.set(pn),jt&&(cn.loop(rt.direction()),un.loop()),xt.to(gt.get())},C=Tn(s,i,()=>b(vt),rt=>D(vt,rt)),k=.68,H=B[Q.get()],J=at(H),W=at(H),Y=at(H),tt=at(H),ut=Cn(J,Y,W,tt,l,k),yt=Rn(d,B,U,N,tt),bt=_n(C,Q,z,ut,yt,tt,e),Ft=zn(N),Bt=pt(),on=Kn(n,r,e,m),{slideRegistry:zt}=jn($,y,B,ct,G,P),rn=$n(t,r,zt,bt,ut,Bt,e,I),vt={ownerDocument:s,ownerWindow:i,eventHandler:e,containerRect:T,slideRects:M,animation:C,axis:A,dragHandler:Dn(A,t,s,i,tt,wn(A,i),J,C,bt,ut,yt,Q,e,R,S,f,p,k,w),eventStore:Bt,percentOfView:R,index:Q,indexPrevious:z,limit:N,location:J,offsetLocation:Y,previousLocation:W,options:o,resizeHandler:Mn(n,e,i,r,A,v,E),scrollBody:ut,scrollBounds:Nn(N,Y,tt,ut,R),scrollLooper:Bn(U,N,Y,[J,Y,W,tt]),scrollProgress:Ft,scrollSnapList:B.map(Ft.get),scrollSnaps:B,scrollTarget:yt,scrollTo:bt,slideLooper:Gn(A,F,U,X,K,et,B,Y,r),slideFocus:rn,slidesHandler:Hn(n,e,L),slidesInView:on,slideIndexes:P,slideRegistry:zt,slidesToScroll:G,target:tt,translate:nn(A,n)};return vt}function Jn(){let t={},n;function r(a){n=a}function s(a){return t[a]||[]}function i(a){return s(a).forEach(g=>g(n,a)),c}function o(a,g){return t[a]=s(a).concat([g]),c}function e(a,g){return t[a]=s(a).filter(d=>d!==g),c}function u(){t={}}const c={init:r,emit:i,off:e,on:o,clear:u};return c}const Xn={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function Yn(t){function n(o,e){return Wt(o,e||{})}function r(o){const e=o.breakpoints||{},u=dt(e).filter(c=>t.matchMedia(c).matches).map(c=>e[c]).reduce((c,a)=>n(c,a),{});return n(o,u)}function s(o){return o.map(e=>dt(e.breakpoints||{})).reduce((e,u)=>e.concat(u),[]).map(t.matchMedia)}return{mergeOptions:n,optionsAtMedia:r,optionsMediaQueries:s}}function Zn(t){let n=[];function r(o,e){return n=e.filter(({options:u})=>t.optionsAtMedia(u).active!==!1),n.forEach(u=>u.init(o,t)),e.reduce((u,c)=>Object.assign(u,{[c.name]:c}),{})}function s(){n=n.filter(o=>o.destroy())}return{init:r,destroy:s}}function ht(t,n,r){const s=t.ownerDocument,i=s.defaultView,o=Yn(i),e=Zn(o),u=pt(),c=Jn(),{mergeOptions:a,optionsAtMedia:g,optionsMediaQueries:d}=o,{on:l,off:S,emit:f}=c,m=A;let h=!1,p,y=a(Xn,ht.globalOptions),v=a(y),L=[],w,I,x;function E(){const{container:P,slides:b}=v;I=(Lt(P)?t.querySelector(P):P)||t.children[0];const C=Lt(b)?I.querySelectorAll(b):b;x=[].slice.call(C||I.children)}function T(P){const b=Qn(t,I,x,s,i,P,c);if(P.loop&&!b.slideLooper.canLoop()){const D=Object.assign({},P,{loop:!1});return T(D)}return b}function M(P,b){h||(y=a(y,P),v=g(y),L=b||L,E(),p=T(v),d([y,...L.map(({options:D})=>D)]).forEach(D=>u.add(D,"change",A)),v.active&&(p.translate.to(p.location.get()),p.animation.init(),p.slidesInView.init(),p.slideFocus.init(z),p.eventHandler.init(z),p.resizeHandler.init(z),p.slidesHandler.init(z),p.options.loop&&p.slideLooper.loop(),I.offsetParent&&x.length&&p.dragHandler.init(z),w=e.init(z,L)))}function A(P,b){const D=G();F(),M(a({startIndex:D},P),b),c.emit("reInit")}function F(){p.dragHandler.destroy(),p.eventStore.clear(),p.translate.clear(),p.slideLooper.clear(),p.resizeHandler.destroy(),p.slidesHandler.destroy(),p.slidesInView.destroy(),p.animation.destroy(),e.destroy(),u.clear()}function R(){h||(h=!0,u.clear(),F(),c.emit("destroy"),c.clear())}function _(P,b,D){!v.active||h||(p.scrollBody.useBaseFriction().useDuration(b===!0?0:v.duration),p.scrollTo.index(P,D||0))}function $(P){const b=p.index.add(1).get();_(b,P,-1)}function Z(P){const b=p.index.add(-1).get();_(b,P,1)}function X(){return p.index.add(1).get()!==G()}function K(){return p.index.add(-1).get()!==G()}function q(){return p.scrollSnapList}function st(){return p.scrollProgress.get(p.location.get())}function G(){return p.index.get()}function et(){return p.indexPrevious.get()}function ot(){return p.slidesInView.get()}function U(){return p.slidesInView.get(!1)}function it(){return w}function ct(){return p}function B(){return t}function N(){return I}function Q(){return x}const z={canScrollNext:X,canScrollPrev:K,containerNode:N,internalEngine:ct,destroy:R,off:S,on:l,emit:f,plugins:it,previousScrollSnap:et,reInit:m,rootNode:B,scrollNext:$,scrollPrev:Z,scrollProgress:st,scrollSnapList:q,scrollTo:_,selectedScrollSnap:G,slideNodes:Q,slidesInView:ot,slidesNotInView:U};return M(n,r),setTimeout(()=>c.emit("init"),0),z}ht.globalOptions=void 0;function kt(t={},n=[]){const r=Gt(t),s=Gt(n);let i=r?t.value:t,o=s?n.value:n;const e=Ht(),u=Ht();function c(){u.value&&u.value.reInit(i,o)}return Yt(()=>{!vn()||!e.value||(ht.globalOptions=kt.globalOptions,u.value=ht(e.value,i,o))}),mn(()=>{u.value&&u.value.destroy()}),r&&Kt(t,a=>{Ot(i,a)||(i=a,c())}),s&&Kt(n,a=>{xn(o,a)||(o=a,c())}),[e,u]}kt.globalOptions=void 0;const[Wn,te]=yn(({opts:t,orientation:n,plugins:r},s)=>{const{locale:i}=gn(),[o,e]=kt({direction:i.value==="ar"?"rtl":"ltr",...t,axis:n==="horizontal"?"x":"y"},r);function u(){var l;(l=e.value)==null||l.scrollPrev()}function c(){var l;(l=e.value)==null||l.scrollNext()}const a=qt(!1),g=qt(!1);function d(l){a.value=(l==null?void 0:l.canScrollNext())||!1,g.value=(l==null?void 0:l.canScrollPrev())||!1}return Yt(()=>{var l,S,f;e.value&&((l=e.value)==null||l.on("init",d),(S=e.value)==null||S.on("reInit",d),(f=e.value)==null||f.on("select",d),s("init-api",e.value))}),{carouselRef:o,carouselApi:e,canScrollPrev:g,canScrollNext:a,scrollPrev:u,scrollNext:c,orientation:n}});function en(){const t=te();if(!t)throw new Error("useCarousel must be used within a <Carousel />");return t}const re=At({__name:"Carousel",props:{opts:{},plugins:{},orientation:{default:"horizontal"},class:{}},emits:["init-api"],setup(t,{expose:n,emit:r}){const s=t,i=r,{canScrollNext:o,canScrollPrev:e,carouselApi:u,carouselRef:c,orientation:a,scrollNext:g,scrollPrev:d}=Wn(s,i);n({canScrollNext:o,canScrollPrev:e,carouselApi:u,carouselRef:c,orientation:a,scrollNext:g,scrollPrev:d});function l(S){const f=s.orientation==="vertical"?"ArrowUp":"ArrowLeft",m=s.orientation==="vertical"?"ArrowDown":"ArrowRight";if(S.key===f){S.preventDefault(),d();return}S.key===m&&(S.preventDefault(),g())}return(S,f)=>(wt(),Dt("div",{class:Zt(V(Tt)("relative",s.class)),role:"region","aria-roledescription":"carousel",tabindex:"0",onKeydown:l},[Pt(S.$slots,"default",{canScrollNext:V(o),canScrollPrev:V(e),carouselApi:V(u),carouselRef:V(c),orientation:V(a),scrollNext:V(g),scrollPrev:V(d)})],34))}}),se=At({inheritAttrs:!1,__name:"CarouselContent",props:{class:{}},setup(t){const n=t,{carouselRef:r,orientation:s}=en();return(i,o)=>(wt(),Dt("div",{ref_key:"carouselRef",ref:r,class:"overflow-hidden"},[hn("div",Sn({class:V(Tt)("flex",V(s)==="horizontal"?"":"-mt-4 flex-col",n.class)},i.$attrs),[Pt(i.$slots,"default")],16)],512))}}),ie=At({__name:"CarouselItem",props:{class:{}},setup(t){const n=t,{orientation:r}=en();return(s,i)=>(wt(),Dt("div",{role:"group","aria-roledescription":"slide",class:Zt(V(Tt)("min-w-0 shrink-0 grow-0 basis-full",V(r)==="horizontal"?"pl-4":"pt-4",n.class))},[Pt(s.$slots,"default")],2))}});export{se as _,ie as a,re as b,en as u};
