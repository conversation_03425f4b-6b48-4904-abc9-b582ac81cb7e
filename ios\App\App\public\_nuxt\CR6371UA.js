import{Skeleton as q}from"./BaodNYLQ.js";import{i as F,j as H,l as m,X as O,k as P,r as I,d,e as r,c as p,p as x,a as e,w as n,q as t,Y as T,o as s,$ as W,g as X,W as Y,t as i,h as N}from"./C3gyeM1K.js";import G from"./CuUhnQhf.js";import J from"./D-ucjfQv.js";import{_ as K}from"./DAA1LH-g.js";import{_ as M}from"./JNWsvxxg.js";import{_ as Q}from"./BsHCAE3W.js";import{_ as U}from"./BoqtTPyX.js";import{_ as Z}from"./USAFFp3X.js";import{_ as ee}from"./6a0mKJmP.js";import{_ as te}from"./CkX-UBEQ.js";import"./jAU0Cazi.js";import"./BispfOyS.js";import"./D1vxl8R8.js";import"./D8XAHdeJ.js";import"./ClItKS3j.js";import"./BCJZ1_ur.js";import"./CRC5xmnh.js";import"./C0H2RQFE.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";const se={class:"flex w-full min-h-screen overflow-auto bg-body"},oe={class:"flex w-full flex-col gap-4 h-full overflow-y-auto"},re={class:"flex w-full gap-4"},ae={key:0,class:"flex"},ne={class:"flex w-full bg-sky-50 p-4 rounded-md gap-2 items-center"},le={class:"flex p-2 rounded-full bg-green-1000 justify-center items-center"},ie={class:"text-base font-semibold"},ce={key:1,class:"text-xl font-bold"},de={key:0,class:"flex w-full gap-3"},me={key:1,class:"flex gap-4 items-center"},pe={class:"flex gap-4 p-6 rounded-lg border flex-col w-1/2 justify-center items-center"},ue={class:"text-base font-bold max-sm:text-sm text-center"},_e={class:"flex gap-4 p-6 rounded-lg border flex-col w-1/2 justify-center items-center"},fe={class:"text-base font-bold max-sm:text-sm text-center"},Je=F({__name:"details",async setup(ve){let u,y;const B=H(),j=m(()=>{var o;return(o=B.params)==null?void 0:o.orderId}),{data:z,error:h,status:R}=([u,y]=O(()=>T(`/orders/${j.value}`)),u=await u,y(),u);h.value&&console.log("Error on fetching order ",h.value);const l=m(()=>z.value),_=m(()=>R.value!=="success"),{locale:S}=P(),V=m(()=>S.value==="ar"),g=I(!1),w=I(!1),A=m(()=>["received","completed"].includes(l.value.status));return(o,a)=>{var C;const f=q,v=X,D=G,E=J,k=Q,$=K,b=U,L=M;return s(),d("div",se,[r("div",oe,[e($,null,{default:n(()=>[e(k,{class:"gap-6"},{default:n(()=>{var c;return[t(_)?(s(),p(f,{key:0,class:"h-6 w-1/2"})):(s(),d(W,{key:1},[r("div",re,[e(D,{class:"flex flex-row gap-2 items-center text-lg font-bold",to:"/my/orders?tab=history"},{default:n(()=>[e(v,{name:"lucide:chevron-right",class:Y(["text-2xl",{"rotate-180":!t(V)}])},null,8,["class"]),r("span",null,i(o.$t("orders.your-order-number",{number:t(l).orderId})),1)]),_:1}),e(E,{status:t(l).status},null,8,["status"])]),t(A)?(s(),d("div",ae,[r("div",ne,[r("div",le,[e(v,{name:"ui:received",class:"text-white",size:"16px"})]),r("span",ie,i(o.$t("orders.received-at",{date:(c=t(l))==null?void 0:c.createdAt})),1)])])):x("",!0)],64))]}),_:1})]),_:1}),((C=t(l))==null?void 0:C.status)!=="draft"?(s(),p($,{key:0},{default:n(()=>[e(k,null,{default:n(()=>[t(_)?(s(),p(f,{key:0,class:"h-6 w-1/2"})):(s(),d("span",ce,i(o.$t("orders.review-experience-text")),1))]),_:1}),e(L,null,{default:n(()=>[t(_)?(s(),d("div",de,[e(f,{class:"h-24 w-1/2"}),e(f,{class:"h-24 w-1/2"})])):(s(),d("div",me,[r("div",pe,[e(v,{name:"ui:order-review",size:"50px",class:"text-primary-600"}),r("span",ue,i(o.$t("orders.help-people-buy")),1),e(b,{class:"w-full mt-2",onClick:a[0]||(a[0]=c=>g.value=!0)},{default:n(()=>[N(i(o.$t("orders.review-experience-btn")),1)]),_:1})]),r("div",_e,[e(v,{name:"ui:delivery-truck",size:"50px",class:"text-primary-600"}),r("span",fe,i(o.$t("orders.review-delivered-experience-text")),1),e(b,{class:"w-full mt-2",onClick:a[1]||(a[1]=c=>w.value=!0)},{default:n(()=>[N(i(o.$t("orders.review-delivered-experience-btn")),1)]),_:1})])]))]),_:1})]),_:1})):x("",!0),e(Z,{loading:t(_),order:t(l)},null,8,["loading","order"])]),t(g)?(s(),p(ee,{key:0,"onClose:modal":a[2]||(a[2]=c=>g.value=!1)})):x("",!0),t(w)?(s(),p(te,{key:1,"shipping-carrier-id":t(l).shippingCarrierId,"onClose:modal":a[3]||(a[3]=c=>w.value=!1)},null,8,["shipping-carrier-id"])):x("",!0)])}}});export{Je as default};
