import{u as c}from"./C0-TGi1L.js";import{S as m}from"./ClItKS3j.js";import{r as u,O as h,l as f,i as v,E as k,A as p,n as w,U as x,af as T}from"./C3gyeM1K.js";const d="data-reka-collection-item";function j(A={}){const{key:M="",isProvider:E=!1}=A,s=`${M}CollectionProvider`;let e;if(E){const o=u(new Map);e={collectionRef:u(),itemMap:o},x(s,e)}else e=h(s);const y=(o=!1)=>{const t=e.collectionRef.value;if(!t)return[];const r=Array.from(t.querySelectorAll(`[${d}]`)),l=Array.from(e.itemMap.value.values()).sort((i,n)=>r.indexOf(i.ref)-r.indexOf(n.ref));return o?l:l.filter(i=>i.ref.dataset.disabled!=="")},C=v({name:"CollectionSlot",setup(o,{slots:t}){const{primitiveElement:r,currentElement:a}=c();return w(a,()=>{e.collectionRef.value=a.value}),()=>p(m,{ref:r},t)}}),I=v({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(o,{slots:t,attrs:r}){const{primitiveElement:a,currentElement:l}=c();return k(i=>{if(l.value){const n=T(l.value);e.itemMap.value.set(n,{ref:l.value,value:o.value}),i(()=>e.itemMap.value.delete(n))}}),()=>p(m,{...r,[d]:"",ref:a},t)}}),R=f(()=>Array.from(e.itemMap.value.values())),S=f(()=>e.itemMap.value.size);return{getItems:y,reactiveItems:R,itemMapSize:S,CollectionSlot:C,CollectionItem:I}}export{j as u};
