import{c as n}from"./D1vxl8R8.js";import{c as a}from"./jAU0Cazi.js";import{i as s,d,s as i,W as c,q as r,o as l}from"./C3gyeM1K.js";const v=s({__name:"Badge",props:{variant:{},class:{}},setup(t){const o=t;return(e,f)=>(l(),d("div",{class:c(r(a)(r(u)({variant:e.variant}),o.class))},[i(e.$slots,"default")],2))}}),u=n("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent px-6 font-bold",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",selected:"bg-[#F3F9FC] rounded-2xl text-gray-600 border-none px-3 shadow-sm"}},defaultVariants:{variant:"default"}});export{v as _};
