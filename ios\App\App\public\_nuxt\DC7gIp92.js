import{i as V,r as S,l as s,u as z,k as _,d as h,e as o,a as n,W as B,q as c,t as r,w as x,K as N,ab as D,g as K,o as g,$ as O,a0 as R}from"./C3gyeM1K.js";import{b as T,_ as q,a as A}from"./ojFwGwqF.js";import"./jAU0Cazi.js";import"./D3IGe8e3.js";import"./ClItKS3j.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";import"./BYbD1TwY.js";import"./C0-TGi1L.js";import"./C2RgRyPS.js";import"./BRIKaOVL.js";import"./BK9Yhb3N.js";import"./B5rxJs06.js";import"./FUKBWQwh.js";const E={class:"flex flex-col gap-4 px-4 pt-6"},G={class:"flex w-full justify-end"},L={class:"relative flex flex-col w-full gap-2 px-4 justify-center items-center"},U={class:"flex flex-col w-full py-4 gap-4 justify-center items-center"},W={for:"phone",class:"text-xl font-bold"},H={class:"flex flex-col gap-1"},J={class:"flex items-center w-full justify-center text-gray-500 text-sm gap-2"},M={dir:"ltr"},Q={class:"flex items-center w-full justify-center gap-2 text-sm"},X={class:"text-gray-500"},fe=V({__name:"otp",emits:["set:step"],setup(Y,{expose:v,emit:y}){const i=y,l=S([]),b=s(()=>l.value.join("")),a=z(),{t:$}=_(),{locale:w}=_(),I=s(()=>w.value==="ar"),u=s(()=>{var e;return(e=a.forgotForm)==null?void 0:e.phone}),j=s(()=>{var e,t;return(t=(e=a.forgotForm)==null?void 0:e.userData)==null?void 0:t.userId}),P=s(()=>{var e,t;return console.log("authStore.forgotForm",a.forgotForm),["+",(e=u.value)==null?void 0:e.code,(t=u.value)==null?void 0:t.number].join("")}),m=async()=>a.resetVerifyPhoneOTP({code:b.value,userId:j.value}).then(()=>{D.success($("form.update-password-now")),i("set:step",3)});return v({submitForm:m}),(e,t)=>{const f=K,k=A,C=q,F=T;return g(),h("div",E,[o("div",G,[o("button",{class:"p-1 hover:bg-gray-200 rounded-lg flex items-center justify-center",onClick:t[0]||(t[0]=p=>i("set:step",1))},[n(f,{name:"lucide:chevron-right",class:B({"rotate-180":c(I)}),size:"30px"},null,8,["class"])])]),o("div",L,[n(f,{name:"ui:phone-otp",size:"120px"}),o("div",U,[o("label",W,r(e.$t("form.verify-phone-title")),1),n(F,{id:"pin-input",modelValue:c(l),"onUpdate:modelValue":t[1]||(t[1]=p=>N(l)?l.value=p:null),type:"number",onComplete:m},{default:x(()=>[n(C,null,{default:x(()=>[(g(),h(O,null,R(4,(p,d)=>n(k,{key:`otp-${d}`,index:d},null,8,["index"])),64))]),_:1})]),_:1},8,["modelValue"]),o("div",H,[o("div",J,[o("span",null,r(e.$t("form.verify-phone-text",{phone:""})),1),o("span",M,r(c(P)),1)]),o("div",Q,[o("span",X,r(e.$t("form.change-otp-phone-title")),1),o("button",{class:"text-primary-500 hover:underline cursor-pointer",onClick:t[2]||(t[2]=()=>i("set:step",1))},r(e.$t("form.change")),1)])])])])])}}});export{fe as default};
