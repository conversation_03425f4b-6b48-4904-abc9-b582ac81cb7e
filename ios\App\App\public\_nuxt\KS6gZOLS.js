import{_ as k,u as C}from"./BD9sJOps.js";import{i as w,k as $,l as h,r as v,d as n,a as s,q as o,w as r,$ as m,o as l,e,t as p,a0 as q,W as c,g as B}from"./C3gyeM1K.js";import{_ as z}from"./DAA1LH-g.js";import{_ as I}from"./JNWsvxxg.js";import{_ as L}from"./BsHCAE3W.js";import"./CuUhnQhf.js";import"./BaodNYLQ.js";import"./jAU0Cazi.js";const N={class:"text-2xl font-bold"},S={class:"text-lg text-gray-600"},V=["onClick"],M=w({__name:"faq",setup(j){const{t:d}=$(),u=h(()=>C().buildSinglePage(d("faq.title"))),a=v(0);return(i,_)=>{const f=k,g=L,x=B,b=I,y=z;return l(),n(m,null,[s(f,{links:o(u),class:"!border-0 !shadow-none"},null,8,["links"]),s(y,{class:"my-6"},{default:r(()=>[s(g,{class:"text-center py-12 gap-6"},{default:r(()=>[e("h1",N,p(i.$t("faq.title")),1),e("p",S,p(i.$t("faq.sub-title")),1)]),_:1}),s(b,{class:"py-4 flex-col flex gap-6 pb-12"},{default:r(()=>[(l(!0),n(m,null,q(Array(10),(A,t)=>(l(),n("div",{key:`faq-${t}`,class:"flex w-full flex-col"},[e("button",{class:c(["flex p-4 rounded-lg text-start gap-2 justify-between",[o(a)===t?"text-primary-600 font-bold bg-sky-50":"bg-gray-100"]]),onClick:D=>a.value=t},[_[0]||(_[0]=e("span",{class:"text-sm"}," هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة ",-1)),s(x,{name:"lucide:chevron-down",class:c({"rotate-180":o(a)===t}),size:"20px"},null,8,["class"])],10,V),e("div",{class:c(["flex p-4 rounded-lg text-sm text-gray-600 transition-all duration-200 ease-in-out",[o(a)===t?"":"overflow-hidden h-0 !p-0"]])}," هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة twessds هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة twessds ",2)]))),128))]),_:1})]),_:1})],64)}}});export{M as default};
