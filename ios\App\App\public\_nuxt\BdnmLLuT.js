import{i as E,u as N,k as $,ak as P,r as k,l as _,ab as R,L as Z,d as D,e as t,a as s,W as u,q as o,w as r,t as c,g as L,o as K,h as C,a1 as F}from"./C3gyeM1K.js";import{_ as M,a as W,b as G}from"./BzSOFl6X.js";import{u as H,t as J,o as O,s as z,F as Q}from"./ZoTeemxs.js";import{_ as U}from"./CjR7N8Rt.js";import{_ as X}from"./CvMKnfiC.js";import"./ClItKS3j.js";import"./jAU0Cazi.js";import"./DSkQD3L1.js";import"./BK9Yhb3N.js";import"./C2RgRyPS.js";import"./BBuxlZld.js";import"./CRC5xmnh.js";const Y={class:"flex flex-col gap-4 py-4 min-h-96"},ee={class:"flex w-full justify-end"},se={class:"flex absolute top-7 end-4"},te={class:"flex flex-col gap-3 text-sm"},oe={class:"font-semibold"},ae={class:"text-sm text-gray-500"},le={class:"flex items-center gap-2"},re={class:"flex items-center gap-2"},ne={class:"flex items-center gap-2"},ce={class:"flex items-center gap-2"},ie={class:"flex absolute top-7 end-4"},$e=E({__name:"reset",emits:["close:modal","set:step"],setup(ue,{expose:I,emit:S}){const w=N(),{t:i}=$(),T=P(),A=S,m=k(!1),d=k(!1),{locale:V}=$(),j=_(()=>V.value==="ar"),f=H({validationSchema:J(O({password:z().min(7,i("error.required")).regex(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/,{message:i("error.password-strength")}),passwordConfirmation:z().min(7,i("error.required"))}).refine(e=>e.password===e.passwordConfirmation,{path:["passwordConfirmation"],message:i("error.passwords-not-match")})),initialValues:{password:"",passwordConfirmation:""}}),l=_(()=>{var a;if(!f.values)return;const e=(a=f.values)==null?void 0:a.password;return{"rule-1":/[a-z]/.test(e)&&/[A-Z]/.test(e),"rule-2":e.length>=8,"rule-3":/\d/.test(e),"rule-4":/[^a-zA-Z0-9]/.test(e)}}),q=_(()=>{var e,a;return(a=(e=w.forgotForm)==null?void 0:e.userData)==null?void 0:a.userId}),B=f.handleSubmit(async e=>w.resetPassword({...e,userId:q.value}).then(()=>{R.success(i("form.password-changed-success")),Z(()=>{T.push("/?auth=login")})}));return I({submitForm:B}),(e,a)=>{const n=L,h=U,g=X,x=W,y=G,b=M,v=Q;return K(),D("form",Y,[t("div",ee,[t("button",{class:"p-1 hover:bg-gray-200 rounded-lg flex items-center justify-center",onClick:a[0]||(a[0]=p=>A("set:step",1))},[s(n,{name:"lucide:chevron-right",class:u({"rotate-180":o(j)}),size:"30px"},null,8,["class"])])]),s(v,{name:"password"},{default:r(({componentField:p})=>[s(b,{class:"w-full relative"},{default:r(()=>[s(h,{class:"font-bold"},{default:r(()=>[C(c(e.$t("form.new-password"))+"* ",1)]),_:1}),s(x,null,{default:r(()=>[s(g,F({type:o(m)?"text":"password",placeholder:e.$t("form.new-password")},p),null,16,["type","placeholder"])]),_:2},1024),s(y),t("div",se,[t("button",{class:"p-1",onClick:a[1]||(a[1]=()=>m.value=!o(m))},[s(n,{name:o(m)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1}),t("div",te,[t("span",oe,c(e.$t("form.password-hint-title")),1),t("ul",ae,[t("li",le,[s(n,{class:u({"text-green-600":o(l)["rule-1"]}),name:o(l)["rule-1"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),t("span",null,c(e.$t("password.rule-1")),1)]),t("li",re,[s(n,{class:u({"text-green-600":o(l)["rule-2"]}),name:o(l)["rule-2"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),t("span",null,c(e.$t("password.rule-2")),1)]),t("li",ne,[s(n,{class:u({"text-green-600":o(l)["rule-3"]}),name:o(l)["rule-3"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),t("span",null,c(e.$t("password.rule-3")),1)]),t("li",ce,[s(n,{class:u({"text-green-600":o(l)["rule-4"]}),name:o(l)["rule-4"]?"lucide:circle-check-big":"lucide:x-circle"},null,8,["class","name"]),t("span",null,c(e.$t("password.rule-4")),1)])])]),s(v,{name:"passwordConfirmation"},{default:r(({componentField:p})=>[s(b,{class:"relative"},{default:r(()=>[s(h,{class:"font-bold"},{default:r(()=>[C(c(e.$t("form.confirm-new-password"))+"* ",1)]),_:1}),s(x,null,{default:r(()=>[s(g,F({type:o(d)?"text":"password",placeholder:e.$t("form.confirm-new-password")},p),null,16,["type","placeholder"])]),_:2},1024),s(y),t("div",ie,[t("button",{class:"p-1",onClick:a[2]||(a[2]=()=>d.value=!o(d))},[s(n,{name:o(d)?"lucide:eye":"lucide:eye-off",size:"18px"},null,8,["name"])])])]),_:2},1024)]),_:1})])}}});export{$e as default};
