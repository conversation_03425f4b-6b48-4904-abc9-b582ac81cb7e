import{_ as D}from"./CvMKnfiC.js";import{_ as H,a as q,b as A}from"./BzSOFl6X.js";import{i as E,k as O,r as v,l as R,c as f,w as a,q as n,o as d,e as t,a as r,d as x,h as b,t as l,Z as g,p as h,a1 as Z,$ as z,a2 as G}from"./C3gyeM1K.js";import{u as J,t as K,o as L,s as i,p as Q,n as U,F as W}from"./ZoTeemxs.js";import{_ as X}from"./XInyCH8Q.js";import{_ as Y}from"./BoqtTPyX.js";import{_ as ee}from"./C0H2RQFE.js";import{u as oe}from"./BCJZ1_ur.js";const te={class:"flex p-4 flex-col gap-2"},se={class:"flex p-4 gap-2 items-center rounded-lg bg-sky-50"},ae={class:"flex"},ne={class:"font-bold"},re={class:"flex flex-col gap-2"},le={for:"balance",class:"text-sm font-bold"},ce={class:"relative flex w-full px-4 border-gray-200 border rounded-lg h-11 mb-4 items-center"},de={class:"absolute top-9 start-0"},me={class:"flex border-s ps-4 h-full items-center"},ie={class:"flex justify-end w-full gap-4 mt-4 border-t border-gray-200 pt-4"},pe={key:1,class:"flex flex-grow justify-end gap-2"},$e=E({__name:"form",props:{user:{}},emits:["close:modal"],setup(ue,{emit:$}){const y=$,{priceFormat:w,currency:k}=oe(),{t:s}=O(),c=v("balance"),p=v(null),F=R(()=>c.value=="balance"?s("wallet.add-balance-title"):s("wallet.payment-method-title")),u=J({validationSchema:K(L({amount:Q(e=>{if(!(e===""||e===null||e===void 0))return e},U({required_error:s("error.balance")}).min(.01,s("error.balance"))),cardNumber:i().regex(/^\d{16}$/,s("error.card-number")),expiryDate:i().regex(/^(0[1-9]|1[0-2])\/\d{2}$/,s("error.expiry")),cvv:i().regex(/^\d{3}$/,s("error.cvv")),cardHolder:i().min(1,s("error.card-holder"))})),initialValues:{amount:0,cardNumber:null,expiryDate:null,cvv:null,cardHolder:null}}),C=async()=>{const{$api:e}=G();e("/wallet/payment-methods",{method:"POST",body:{amount:u.values.amount,paymentMethodId:4}}).then(o=>{c.value="cards",p.value=o,console.log("Payment method",o)}).catch(o=>{console.error("Error on saving payment method",o)}).finally(()=>{u.resetForm()})},T=()=>{p.value=null,c.value="balance"};return(e,o)=>{const N=D,B=q,V=A,M=H,I=W,P=X,_=Y,S=ee;return d(),f(S,{title:n(F),dismissible:!1,onClose:o[2]||(o[2]=m=>y("close:modal"))},{body:a(()=>{var m;return[t("div",te,[n(c)==="balance"?(d(),x(z,{key:0},[t("div",se,[t("span",ae,l(e.$t("wallet.total-balance")),1),t("span",ne,l(n(w)(((m=e.user.wallet)==null?void 0:m.value)||0)),1)]),o[3]||(o[3]=t("div",{class:"flex w-full border-dashed border border-gray-100 my-2"},null,-1)),t("div",re,[t("label",le,l(e.$t("wallet.add-price"))+"*",1),t("div",ce,[r(I,{name:"amount"},{default:a(({componentField:j})=>[r(M,{class:"w-full flex flex-col"},{default:a(()=>[r(B,null,{default:a(()=>[r(N,Z({min:.01,minlength:.01,step:"0.01",pattern:"^\\d+(\\.\\d{1,2})?$",class:"outline-none flex flex-grow no-spinner border-none px-0",type:"number"},j),null,16)]),_:2},1024),t("div",de,[r(V)])]),_:2},1024)]),_:1}),t("div",me,l(n(k)),1)])])],64)):h("",!0),n(c)==="cards"?(d(),f(P,{key:1,params:n(p)},null,8,["params"])):h("",!0)])]}),footer:a(()=>[t("div",ie,[r(_,{variant:"outline",onClick:o[0]||(o[0]=m=>y("close:modal"))},{default:a(()=>[b(l(e.$t("form.cancel")),1)]),_:1}),n(c)==="balance"?(d(),f(_,{key:0,disabled:!n(u).isFieldValid("amount"),onClick:o[1]||(o[1]=g(()=>C(),["prevent"]))},{default:a(()=>[b(l(e.$t("wallet.next")),1)]),_:1},8,["disabled"])):(d(),x("div",pe,[r(_,{onClick:g(T,["prevent"])},{default:a(()=>[b(l(e.$t("form.back")),1)]),_:1})]))])]),_:1},8,["title"])}}});export{$e as _};
