import{_ as f}from"./DQHUSZRG.js";import{_ as h}from"./Bjqrwoww.js";import{_ as w}from"./BrbpxSLN.js";import{_ as x}from"./BuJZm6eP.js";import{d as v}from"./BBuxlZld.js";import{i as k,j as y,ak as b,r as m,l as A,d as S,a as r,W as e,q as o,o as g,L as H,_ as $}from"./C3gyeM1K.js";import"./CuUhnQhf.js";import"./BQWT7oJn.js";import"./CUNFy6WN.js";import"./D3IGe8e3.js";import"./jAU0Cazi.js";import"./D_nR533G.js";import"./DYRsuAWe.js";import"./CRC5xmnh.js";import"./FUKBWQwh.js";import"./C2RgRyPS.js";import"./ClItKS3j.js";import"./DwXv0R8y.js";import"./B12VHTgT.js";import"./Bqx9KQ63.js";import"./C0-TGi1L.js";import"./DDFqsyHi.js";import"./BK9Yhb3N.js";import"./BRIKaOVL.js";import"./CB0C_qKx.js";import"./C00tNdvy.js";import"./B5rxJs06.js";import"./ytjl1pT4.js";import"./CY42fsWK.js";import"./DSkQD3L1.js";import"./CoTDbsFa.js";import"./BaodNYLQ.js";import"./BispfOyS.js";import"./D1vxl8R8.js";import"./BoqtTPyX.js";import"./D8XAHdeJ.js";import"./BrsQk_-I.js";import"./UTzmdM4z.js";import"./Tzk92Vbf.js";const q=k({__name:"index",setup(L){const a=y(),l=b(),s=m(0),n=A(()=>s.value>0),t=m(!1);v("scroll",()=>{t.value=s.value<window.scrollY,H(()=>s.value=window.scrollY)});const p=(i=null)=>{l.push({path:a.path,query:{...a.query,drawer:i}})};return(i,j)=>{const c=f,_=h,u=w,d=x;return g(),S("header",{class:e([{"-translate-y-8":o(t),"has-scrolling":o(n)},"flex w-full flex-col transition-transform duration-100 ease-in-out justify-center items-center shadow-lg pb-3 bg-white z-10 sm:pb-0 sticky top-0"])},[r(c,{class:e({"-translate-y-8":o(t)})},null,8,["class"]),r(_,{"has-scroll":o(t),"onOpen:drawer":p},null,8,["has-scroll"]),r(u,{"onOpen:drawer":p}),r(d,{class:e(["menu-list",{visible:!o(n)}])},null,8,["class"])],2)}}}),xo=$(q,[["__scopeId","data-v-7539ad34"]]);export{xo as default};
