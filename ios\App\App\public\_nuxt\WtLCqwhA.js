import{i as p,r as d,d as l,e as t,$ as _,a0 as u,W as h,q as f,o as s,a as i,w as x,g as b,t as v}from"./C3gyeM1K.js";import y from"./CuUhnQhf.js";import{d as g}from"./BBuxlZld.js";const w={class:"fixed bottom-0 z-20 min-w-full"},B={class:"flex justify-evenly items-center px-2 pb-4 border-t w-full border-gray-200 bg-white text-gray-400"},L={class:"flex h-8"},S={class:"text-sm"},q=p({__name:"mobile",setup(z){const n=[{title:"mobile.nav-home-title",icon:"lucide:house",path:"/"},{title:"mobile.nav-categories-title",icon:"lucide:layout-grid",path:"/categories/all"},{title:"mobile.nav-search-title",icon:"lucide:search",path:"/search?q="},{title:"mobile.nav-cart-title",icon:"lucide:shopping-cart",path:"/cart"},{title:"mobile.nav-profile-title",icon:"lucide:circle-user-round",path:"my/profile"}],o=d(!0);let a=0;return g("scroll",()=>{o.value=window.scrollY<a,a=window.scrollY}),(r,N)=>{const c=b,m=y;return s(),l("div",{class:h(["w-full h-20 hidden transition-all max-sm:flex delay-75",{"translate-y-20 h-4":!f(o)}])},[t("div",w,[t("div",B,[(s(),l(_,null,u(n,e=>i(m,{key:e.title,class:"min-h-9 flex flex-col items-center border-t-[3px] border-transparent justify-center px-2 gap-1 pt-2 -top-px relative",to:e.path,"exact-active-class":"text-primary-600 !border-primary-600"},{default:x(()=>[t("div",L,[i(c,{name:e.icon,size:"25px"},null,8,["name"])]),t("span",S,v(r.$t(e.title)),1)]),_:2},1032,["to"])),64))])])],2)}}});export{q as _};
