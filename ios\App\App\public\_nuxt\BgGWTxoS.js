import{_ as y,a as k,b as w}from"./BzSOFl6X.js";import{u as C,t as S,o as v,s as T,F as B}from"./ZoTeemxs.js";import{_ as V}from"./CjR7N8Rt.js";import{_ as x}from"./CvMKnfiC.js";import{_ as I}from"./BoqtTPyX.js";import{_ as N}from"./C0H2RQFE.js";import{i as q,u as E,k as L,ab as M,L as j,c as A,w as e,o as D,e as K,a as o,h as c,t as i,q as P,a1 as U}from"./C3gyeM1K.js";const z={class:"flex w-full mt-2"},Y=q({__name:"email",emits:["close:modal"],setup(G,{emit:_}){const s=E(),{t}=L(),n=C({validationSchema:S(v({email:T().email(t("error.email")).min(2,t("error.required"))})),initialValues:{email:""}}),m=_,f=n.handleSubmit(async()=>s.changeEmail(n.values.email).then(()=>(s.fetchUser(),M.success(t("form.email-changed-success")),j(()=>m("close:modal")))).catch(()=>{throw error.value}));return(a,l)=>{const u=V,p=x,d=k,h=w,$=y,F=B,g=I,b=N;return D(),A(b,{title:a.$t("form.change-email-title"),dismissible:!0,onClose:l[0]||(l[0]=r=>m("close:modal"))},{body:e(()=>[o(F,{name:"email"},{default:e(({componentField:r})=>[o($,{class:"px-4 w-full"},{default:e(()=>[o(u,{class:"font-bold"},{default:e(()=>[c(i(a.$t("form.email"))+"* ",1)]),_:1}),o(d,null,{default:e(()=>[o(p,U({type:"email",placeholder:a.$t("form.email")},r),null,16,["placeholder"])]),_:2},1024),o(h)]),_:2},1024)]),_:1})]),footer:e(()=>[K("div",z,[o(g,{class:"w-full",onClick:P(f)},{default:e(()=>[c(i(a.$t("form.change-email-title")),1)]),_:1},8,["onClick"])])]),_:1},8,["title"])}}});export{Y as _};
