import{_ as A}from"./BRCXRF_f.js";import{i as D,u as G,k as J,r as P,ab as w,L as T,l as _,c as h,w as r,q as s,o as c,e as o,p as j,h as S,t as l,d as y,a as m,g as M,$ as R,a0 as H,K as Q}from"./C3gyeM1K.js";import{_ as W,a as X,b as Y}from"./ojFwGwqF.js";import{_ as Z}from"./BoqtTPyX.js";import{_ as ee}from"./C0H2RQFE.js";import{u as te,t as oe,o as I,b as se,s as v}from"./ZoTeemxs.js";const ne={key:0,class:"flex flex-col gap-4 p-4"},le={key:1,class:"flex flex-col gap-4 px-4"},ae={class:"relative flex flex-col w-full gap-2 px-4 justify-center items-center"},ie={class:"flex flex-col w-full py-4 gap-4 justify-center items-center"},re={for:"phone",class:"text-xl font-bold"},ce={class:"flex flex-col gap-1"},ue={class:"flex items-center w-full justify-center text-gray-500 text-sm gap-2"},me={dir:"ltr"},pe={class:"flex items-center w-full justify-center gap-2 text-sm"},de={class:"text-gray-500"},fe={class:"flex w-full px-4"},$e=D({__name:"phone",emits:["close:modal"],setup(_e,{emit:N}){const p=G(),{t:d}=J(),a=P(!1),u=P([]),i=te({validationSchema:oe(I({phone:I({number:v().nullish(),iso:v().nullish(),code:v().nullish(),isValid:se()}).refine(e=>e==null?void 0:e.isValid,{message:d("error.phone-number-invalid"),path:["number"]})})),initialValues:{phone:{number:"",iso:"JO",code:"962",isValid:!1}}}),x=N,O=i.handleSubmit(async()=>p.changePhone(i.values.phone).then(()=>(p.fetchUser(),w.success(d("form.phone-changed-success")),T(()=>a.value=!0))).catch(e=>{throw e}).finally(()=>{u.value=[]})),b=async()=>p.verifyPhoneOTP(g.value).then(()=>(w.success(d("form.otp-validate-success")),T(()=>x("close:modal")))).catch(e=>{console.log("Error ",e)}),B=_(()=>{var e,t;return["+",(e=i.values.phone)==null?void 0:e.code,(t=i.values.phone)==null?void 0:t.number].join(" ")}),g=_(()=>u.value.join("")),$=_(()=>g.value.length>=6),F=()=>{$.value&&b()};return(e,t)=>{const U=A,E=M,K=X,L=W,q=Y,V=Z,z=ee;return c(),h(z,{title:s(a)?"":e.$t("form.change-phone-title"),dismissible:!0,onClose:t[3]||(t[3]=f=>x("close:modal"))},{body:r(()=>{var f,k;return[s(a)?(c(),y("div",le,[o("div",ae,[m(E,{name:"ui:phone-otp",size:"120px"}),o("div",ie,[o("label",re,l(e.$t("form.verify-phone-title")),1),m(q,{id:"pin-input",modelValue:s(u),"onUpdate:modelValue":t[1]||(t[1]=n=>Q(u)?u.value=n:null),type:"number",onComplete:F},{default:r(()=>[m(L,null,{default:r(()=>[(c(),y(R,null,H(6,(n,C)=>m(K,{key:`otp-${n}-${C}`,index:C},null,8,["index"])),64))]),_:1})]),_:1},8,["modelValue"]),o("div",ce,[o("div",ue,[o("span",null,l(e.$t("form.verify-phone-text",{phone:""})),1),o("span",me,l(s(B)),1)]),o("div",pe,[o("span",de,l(e.$t("form.change-otp-phone-title")),1),o("button",{class:"text-primary-500 hover:underline cursor-pointer",onClick:t[2]||(t[2]=()=>a.value=!1)},l(e.$t("form.change")),1)])])])])])):(c(),y("div",ne,[m(U,{title:e.$t("form.new-phone-title"),error:(k=(f=s(i).errors)==null?void 0:f.value)==null?void 0:k.phone,onUpdate:t[0]||(t[0]=n=>{s(i).setFieldValue("phone",{number:n.nationalNumber,code:n.countryCallingCode,iso:n.countryCode,isValid:n.isValid})})},null,8,["title","error"])]))]}),footer:r(()=>[o("div",fe,[s(a)?j("",!0):(c(),h(V,{key:0,class:"w-full",onClick:s(O)},{default:r(()=>[S(l(e.$t("form.change-phone-title")),1)]),_:1},8,["onClick"])),s(a)?(c(),h(V,{key:1,class:"w-full",disabled:!s($),onClick:b},{default:r(()=>[S(l(e.$t("form.verify")),1)]),_:1},8,["disabled"])):j("",!0)])]),_:1},8,["title"])}}});export{$e as _};
