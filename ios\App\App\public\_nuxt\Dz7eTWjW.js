const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as V,l as v,c as k,w as f,o as l,a as e,d as r,t as a,e as t,a0 as $,$ as u,ag as q,q as i,h as A,ah as E}from"./C3gyeM1K.js";import{Skeleton as L}from"./BaodNYLQ.js";import{_ as P}from"./DAA1LH-g.js";import{_ as S}from"./JNWsvxxg.js";import{_ as T}from"./BsHCAE3W.js";import{u as j}from"./BCJZ1_ur.js";const z=q(()=>E(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(c=>c.default||c)),D={key:1,class:"text-lg font-bold"},F={class:"flex flex-col gap-2 w-full"},H={class:"flex flex-col gap-2 flex-grow"},O={class:"flex w-28 h-full bg-gray-100 rounded-s-lg p-2 justify-center"},Q={class:"flex flex-col gap-2 w-3/4 pe-2 py-2"},R={class:"text-sm font-normal truncate-2-line h-10"},X={class:"flex gap-2 items-end font-bold text-lg"},G={class:"text-green-700"},J={dir:"ltr",class:"text-gray-400"},K={class:"text-2xl font-normal"},M={key:3,class:"grid grid-cols-2 w-full gap-2 text-base font-semibold text-gray-500"},U={class:"text-end"},W={class:"col-span-2 mb-4"},Y={class:"text-end"},Z={class:"text-xs font-normal col-span-2"},ss={key:5,class:"grid grid-cols-2 w-full gap-2 text-base font-semibold"},ts={class:"font-bold text-base text-gray-700"},es={class:"col-span-2 mb-4 text-sm text-gray-500"},is=V({__name:"products",props:{loading:{type:Boolean},order:{}},setup(c){const{priceFormat:m}=j(),_=v(()=>{var s;return(s=c.order)==null?void 0:s.orderItems}),g=v(()=>_.value.map(s=>s.quantity).reduce((s,o)=>s+o,0));return(s,o)=>{const n=L,C=T,B=z,I=S,N=P;return l(),k(N,{class:"shadow-2xl"},{default:f(()=>[e(C,null,{default:f(()=>[s.loading?(l(),k(n,{key:0,class:"w-1/2 h-5"})):(l(),r("span",D,a(s.$t("orders.details")),1))]),_:1}),e(I,null,{default:f(()=>{var h;return[t("div",F,[s.loading?(l(!0),r(u,{key:0},$(Array(4),(d,p)=>(l(),r("div",{key:p,class:"flex w-full gap-2"},[e(n,{class:"w-1/3 h-24"}),t("div",H,[e(n,{class:"w-full h-8"}),e(n,{class:"w-1/2 h-8"})])]))),128)):(l(!0),r(u,{key:1},$(i(_),d=>{var p,y,x,w,b;return l(),r("div",{key:d.productId,class:"flex rounded-lg border w-full p-px gap-2 h-24"},[t("div",O,[e(B,{class:"object-contain w-full h-full",src:(w=(x=(y=(p=d.product)==null?void 0:p.media)==null?void 0:y.cover)==null?void 0:x[0])==null?void 0:w.preview},null,8,["src"])]),t("div",Q,[t("span",R,a(d.product.name),1),t("div",X,[t("span",G,a(i(m)((b=d.price)==null?void 0:b.value)),1),t("span",J,[t("span",K,a(d==null?void 0:d.quantity),1),o[0]||(o[0]=A(" X "))])])])])}),128)),o[2]||(o[2]=t("div",{class:"flex w-full border-dashed border border-gray-100 mt-4 mb-2"},null,-1)),s.loading?(l(),r(u,{key:2},[e(n,{class:"w-full h-8"}),e(n,{class:"w-full h-8"}),e(n,{class:"w-full h-8"}),e(n,{class:"w-full h-8"})],64)):(l(),r("div",M,[t("span",null,a(s.$t("cart.payment-sub-amount-title",{item:s.$t("cart.item",{count:i(g)}),number:i(g)})),1),t("span",U,a(i(m)(s.order.subTotal.value)),1),t("span",W,a(s.$t("cart.payment-tax-inclusive")),1),t("span",null,a(s.$t("form.shipping")),1),t("span",Y,a(i(m)((h=s.order.shippingPrice)==null?void 0:h.value)),1),t("span",Z,a(s.$t("checkout.shipping-delay-hint")),1)])),o[3]||(o[3]=t("div",{class:"flex w-full border-dashed border border-gray-100 mt-4 mb-2"},null,-1)),s.loading?(l(),r(u,{key:4},[e(n,{class:"w-full h-6"}),e(n,{class:"w-full h-4"})],64)):(l(),r("div",ss,[t("span",ts,a(s.$t("cart.total-title")),1),o[1]||(o[1]=t("span",{class:"text-end"}," -------- ",-1)),t("span",es,a(s.$t("form.calculate-total-hint")),1)]))])]}),_:1})]),_:1})}}});export{is as _};
