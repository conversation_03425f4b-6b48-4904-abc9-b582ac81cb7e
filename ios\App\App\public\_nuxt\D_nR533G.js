import{_ as ee,a as te,u as oe,b as ne,c as ae,P as se}from"./DYRsuAWe.js";import{r as p,m as re,i as C,T as N,c as h,o as M,w as y,s as B,q as a,v as K,x as O,aW as ie,n as ue,ar as le,a as D,a1 as X,L as S}from"./C3gyeM1K.js";import{a as de,b as ce,F as fe,L as pe,f as me,c as $,S as ye,I as ge}from"./B12VHTgT.js";import{u as ve}from"./Bqx9KQ63.js";import{_ as be,u as Pe,a as we,b as Ce}from"./DDFqsyHi.js";import{d as z,a as he}from"./BBuxlZld.js";import{y as Me}from"./CRC5xmnh.js";import{u as Be}from"./BRIKaOVL.js";import{c as _,u as A}from"./C2RgRyPS.js";import{_ as Ie}from"./CB0C_qKx.js";import{u as Se}from"./B5rxJs06.js";import{g as Te}from"./FUKBWQwh.js";import{P as De}from"./ClItKS3j.js";function ke(){const r=p(!1);return re(()=>{z("keydown",()=>{r.value=!0},{capture:!0,passive:!0}),z(["pointerdown","pointermove"],()=>{r.value=!1},{capture:!0,passive:!0})}),r}const Ee=Me(ke),[Re,Fe]=_(["MenuRoot","MenuSub"],"MenuContext"),[j,$e]=_("MenuRoot"),Je=C({__name:"MenuRoot",props:{open:{type:Boolean,default:!1},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(r,{emit:d}){const i=r,u=d,{modal:v,dir:l}=N(i),b=Be(l),g=he(i,"open",u),o=p(),c=Ee();return Fe({open:g,onOpenChange:s=>{g.value=s},content:o,onContentChange:s=>{o.value=s}}),$e({onClose:()=>{g.value=!1},isUsingKeyboardRef:c,dir:b,modal:v}),(s,f)=>(M(),h(a(ee),null,{default:y(()=>[B(s.$slots,"default")]),_:3}))}}),Qe=C({__name:"MenuAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(r){const d=r;return(i,u)=>(M(),h(a(te),K(O(d)),{default:y(()=>[B(i.$slots,"default")]),_:3},16))}}),Ke=C({__name:"MenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(r){const d=r;return(i,u)=>(M(),h(a(be),K(O(d)),{default:y(()=>[B(i.$slots,"default")]),_:3},16))}}),[W,Oe]=_("MenuContent"),Ze=C({__name:"MenuContentImpl",props:ie({loop:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},disableOutsideScroll:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...se}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(r,{emit:d}){const i=r,u=d,v=Re(),l=j(),{trapFocus:b,disableOutsidePointerEvents:g,loop:o}=N(i);oe(),Pe(g.value);const c=p(""),s=p(0),f=p(0),m=p(null),P=p("right"),k=p(0),E=p(null),L=p(),{forwardRef:Y,currentElement:I}=A(),{handleTypeaheadSearch:q}=ne();ue(I,e=>{v.onContentChange(e)}),le(()=>{window.clearTimeout(s.value)});function R(e){var n,w;return P.value===((n=m.value)==null?void 0:n.side)&&ce(e,(w=m.value)==null?void 0:w.area)}async function H(e){var t;u("openAutoFocus",e),!e.defaultPrevented&&(e.preventDefault(),(t=I.value)==null||t.focus({preventScroll:!0}))}function J(e){var V;if(e.defaultPrevented)return;const n=e.target.closest("[data-reka-menu-content]")===e.currentTarget,w=e.ctrlKey||e.altKey||e.metaKey,F=e.key.length===1,T=Se(e,Te(),I.value,{loop:o.value,arrowKeyOptions:"vertical",dir:l==null?void 0:l.dir.value,focus:!0,attributeName:"[data-reka-collection-item]:not([data-disabled])"});if(T)return T==null?void 0:T.focus();if(e.code==="Space")return;const U=((V=L.value)==null?void 0:V.getItems())??[];if(n&&(e.key==="Tab"&&e.preventDefault(),!w&&F&&q(e.key,U)),e.target!==I.value||!fe.includes(e.key))return;e.preventDefault();const G=[...U.map(x=>x.ref)];pe.includes(e.key)&&G.reverse(),me(G)}function Q(e){var t,n;(n=(t=e==null?void 0:e.currentTarget)==null?void 0:t.contains)!=null&&n.call(t,e.target)||(window.clearTimeout(s.value),c.value="")}function Z(e){var w;if(!$(e))return;const t=e.target,n=k.value!==e.clientX;if((w=e==null?void 0:e.currentTarget)!=null&&w.contains(t)&&n){const F=e.clientX>k.value?"right":"left";P.value=F,k.value=e.clientX}}return Oe({onItemEnter:e=>!!R(e),onItemLeave:e=>{var t;R(e)||((t=I.value)==null||t.focus(),E.value=null)},onTriggerLeave:e=>!!R(e),searchRef:c,pointerGraceTimerRef:f,onPointerGraceIntentChange:e=>{m.value=e}}),(e,t)=>(M(),h(a(Ce),{"as-child":"",trapped:a(b),onMountAutoFocus:H,onUnmountAutoFocus:t[7]||(t[7]=n=>u("closeAutoFocus",n))},{default:y(()=>[D(a(we),{"as-child":"","disable-outside-pointer-events":a(g),onEscapeKeyDown:t[2]||(t[2]=n=>u("escapeKeyDown",n)),onPointerDownOutside:t[3]||(t[3]=n=>u("pointerDownOutside",n)),onFocusOutside:t[4]||(t[4]=n=>u("focusOutside",n)),onInteractOutside:t[5]||(t[5]=n=>u("interactOutside",n)),onDismiss:t[6]||(t[6]=n=>u("dismiss"))},{default:y(()=>[D(a(Ie),{ref_key:"rovingFocusGroupRef",ref:L,"current-tab-stop-id":E.value,"onUpdate:currentTabStopId":t[0]||(t[0]=n=>E.value=n),"as-child":"",orientation:"vertical",dir:a(l).dir.value,loop:a(o),onEntryFocus:t[1]||(t[1]=n=>{u("entryFocus",n),a(l).isUsingKeyboardRef.value||n.preventDefault()})},{default:y(()=>[D(a(ae),{ref:a(Y),role:"menu",as:e.as,"as-child":e.asChild,"aria-orientation":"vertical","data-reka-menu-content":"","data-state":a(de)(a(v).open.value),dir:a(l).dir.value,side:e.side,"side-offset":e.sideOffset,align:e.align,"align-offset":e.alignOffset,"avoid-collisions":e.avoidCollisions,"collision-boundary":e.collisionBoundary,"collision-padding":e.collisionPadding,"arrow-padding":e.arrowPadding,"prioritize-position":e.prioritizePosition,"position-strategy":e.positionStrategy,"update-position-strategy":e.updatePositionStrategy,sticky:e.sticky,"hide-when-detached":e.hideWhenDetached,reference:e.reference,onKeydown:J,onBlur:Q,onPointermove:Z},{default:y(()=>[B(e.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","position-strategy","update-position-strategy","sticky","hide-when-detached","reference"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),_e=C({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(r){const d=r,i=W(),{forwardRef:u}=A(),{CollectionItem:v}=ve(),l=p(!1);async function b(o){if(!o.defaultPrevented&&$(o)){if(d.disabled)i.onItemLeave(o);else if(!i.onItemEnter(o)){const s=o.currentTarget;s==null||s.focus({preventScroll:!0})}}}async function g(o){await S(),!o.defaultPrevented&&$(o)&&i.onItemLeave(o)}return(o,c)=>(M(),h(a(v),{value:{textValue:o.textValue}},{default:y(()=>[D(a(De),X({ref:a(u),role:"menuitem",tabindex:"-1"},o.$attrs,{as:o.as,"as-child":o.asChild,"aria-disabled":o.disabled||void 0,"data-disabled":o.disabled?"":void 0,"data-highlighted":l.value?"":void 0,onPointermove:b,onPointerleave:g,onFocus:c[0]||(c[0]=async s=>{await S(),!(s.defaultPrevented||o.disabled)&&(l.value=!0)}),onBlur:c[1]||(c[1]=async s=>{await S(),!s.defaultPrevented&&(l.value=!1)})}),{default:y(()=>[B(o.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),xe=C({__name:"MenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(r,{emit:d}){const i=r,u=d,{forwardRef:v,currentElement:l}=A(),b=j(),g=W(),o=p(!1);async function c(){const s=l.value;if(!i.disabled&&s){const f=new CustomEvent(ge,{bubbles:!0,cancelable:!0});u("select",f),await S(),f.defaultPrevented?o.value=!1:b.onClose()}}return(s,f)=>(M(),h(_e,X(i,{ref:a(v),onClick:c,onPointerdown:f[0]||(f[0]=()=>{o.value=!0}),onPointerup:f[1]||(f[1]=async m=>{var P;await S(),!m.defaultPrevented&&(o.value||(P=m.currentTarget)==null||P.click())}),onKeydown:f[2]||(f[2]=async m=>{const P=a(g).searchRef.value!=="";s.disabled||P&&m.key===" "||a(ye).includes(m.key)&&(m.currentTarget.click(),m.preventDefault())})}),{default:y(()=>[B(s.$slots,"default")]),_:3},16))}}),et=C({__name:"DropdownMenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(r){const d=r;return(i,u)=>(M(),h(a(Ke),K(O(d)),{default:y(()=>[B(i.$slots,"default")]),_:3},16))}});export{xe as _,Ze as a,j as b,Je as c,Qe as d,et as e,W as f,_e as g,Re as i,Fe as p};
