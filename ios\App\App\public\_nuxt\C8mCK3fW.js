import{Skeleton as D}from"./BaodNYLQ.js";import{i as E,j as L,l,X as R,k as q,r as F,d as i,e,c as x,p as v,a as o,w as p,q as t,Y as H,o as r,$ as y,g as T,W as f,t as c,h as W,_ as X}from"./C3gyeM1K.js";import Y from"./CuUhnQhf.js";import G from"./D-ucjfQv.js";import{_ as J}from"./BoqtTPyX.js";import{_ as K}from"./DAA1LH-g.js";import{_ as M}from"./BsHCAE3W.js";import{_ as P}from"./USAFFp3X.js";import{_ as Q}from"./B-7j4s9i.js";import"./jAU0Cazi.js";import"./BispfOyS.js";import"./D1vxl8R8.js";import"./D8XAHdeJ.js";import"./ClItKS3j.js";import"./JNWsvxxg.js";import"./BCJZ1_ur.js";import"./CRC5xmnh.js";import"./C0H2RQFE.js";import"./D3IGe8e3.js";import"./DEEq2QX3.js";import"./DDFqsyHi.js";import"./BBuxlZld.js";import"./C2RgRyPS.js";import"./FUKBWQwh.js";import"./BK9Yhb3N.js";import"./CY42fsWK.js";import"./B12VHTgT.js";import"./DSkQD3L1.js";import"./ytjl1pT4.js";import"./B9XwVa_7.js";const U={class:"flex w-full min-h-screen overflow-auto bg-body"},Z={class:"flex w-full flex-col gap-4 h-full overflow-y-auto"},ee={class:"flex w-full gap-4 justify-between items-center"},te={class:"flex items-center gap-4"},oe={key:0,class:"relative flex w-full bg-sky-50 p-4 rounded-md gap-2 items-center justify-between font-bold z-0"},se={class:"icon-box flex p-4 bg-gray rounded-full items-center justify-center bg-gray-200"},re={class:"icon-box flex p-4 bg-gray rounded-full items-center justify-center bg-gray-200"},ae={class:"icon-box flex p-4 bg-gray rounded-full items-center justify-center bg-gray-200"},ne=E({__name:"tracking",async setup(le){let m,b;const h=L(),$=l(()=>{var s;return(s=h.params)==null?void 0:s.orderId}),{data:C,error:k,status:j}=([m,b]=R(()=>H(`/my/orders/${$.value}`)),m=await m,b(),m);k.value&&console.log("Error on fetching order ",k.value);const a=l(()=>C.value),_=l(()=>j.value!=="success"),{locale:z}=q(),I=l(()=>z.value==="ar"),g=F(!1),w=l(()=>{var s;return((s=a.value)==null?void 0:s.status)==="draft"});return(s,n)=>{const d=D,u=T,B=Y,N=G,S=J,V=M,O=K;return r(),i("div",U,[e("div",Z,[o(O,null,{default:p(()=>[o(V,{class:"gap-6"},{default:p(()=>[e("div",ee,[t(_)?(r(),x(d,{key:0,class:"h-6 w-1/2"})):(r(),i(y,{key:1},[e("div",te,[o(B,{class:"flex flex-row gap-2 items-center text-lg font-bold",to:"/my/orders"},{default:p(()=>[o(u,{name:"lucide:chevron-right",class:f(["text-2xl",{"rotate-180":!t(I)}])},null,8,["class"]),e("span",null,c(s.$t("orders.your-order-number",{number:t(a).orderId})),1)]),_:1}),o(N,{status:t(a).status},null,8,["status"])]),t(w)?v("",!0):(r(),x(S,{key:0,variant:"danger",size:"sm",onClick:n[0]||(n[0]=A=>g.value=!0)},{default:p(()=>[W(c(s.$t("orders.cancel-order")),1)]),_:1}))],64))]),t(w)?v("",!0):(r(),i("div",oe,[t(_)?(r(),i(y,{key:0},[o(d,{class:"h-10 w-1/3"}),o(d,{class:"h-10 w-1/3"}),o(d,{class:"h-10 w-1/3"})],64)):(r(),i(y,{key:1},[e("div",{class:f(["flex flex-col px-8 bg-sky-50 items-center justify-center gap-2",{active:t(a).status==="received"}])},[e("div",se,[o(u,{name:"ui:order-in-progress",size:"30px"})]),e("span",null,c(s.$t("orders.order-executing")),1)],2),e("div",{class:f(["flex flex-col px-8 bg-sky-50 items-center justify-center gap-2",{active:t(a).status==="active"}])},[e("div",re,[o(u,{name:"ui:order-loading",size:"30px"})]),e("span",null,c(s.$t("orders.order-on-way")),1)],2),e("div",{class:f(["flex flex-col px-8 bg-sky-50 items-center justify-center gap-2",{active:t(a).status==="completed"}])},[e("div",ae,[o(u,{name:"ui:order-loading",size:"30px"})]),e("span",null,c(s.$t("orders.order-delivered")),1)],2),n[2]||(n[2]=e("div",{class:"flex max-w-3xl h-[2px] bg-gray-300 absolute top-auto bottom-auto inset-0 left-0 -z-10 mx-auto"},null,-1))],64))]))]),_:1})]),_:1}),o(P,{loading:t(_),order:t(a)},null,8,["loading","order"])]),t(g)?(r(),x(Q,{key:0,"onClose:modal":n[1]||(n[1]=A=>g.value=!1)})):v("",!0)])}}}),Re=X(ne,[["__scopeId","data-v-53704ff1"]]);export{Re as default};
