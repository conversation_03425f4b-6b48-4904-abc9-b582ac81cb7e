const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DerKnm_3.js","./C3gyeM1K.js","./entry.CGfGZREn.css"])))=>i.map(i=>d[i]);
import{i as D,r as _,l as R,d as n,W as t,e as p,a as s,p as h,q as a,w as i,Z as x,o as l,g as W,$ as y,a0 as S,c as z,aq as X,ag as Z,t as B,ah as G}from"./C3gyeM1K.js";import{_ as H}from"./BoqtTPyX.js";import{Skeleton as J}from"./BaodNYLQ.js";import{_ as j,a as E,b as L}from"./CiqmSqHP.js";import{a as N,_ as T}from"./D3J2c5MI.js";import{r as K}from"./CRC5xmnh.js";const Q=Z(()=>G(()=>import("./DerKnm_3.js"),__vite__mapDeps([0,1,2]),import.meta.url).then(b=>b.default||b)),U={class:"z-0 relative flex w-full aspect-auto justify-center items-center bg-gray-100 rounded-lg"},Y={key:0,class:"absolute start-4 top-4 flex flex-col gap-2 z-10"},ee={key:0,class:"absolute top-0 left-0 z-10 w-full h-full bg-gray-100"},se={class:"fixed top-0 end-0 m-5 bg-black rounded-lg px-1"},le={class:"text-md text-white font-semibold"},ae={key:2,class:"flex absolute start-2 bottom-0 w-full"},te=["data-index","onClick"],M=.5,P=2,me=D({__name:"slider",props:{images:{default:()=>[]},loading:{type:Boolean},isFullScreen:{type:Boolean,default:!1}},emits:["set:preview"],setup(b,{emit:V}){const C=V,u=_(),w=_(),m=_(0),r=_(1),k=()=>{r.value=1,!(!u.value||!w.value)&&(m.value=u.value.selectedScrollSnap(),w.value.scrollTo(u.value.selectedScrollSnap()))},F=e=>{!u.value||!w.value||u.value.scrollTo(e)};K(u,e=>{e&&(k(),e.on("select",k),e.on("reInit",k))});const q=()=>{r.value<P&&(r.value+=.1)},O=()=>{r.value>M&&(r.value-=.1)},I=R(()=>!!b.images.length);return(e,c)=>{const v=W,g=H,$=J,A=Q;return l(),n("div",{class:t(["flex max-w-4xl w-full flex-col items-start mx-auto select-none",{"sm:h-svh py-12 justify-center":e.isFullScreen}])},[p("div",U,[!e.isFullScreen&&a(I)&&!e.loading?(l(),n("div",Y,[s(g,{variant:"icon",class:"bg-white p-0 shadow hover:shadow-lg"},{default:i(()=>[s(v,{name:"ui:share",class:"text-gray-800",size:"18px"})]),_:1}),s(g,{variant:"icon",class:"bg-white p-0 shadow hover:shadow-lg",onClick:c[0]||(c[0]=x(()=>C("set:preview",!0),["prevent"]))},{default:i(()=>[s(v,{name:"ui:expand",class:"text-gray-800",size:"18px"})]),_:1})])):h("",!0),s(a(L),{class:t(["relative max-w-full py-10 rounded-lg overflow-hidden min-h-96 w-full z-0",{"py-0 min-h-full":e.isFullScreen,"flex items-center":!e.isFullScreen}]),onInitApi:c[2]||(c[2]=o=>u.value=o)},{default:i(()=>{var o;return[e.loading?(l(),n("div",ee,[s($,{class:"w-full h-full min-w-full"})])):h("",!0),s(a(j),{class:"!m-0"},{default:i(()=>[(l(!0),n(y,null,S(e.images,(d,f)=>(l(),z(a(E),{key:`big-image-${d.id}`,class:t(["p-0 bg-gray-100",{"z-10":f===m.value}])},{default:i(()=>[p("div",{class:t(["flex items-center justify-center rounded-lg",{"h-full":e.isFullScreen}]),style:X({scale:r.value})},[s(A,{src:d.preview,alt:"Product Image",class:t(["object-contain w-full max-w-sm relative",{"min-h-full min-w-[99%] max-h-[700px]":e.isFullScreen}]),sizes:"xs:100vw md:1000px",width:"400",height:"400",format:"webp",fit:"contain",quality:"100"},null,8,["src","class"])],6)]),_:2},1032,["class"]))),128))]),_:1}),e.isFullScreen?(l(),n(y,{key:1},[s(a(N),{class:"mx-5 fixed"}),s(a(T),{class:"mx-5 fixed"}),s(g,{variant:"icon",class:"fixed top-0 start-0 ms-5 mt-5 size-6 bg-black",onClick:c[1]||(c[1]=x(()=>C("set:preview",!1),["prevent"]))},{default:i(()=>[s(v,{name:"lucide:circle-x",class:"text-white",size:"18px"})]),_:1}),s(g,{variant:"icon",class:"fixed top-0 start-20 mt-5 size-6 group max-w-20 p-0 bg-black",disabled:r.value>=P,onClick:x(q,["prevent"])},{default:i(()=>[s(v,{name:"lucide:zoom-in",class:"text-white group-disabled:opacity-75",size:"20px"})]),_:1},8,["disabled"]),s(g,{variant:"icon",class:"fixed top-0 start-28 mt-5 size-6 group max-w-20 p-0 bg-black",disabled:r.value<=M,onClick:x(O,["prevent"])},{default:i(()=>[s(v,{name:"lucide:zoom-out",class:"text-white group-disabled:opacity-75",size:"20px"})]),_:1},8,["disabled"]),p("div",se,[p("span",le,B(1+m.value)+" / "+B((o=e.images)==null?void 0:o.length),1)])],64)):h("",!0),!e.isFullScreen&&a(I)?(l(),n("div",ae,[e.loading?h("",!0):(l(),n("div",{key:0,class:t(["flex relative w-[4.5rem] h-14 px-2",{"max-sm:hidden":!e.isFullScreen}])},[s(a(N),{class:"top-6"}),s(a(T),{class:"top-6"})],2)),p("div",{class:t(["w-full gap-2 py-6 justify-center items-center sm:hidden",{"xs:flex":!e.isFullScreen}])},[(l(!0),n(y,null,S(e.images,(d,f)=>(l(),n("div",{key:`scroll-image-${d.id}`,class:t(["flex p-1 rounded-full bg-gray-300 h-1 transition ease-in-out duration-200",{"bg-primary-600 px-4":f===m.value}]),"data-index":f,onClick:ie=>F(f)},null,10,te))),128))],2)])):h("",!0)]}),_:1},8,["class"])]),s(a(L),{class:t(["relative w-full my-6",{"max-sm:hidden":!e.isFullScreen}]),onInitApi:c[3]||(c[3]=o=>w.value=o)},{default:i(()=>[s(a(j),{class:"mr-4 flex gap-4"},{default:i(()=>[e.loading?(l(),z($,{key:0,class:"basis-1/4 h-20 bg-gray-200"})):(l(!0),n(y,{key:1},S(e.images,(o,d)=>(l(),z(a(E),{key:o.id,dir:"ltr",class:t(["basis-1/5 cursor-pointer",{"sm:basis-1/12 sm:mx-2 xs:basis-1/6":e.isFullScreen,"max-md:basis-1/4 max-md:me-2":!e.isFullScreen}]),selected:m.value,onClick:f=>F(d)},{default:i(()=>[p("div",{class:t(["flex border border-gray-200 rounded-lg p-1 bg-white xs:h-14 xs:w-14 sm:w-20 sm:h-20",{"border-2":e.isFullScreen,"border-primary-600 !bg-gray-100":d===m.value}])},[s(A,{src:o.preview,alt:"Carosale Product Image",class:"object-contain",sizes:"xs:100vw md:100px",width:"100",height:"100",format:"webp",fit:"contain"},null,8,["src"])],2)]),_:2},1032,["selected","class","onClick"]))),128))]),_:1})]),_:1},8,["class"])],2)}}});export{me as _};
