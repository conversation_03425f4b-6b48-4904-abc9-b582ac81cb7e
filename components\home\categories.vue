<script setup lang="ts">
import { ref } from 'vue'
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselPrevious,
	CarouselNext, type CarouselApi,
} from '@/components/ui/carousel'
import { useCategoriesStore } from '~/store/categoriesStore'
import { Skeleton } from '~/components/ui/skeleton'

const categoryStore = useCategoriesStore()
const categories = computed(() => categoryStore.slider || [])
const fetching = computed(() => !!categoryStore.fetching)

const sliderMainApi = ref<CarouselApi>()
const selectedIndex = ref(0)

const categoriesList = computed(() => {
	const result = []
	const items = categories.value

	for (let i = 0; i < items.length; i += 2) {
		result.push(items.slice(i, i + 2))
	}

	return result
})

/**
 * On a small image clicked
 * @param index
 */
const onThumbClick = (index: number) => {
	if (!sliderMainApi.value) {
		return
	}

	sliderMainApi.value.scrollTo(index)
	selectedIndex.value = sliderMainApi.value.selectedScrollSnap()
}

/**
 * Watch on the slider
 */
const handleOnChange = (mainApi: CarouselApi) => {
	selectedIndex.value = mainApi.selectedScrollSnap()
}

onMounted(() => {
	if (sliderMainApi.value) {
		sliderMainApi.value.on('select', handleOnChange)
	}
})
</script>

<template>
	<div class="col-span-3 w-full">
		<Carousel
			:opts="{ align: 'center', slidesToScroll: 'auto' }"
			class="w-full rounded-lg py-4 bg-white max-sm:hidden"
		>
			<template #default="{ canScrollNext, canScrollPrev }">
				<CarouselContent class="px-4">
					<template v-if="fetching || !categories?.length">
						<CarouselItem
							v-for="index in Array(10)"
							:key="`card-${index}`"
							class="basis-1/10 sm:basis-1/8 md:basis-1/10 lg:basis-1/12 xl:basis-1/14"
						>
							<div class="flex flex-col items-center gap-4 w-full h-full">
								<Skeleton class="w-20 h-20" />
								<Skeleton class="w-20 h-5" />
							</div>
						</CarouselItem>
					</template>
					<template v-else>
						<CarouselItem
							v-for="category in categories"
							:key="category.name"
							class="basis-1/10 sm:basis-1/8 md:basis-1/10 lg:basis-1/12 xl:basis-1/14"
							:as-child="true"
						>
							<div>
								<NuxtLinkLocale
									:title="category.name"
									:to="`/category/${category?.slug}`"
									class="flex flex-col items-center gap-4 w-full h-full"
								>
									<div class="flex w-20 h-20 shadow rounded-lg bg-gray-100">
										<NuxtImg
											:src="category.media"
											:alt="category.name"
											:title="category.name"
											provider="backend"
											class="aspect-square object-contain"
											width="80"
											height="80"
											format="webp"
											quality="90"
											fit="contain"
											loading="eager"
										/>
									</div>
									<h3 class="text-sm font-semibold text-gray-500 text-nowrap">
										{{ category.name }}
									</h3>
								</NuxtLinkLocale>
							</div>
						</CarouselItem>
					</template>
				</CarouselContent>
				<CarouselPrevious v-if="canScrollPrev" />
				<CarouselNext v-if="canScrollNext" />
			</template>
		</Carousel>

		<Carousel
			:opts="{ align: 'center', slidesToScroll: 'auto' }"
			class="w-full rounded-lg py-4 bg-white sm:hidden"
			@init-api="(val) => sliderMainApi = val"
			@slide="handleOnChange"
		>
			<CarouselContent class="gap-x-0 px-0 mb-2">
				<template v-if="fetching || !categoriesList?.length">
					<CarouselItem
						v-for="index in Array(10)"
						:key="`card-${index}`"
						class="basis-3/12 !ps-0"
					>
						<div class="flex flex-col items-center gap-4 w-full h-full">
							<div class="flex flex-col items-center gap-2 w-full h-full">
								<Skeleton class="w-20 h-16" />
								<Skeleton class="w-20 h-5" />
							</div>
							<div class="flex flex-col items-center gap-2 w-full h-full">
								<Skeleton class="w-20 h-16" />
								<Skeleton class="w-20 h-5" />
							</div>
						</div>
					</CarouselItem>
				</template>
				<template v-else>
					<CarouselItem
						v-for="(items, index) in categoriesList"
						:key="`mobile-category-${index}`"
						class="basis-3/12 !ps-0"
						:as-child="true"
						:selected="selectedIndex"
					>
						<div class="flex flex-col items-center gap-4 w-full h-full">
							<template
								v-for="(category, cIndex) in items"
								:key="`mobile-category-items-${cIndex}`"
							>
								<NuxtLinkLocale
									:title="category.name"
									:to="`/category/${category?.slug}`"
									class="flex flex-col items-center justify-between gap-4 w-full h-full max-h-28"
								>
									<div class="flex w-16 h-16 shadow rounded-lg bg-gray-100">
										<NuxtImg
											:src="category.media"
											:alt="category.name"
											:title="category.name"
											provider="backend"
											class="aspect-square object-contain"
											width="60"
											height="60"
											format="webp"
											quality="90"
											fit="contain"
											loading="eager"
										/>
									</div>
									<h3 class="text-xs font-semibold text-gray-500 max-w-20 text-center line-clamp-2 h-8">
										{{ category.name }}
									</h3>
								</NuxtLinkLocale>
							</template>
						</div>
					</CarouselItem>
				</template>
			</CarouselContent>
			<div
				v-if="!fetching"
				class="flex w-full gap-2 justify-center items-center sm:hidden"
			>
				<div
					v-for="(_, iIndex) in Array(2)"
					:key="`scroll-thumb-image-${iIndex}`"
					class="flex p-1 rounded-full bg-gray-300 h-1 transition ease-in-out duration-200"
					:data-index="iIndex"
					:class="{ 'bg-primary-600 px-4': iIndex === selectedIndex }"
					@click="onThumbClick(iIndex)"
				/>
			</div>
		</Carousel>
	</div>
</template>
